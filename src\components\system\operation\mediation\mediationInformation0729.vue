<script lang="ts" setup>
import { onMounted, ref, type Ref, watch, computed } from 'vue'
import CustomButton from '@/components/common/CustomButton.vue';
import CustomInput from '@/components/common/CustomInput.vue';
import EditMediationInformationDialog from '../../dialogs/EditMediationInformationDialog.vue';
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue';
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import type { Mediation, MediationParams, EditMediationParams, MediationField } from '../../auth/type';
import { getDataImportList, getMediationCase, getMediationCaseDetail, editMediationCase, deleteMediationCase, updateAttachmentsMediation } from '@/axios/system'
import { ElMessage } from 'element-plus';

// Tab标签页类型
type TabType = 'asset' | 'mediation'

// 当前激活的Tab
const activeTab = ref<TabType>('asset')

// 选中的调解信息行数据
const selectRow: Ref<Mediation> = ref({
  id: '',
  asset_package_name: '',
  mediation_config: [],
})

// 分页和搜索
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const loading = ref(false)

// 列表数据
const assetList = ref<any[]>([])
const mediationList = ref<any[]>([])

// 对话框控制
const showEdit = ref(false)
const showDelete = ref(false)

// 编辑弹窗控制
const showEditDialog = ref(false)
const editingRow = ref<any>(null)
const editMode = ref<'asset' | 'mediation'>('asset')

// 计算属性：搜索框提示文字
const searchPlaceholder = computed(() => {
  return activeTab.value === 'asset' ? '资产包名称' : '调解案件号'
})

// 计算属性：当前列表数据
const currentList = computed(() => {
  return activeTab.value === 'asset' ? assetList.value : mediationList.value
})

// 监听Tab切换，重新加载数据
watch(activeTab, (newTab) => {
  page.value = 1
  if (newTab === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
})

// 监听搜索变化
watch(search, () => {
  page.value = 1
  if (activeTab.value === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
})

// 获取案件状态选项
/* async function getStatusOptions() {
  try {
    const { data } = await getStatusChoicesOptions()
    const { state, msg } = data
    if (state === 'success') {
      statusOptions.value = data.data.map((item: any) => ({
        label: item.label,
        value: item.value
      }))
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error('获取状态选项失败:', error)
    ElMessage.error('获取状态选项失败')
  }
} */
// 页面初始化
onMounted(() => {
  loadAssetList()
})

// 加载资产包列表
async function loadAssetList() {
  loading.value = true 
  const params = {
    page: page.value,
    page_size: page_size,
    search: search.value,
    package_status: 'available'
  }
  const response = await getDataImportList(params)
  const { state, msg, data } = response.data
  if (state === 'success') {
    assetList.value = data.results || []
    total.value = data.count || 0
  } else {
    ElMessage.error(msg || '获取资产包列表失败')
  } 
  loading.value = false 
}

// 加载调解案件列表
async function loadMediationList() {
  loading.value = true
  const params: MediationParams = {
    page: page.value,
    page_size: page_size,
    search: search.value,
  }

  const { data } = await getMediationCase(params)
  const { state, msg } = data

  if (state === 'success') {
    const { results, count } = data.data
    mediationList.value = results
    total.value = count
  } else {
    ElMessage.error(msg)
  } 
  loading.value = false 
}

// 重置到第一页并搜索
function handleSearch() {
  page.value = 1
  // 判断tab获取不一样列表信息
  if (activeTab.value === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
}

// 分页改变
function pageChanged(p: number) {
  page.value = p
  if (activeTab.value === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
}

// 打开编辑弹窗
function openEditDialog(row: any, mode: 'asset' | 'mediation') {
  editingRow.value = row
  editMode.value = mode
  showEditDialog.value = true
}

// 关闭编辑弹窗
function closeEditDialog() {
  showEditDialog.value = false
  editingRow.value = null
}

// 处理编辑确认
async function handleEditConfirm(formData: FormData) {
  if (!editingRow.value || !editingRow.value.id) {
    ElMessage.error('缺少必要参数')
    return
  }

  try {
    let response

    // 根据当前tab选择不同的保存接口
    if (activeTab.value === 'asset') {
      // 资产包tab：调用updateAttachmentsMediation接口
      response = await updateAttachmentsMediation(formData, Number(editingRow.value.id))
    } else {
      // 调解案件tab：调用editMediationCase接口
      response = await editMediationCase(formData, Number(editingRow.value.id))
    }

    const { data } = response
    const { state, msg } = data

    if (state === 'success') {
      ElMessage.success(msg || '编辑成功')
      closeEditDialog()
      // 重新加载当前列表
      if (activeTab.value === 'asset') {
        loadAssetList()
      } else {
        loadMediationList()
      }
    } else {
      ElMessage.error(msg || '编辑失败')
    }
  } catch (error) {
    console.error('编辑失败:', error)
    ElMessage.error('编辑失败，请重试')
  }
}


// 打开编辑调解信息对话框
/* async function openEditPlanDialog(row: Mediation) {
  const { data } = await getMediationCaseDetail(Number(row.id))
  const { state, msg } = data
  if (state === 'success') {
    selectRow.value = data.data
  } else {
    ElMessage.error(msg)
  }
  showEdit.value = true
}

// 提交编辑调解计划
async function submitEditPlan(params: EditMediationParams) {
  if (!selectRow.value.id) {
    ElMessage.error('缺少必要参数')
    return
  }
  const { data } = await editMediationCase(params, Number(selectRow.value.id))
  const { state, msg } = data

  if (state === 'success') {
    ElMessage.success(msg)
    showEdit.value = false
    // 重新加载当前列表
    if (activeTab.value === 'asset') {
      loadAssetList()
    } else {
      loadMediationList()
    }
  } else {
    ElMessage.error(msg || '编辑失败')
  }
} */

// 打开删除确认对话框
/* function openEnsureDeleteDialog(row: Mediation) {
  selectRow.value = { ...row }
  showDelete.value = true
} */

// 删除调解计划
/* async function deletePlanRow() {
  if (!selectRow.value.id) {
    ElMessage.error('缺少必要参数')
    return
  }

  const { data } = await deleteMediationCase(Number(selectRow.value.id))
  const { state, msg } = data

  if (state === 'success') {
    ElMessage.success(msg)
    showDelete.value = false
    // 重新加载当前列表
    if (activeTab.value === 'asset') {
      loadAssetList()
    } else {
      loadMediationList()
    }
  } else {
    ElMessage.error(msg || '删除失败')
  }
} */

// 关闭编辑对话框（旧版本）
/* function closeEditDialogOld() {
  showEdit.value = false
  selectRow.value = {
    id: '',
    asset_package_name: '',
    mediation_config: [],
  }
} */

// 获取案件状态标签
/* function getCaseStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'mediating': '调解中',
    'completed': '已完成',
    'closed': '已关闭'
  }
  return statusMap[status] || status
} 

// 获取案件状态类型（用于el-tag的type属性）
 function getCaseStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'mediating': 'primary',
    'completed': 'success',
    'closed': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取公证状态标签
function getNotarizationLabel(status: string): string {
  const statusMap: Record<string, string> = {
    'not_required': '无需公证',
    'pending': '待公证',
    'in_progress': '公证中',
    'completed': '已公证',
    'rejected': '公证失败'
  }
  return statusMap[status] || '未知'
}

// 获取公证状态类型
function getNotarizationType(status: string): string {
  const typeMap: Record<string, string> = {
    'not_required': 'info',
    'pending': 'warning',
    'in_progress': 'primary',
    'completed': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}
 */


// 格式化字段类型显示
/* function getFieldTypeIcon(type: FieldType): string {
  const iconMap = {
    [FieldType.TEXTAREA]: '📝',
    [FieldType.TEXTAREA]: '📄',
    [FieldType.DATE]: '📅',
    [FieldType.AMOUNT]: '💰',
    [FieldType.FILE]: '📎'
  }
  return iconMap[type] || '📝'
} */

// 预览JSON数据
/* function previewJsonData(row: Mediation) {
  try {
    const jsonData = JSON.parse(row.jsonData)
    ElMessage({
      message: `调解信息JSON预览：\n${JSON.stringify(jsonData, null, 2)}`,
      type: 'info',
      duration: 8000,
      showClose: true
    })
  } catch (error) {
    ElMessage.error('JSON数据格式错误')
  }
} */

// 删除重复的onMounted，已在前面定义
</script>

<template>
  <div class="mediation-management">
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput
            v-model="search"
            :placeholder="searchPlaceholder"
						@keydown.enter="handleSearch"
            @click="handleSearch">
          </CustomInput>
        </div>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="mediation-tabs">
      <el-tab-pane label="资产包" name="asset">
        <el-table
          :data="currentList"
          v-loading="loading"
          border
          :cell-style="cellStyle"
          :header-cell-style="headerCellStyle"
          class="plan-table">
          <el-table-column label="序号" type="index" width="80" align="center" />
          <el-table-column label="资产包名称" prop="package_name" min-width="150" align="center" />
          <el-table-column label="债权人" prop="creditor_name" min-width="120" align="center" />
          <!-- <el-table-column label="调解信息配置" align="center" min-width="180">
            <template v-slot="{ row }">
              <div class="config-preview">
                <span v-if="row.field_mappings_detail && row.field_mappings_detail.length > 0">
                  {{ row.field_mappings_detail.length }}个字段
                </span>
                <span v-else>未配置</span>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="调解信息配置" align="center" min-width="260">
            <template v-slot="{row}">
              <div class="fields-preview">
                <div class="field-types">
                  <!-- 修复TypeError: 添加防御性检查确保field_mappings_detail存在且为数组 -->
                  <span
                    v-for="field in (row.field_mappings_detail && Array.isArray(row.field_mappings_detail) ? row.field_mappings_detail.slice(0, 6) : [])"
                    :key="field.id || field.original_field_name || Math.random()"
                    class="field-type-tag"
                    :title="field.original_field_name">
                      {{ field.original_field_name }}
                  </span>
                  <span v-if="row.field_mappings_detail && Array.isArray(row.field_mappings_detail) && row.field_mappings_detail.length > 6" class="more-fields">
                    +{{ row.field_mappings_detail.length - 6 }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="file_cn" label="相关文件" align="left" header-align="center" min-width="140">
            <template v-slot="{row}">
              <div style="white-space: pre-wrap;">{{ row.file_cn }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template v-slot="{ row }">
              <div class="operation-buttons">
								<div @click="openEditDialog(row, 'asset')" class="operation-btn edit-btn">编辑</div>
							</div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="调解案件" name="mediation">
        <el-table
          :data="currentList"
          v-loading="loading"
          border
          :cell-style="cellStyle"
          :header-cell-style="headerCellStyle"
          class="plan-table">
          <el-table-column label="序号" type="index" width="80" align="center" />
          <el-table-column label="调解案件号" prop="case_number" min-width="150" align="center" />
          <el-table-column label="案件状态" prop="case_status_cn" min-width="120" align="center"></el-table-column>
          <el-table-column label="债权人" prop="creditor_name" min-width="120" align="center" />
          <el-table-column label="债务人" prop="debtor_name" min-width="120" align="center" />
          <el-table-column label="调解信息配置" align="center" min-width="220">
              <template v-slot="{row}">
                <div class="fields-preview">
                  <div class="field-types">
                    <!-- 修复TypeError: 添加防御性检查确保mediation_config存在且为数组 -->
                    <span
                      v-for="field in (row.mediation_config && Array.isArray(row.mediation_config) ? row.mediation_config.slice(0, 4) : [])"
                      :key="field.id || field.title || Math.random()"
                      class="field-type-tag"
                      :title="field.title">
                       {{ field.title }}
                    </span>
                    <span v-if="row.mediation_config && Array.isArray(row.mediation_config) && row.mediation_config.length > 4" class="more-fields">
                      +{{ row.mediation_config.length - 4 }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
          <el-table-column prop="file_cn" label="相关文件" align="left" header-align="center" min-width="140">
            <template v-slot="{row}">
              <div style="white-space: pre-wrap;">{{ row.file_cn }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template v-slot="{ row }">
              <div class="operation-buttons">
								<div @click="openEditDialog(row, 'mediation')" class="operation-btn edit-btn">编辑</div>
							</div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!-- 分页组件 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="page"
        :page-size="page_size"
        :total="total"
        layout="total, prev, pager, next, jumper"
        @current-change="pageChanged"
      />
    </div>
  </div>
  <!-- 编辑调解信息对话框 -->
  <!-- <EditMediationInformation
    :show-dialog="showEdit"
    :plan-data="selectRow"
    @close="closeEditDialogOld"
    @ensure="submitEditPlan">
  </EditMediationInformation> -->

  <!-- 新的编辑弹窗 -->
  <EditMediationInformationDialog
    v-if="showEditDialog"
    :visible="showEditDialog"
    :row-data="editingRow"
    :edit-mode="editMode"
    @update:visible="closeEditDialog"
    @confirm="handleEditConfirm"
  />
  
  <!-- 删除确认对话框 -->
  <!-- <DeleteConfirmDialog
    :visible="showDelete"
    title="删除调解信息"
    :message="`确认删除选中的调解信息吗？此操作不可撤销。`"
    confirm-text="确认"
    cancel-text="取消"
    @update:visible="showDelete = $event"
    @confirm="deletePlanRow"
    @cancel="showDelete = false">
  </DeleteConfirmDialog> -->
</template>

<style lang="scss" scoped>
.mediation-management {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    margin-bottom: 20px;
    
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          min-width: 80px;
          font-weight: 500;
          color: #333;
        }
        
        :deep(.el-select) {
          width: 160px;
        }
        
        :deep(.el-date-editor) {
          height: 36px;
        }
      }
    }
  }

  .mediation-tabs {
    .config-preview, .files-preview {
      text-align: center;
      color: #666;
      font-size: 14px;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 20px 0;
  }
}

.plan-table {
  border-radius: 8px;
  overflow: hidden;
  
  .plan-title {
    .title-text {
      color: #333;
      line-height: 1.4;
    }
  }
  
  .fields-preview {
    .field-types {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      justify-content: center;
      
      .field-type-tag {
        display: inline-block;
        padding: 2px 6px;
        background-color: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 12px;
        font-size: 12px;
        color: #0369a1;
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .more-fields {
        padding: 2px 6px;
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
  
  .field-stats {
    .required-count {
      margin-top: 4px;
    }
  }
  
  .create-time {
    font-size: 13px;
    color: #666;
  }
}

.pagi {
  margin-top: 20px;
  text-align: center;
  
  :deep(.el-pagination) {
    justify-content: center;
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .plan-management {
    .search-header {
      grid-template-columns: 1fr auto;
    }
  }
  
  .plan-table {
    .fields-preview {
      .field-types {
        .field-type-tag {
          max-width: 60px;
        }
      }
    }
  }
}
</style>