/* empty css             */import{C as de,c as ue,h as ce}from"./headerCellStyle-17161c7c.js";import{C as I}from"./CustomButton-ea16d5c5.js";/* empty css                     *//* empty css                        *//* empty css                 */import{_ as H}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{a as M,r as h,s as P,o as f,q as S,w as i,J as K,f as E,h as a,F as N,A as j,D as W,g as m,i as F,E as o,j as Y,k as Z,Q as ee,S as le,at as ae,U as te,l as se,M as R,p as X,m as L,au as ie,e as re,z as _e,av as fe,n as G,a9 as pe,aw as ve,ax as me,K as he,t as be,ay as ge,ad as ke,az as ye,N as we}from"./index-8a4876d8.js";import{F as z,V as q}from"./types-d67a131c.js";import{_ as J}from"./_plugin-vue_export-helper-c27b6911.js";const oe=r=>(X("data-v-d9a82203"),r=r(),L(),r),xe={class:"dialog-content"},Ce={key:0,class:"desensitize-config"},Ve={class:"footer-actions"},Ee=oe(()=>m("i",{class:"jt-20-ensure"},null,-1)),$e=oe(()=>m("i",{class:"jt-20-delete"},null,-1)),Te=M({__name:"fieldConfigurationAdd",props:{visible:{type:Boolean}},emits:["close","success"],setup(r,{emit:C}){const k=r,u=h(!1),e=h({field_name:"",field_type:z.TEXT,data_validation:q.NONE,is_masked:!1,prefix_keep_chars:0,suffix_keep_chars:0}),y=h([]),g=h([]);async function D(){try{const{data:n}=await ie(),{state:t,msg:V}=n;if(t==="success"){const d=Object.entries(n.data.field_types).map(([p,b])=>({label:b,value:p})),w=Object.entries(n.data.validation_types).map(([p,b])=>({label:b,value:p}));y.value=d,g.value=w}else o.error(V)}catch{o.error("获取字段类型失败")}}P(()=>k.visible,n=>{n&&(x(),D())});function x(){e.value={field_name:"",field_type:z.TEXT,data_validation:q.NONE,is_masked:!1,prefix_keep_chars:0,suffix_keep_chars:0}}async function O(){if(!e.value.field_name.trim()){o.error("字段名称不能为空");return}if(e.value.is_masked){if(e.value.prefix_keep_chars===0||e.value.prefix_keep_chars===null){o.error("前保留字符数不能为0");return}if(e.value.suffix_keep_chars===0||e.value.suffix_keep_chars===null){o.error("后保留字符数不能为0");return}}else e.value.is_masked||(e.value.prefix_keep_chars=0,e.value.suffix_keep_chars=0);C("success",e.value)}function T(){C("close")}return(n,t)=>{const V=Y,d=Z,w=ee,p=le,b=ae,U=te,B=se,s=R;return f(),S(H,{visible:n.visible,"onUpdate:visible":T,width:"800px",title:"新增字段配置"},{default:i(()=>[K((f(),E("div",xe,[a(B,{model:e.value,"label-width":"140px","label-position":"left",class:"field-config-form"},{default:i(()=>[a(d,{label:"字段名称",required:""},{default:i(()=>[a(V,{modelValue:e.value.field_name,"onUpdate:modelValue":t[0]||(t[0]=l=>e.value.field_name=l),placeholder:"请输入字段名称",maxlength:"50","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(d,{label:"字段类型",required:""},{default:i(()=>[a(p,{modelValue:e.value.field_type,"onUpdate:modelValue":t[1]||(t[1]=l=>e.value.field_type=l),placeholder:"选择类型",style:{width:"100%"}},{default:i(()=>[(f(!0),E(N,null,j(y.value,l=>(f(),S(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"数据校验",required:""},{default:i(()=>[a(p,{modelValue:e.value.data_validation,"onUpdate:modelValue":t[2]||(t[2]=l=>e.value.data_validation=l),placeholder:"选择校验",style:{width:"100%"}},{default:i(()=>[(f(!0),E(N,null,j(g.value,l=>(f(),S(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"是否脱敏"},{default:i(()=>[a(b,{modelValue:e.value.is_masked,"onUpdate:modelValue":t[3]||(t[3]=l=>e.value.is_masked=l)},null,8,["modelValue"])]),_:1}),e.value.is_masked?(f(),E("div",Ce,[a(d,{label:"前保留字符数"},{default:i(()=>[a(U,{modelValue:e.value.prefix_keep_chars,"onUpdate:modelValue":t[4]||(t[4]=l=>e.value.prefix_keep_chars=l),min:0,max:99,placeholder:"前保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(d,{label:"后保留字符数"},{default:i(()=>[a(U,{modelValue:e.value.suffix_keep_chars,"onUpdate:modelValue":t[5]||(t[5]=l=>e.value.suffix_keep_chars=l),min:0,max:99,placeholder:"后保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])):W("",!0)]),_:1},8,["model"]),m("div",Ve,[a(I,{onClick:O,height:34,loading:u.value,"btn-type":"blue"},{default:i(()=>[Ee,F("确认 ")]),_:1},8,["loading"]),a(I,{onClick:T,height:34},{default:i(()=>[$e,F("取消 ")]),_:1})])])),[[s,u.value]])]),_:1},8,["visible"])}}});const Ue=J(Te,[["__scopeId","data-v-d9a82203"]]),ne=r=>(X("data-v-c645b40f"),r=r(),L(),r),Se={class:"dialog-content"},De={key:0,class:"desensitize-config"},Fe={class:"footer-actions"},Oe=ne(()=>m("i",{class:"jt-20-ensure"},null,-1)),Be=ne(()=>m("i",{class:"jt-20-delete"},null,-1)),Ie=M({__name:"fieldConfigurationEdit",props:{visible:{type:Boolean},editData:{}},emits:["close","success"],setup(r,{emit:C}){const k=r,u=h(!1),e=h({field_name:"",field_type:z.TEXT,data_validation:q.NONE,is_masked:!1,prefix_keep_chars:0,suffix_keep_chars:0}),y=h([]),g=h([]);async function D(){try{const{data:n}=await ie(),{state:t,msg:V}=n;if(t==="success"){const d=Object.entries(n.data.field_types).map(([p,b])=>({label:b,value:p})),w=Object.entries(n.data.validation_types).map(([p,b])=>({label:b,value:p}));y.value=d,g.value=w}else o.error(V)}catch{o.error("获取字段类型失败")}}P([()=>k.visible,()=>k.editData],([n,t])=>{n&&t&&(x(t),D())});function x(n){e.value={...n}}async function O(){if(!e.value.field_name.trim()){o.error("字段名称不能为空");return}if(e.value.is_masked){if(e.value.prefix_keep_chars===0||e.value.prefix_keep_chars===null){o.error("前保留字符数不能为0");return}if(e.value.suffix_keep_chars===0||e.value.suffix_keep_chars===null){o.error("后保留字符数不能为0");return}}else e.value.is_masked||(e.value.prefix_keep_chars=0,e.value.suffix_keep_chars=0);C("success",e.value)}function T(){C("close")}return(n,t)=>{const V=Y,d=Z,w=ee,p=le,b=ae,U=te,B=se,s=R;return f(),S(H,{visible:n.visible,"onUpdate:visible":T,width:"800px",title:"编辑字段配置"},{default:i(()=>[K((f(),E("div",Se,[a(B,{model:e.value,"label-width":"140px","label-position":"left",class:"field-config-form"},{default:i(()=>[a(d,{label:"字段名称",required:""},{default:i(()=>[a(V,{modelValue:e.value.field_name,"onUpdate:modelValue":t[0]||(t[0]=l=>e.value.field_name=l),placeholder:"请输入字段名称",maxlength:"50","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(d,{label:"字段类型"},{default:i(()=>[a(p,{modelValue:e.value.field_type,"onUpdate:modelValue":t[1]||(t[1]=l=>e.value.field_type=l),placeholder:"选择类型",style:{width:"100%"}},{default:i(()=>[(f(!0),E(N,null,j(y.value,l=>(f(),S(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"数据校验"},{default:i(()=>[a(p,{modelValue:e.value.data_validation,"onUpdate:modelValue":t[2]||(t[2]=l=>e.value.data_validation=l),placeholder:"选择校验",style:{width:"100%"}},{default:i(()=>[(f(!0),E(N,null,j(g.value,l=>(f(),S(w,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"是否脱敏"},{default:i(()=>[a(b,{modelValue:e.value.is_masked,"onUpdate:modelValue":t[3]||(t[3]=l=>e.value.is_masked=l)},null,8,["modelValue"])]),_:1}),e.value.is_masked?(f(),E("div",De,[a(d,{label:"前保留字符数"},{default:i(()=>[a(U,{modelValue:e.value.prefix_keep_chars,"onUpdate:modelValue":t[4]||(t[4]=l=>e.value.prefix_keep_chars=l),min:0,max:99,placeholder:"前保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(d,{label:"后保留字符数"},{default:i(()=>[a(U,{modelValue:e.value.suffix_keep_chars,"onUpdate:modelValue":t[5]||(t[5]=l=>e.value.suffix_keep_chars=l),min:0,max:99,placeholder:"后保留字符数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])):W("",!0)]),_:1},8,["model"]),m("div",Fe,[a(I,{onClick:O,height:34,loading:u.value,"btn-type":"blue"},{default:i(()=>[Oe,F("确认 ")]),_:1},8,["loading"]),a(I,{onClick:T,height:34},{default:i(()=>[Be,F("取消 ")]),_:1})])])),[[s,u.value]])]),_:1},8,["visible"])}}});const Ne=J(Ie,[["__scopeId","data-v-c645b40f"]]),je=r=>(X("data-v-9a60409a"),r=r(),L(),r),Ae={class:"field-configuration"},ze={class:"search-header"},qe=je(()=>m("i",{class:"jt-20-add"},null,-1)),Me={class:"table-container"},Ke={class:"operation-buttons"},Re=["onClick"],Xe=["onClick"],Le=["onClick"],Je=["onClick"],Qe=M({__name:"fieldConfiguration",setup(r){const C=h(""),k=h(!1),u=h([]),e=h(!1),y=h(!1),g=h(null),D=re({page:1,page_size:9999,total:0});_e(()=>{x()});async function x(){k.value=!0;const s={page:D.page,page_size:D.page_size,search:C.value},{data:l}=await fe(s),{state:c,msg:v}=l;c==="success"?(u.value=l.data.results,k.value=!1):(o.error(v),k.value=!1)}function O(){pe("搜索","数据治理-字段配置"),x()}async function T(s){if(s===0){o.warning("已经是第一行");return}const l=u.value[s];u.value[s]=u.value[s-1],u.value[s-1]=l,await t()}async function n(s){if(s===u.value.length-1){o.warning("已经是最后一行");return}const l=u.value[s];u.value[s]=u.value[s+1],u.value[s+1]=l,await t()}async function t(){try{k.value=!0;const s=u.value.map((_,$)=>({id:_.id,field_name:_.field_name,field_type:_.field_type,data_validation:_.data_validation||_.validation,is_masked:_.is_masked!==void 0?_.is_masked:_.isDesensitize,prefix_keep_chars:_.prefix_keep_chars!==void 0?_.prefix_keep_chars:_.prefixKeepCount,suffix_keep_chars:_.suffix_keep_chars!==void 0?_.suffix_keep_chars:_.suffixKeepCount,display_order:$+1})),{data:l}=await ge({items:s}),{state:c,msg:v}=l;c==="success"?(o.success(v),await x()):o.error(v||"保存失败")}catch(s){o.error(s||"保存失败")}finally{k.value=!1}}function V(){e.value=!0}function d(s){g.value={...s},y.value=!0}function w(){e.value=!1}function p(){y.value=!1,g.value=null}async function b(s){const{data:l}=await ve(s),{state:c,msg:v}=l;c==="success"?(o.success(v),e.value=!1,x()):(o.error(v),e.value=!1)}async function U(s){const{data:l}=await me(s,g.value.id),{state:c,msg:v}=l;c==="success"?(o.success(v),y.value=!1,g.value=null,x()):(o.error(v),y.value=!1,g.value=null)}async function B(s){await ke.confirm(`确定要删除字段"${s.field_name}"吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const{data:l}=await ye(s.id),{state:c,msg:v}=l;c==="success"?(o.success(v),x()):o.error(v)}return(s,l)=>{const c=we,v=he,_=R;return f(),E("div",Ae,[m("div",ze,[a(de,{modelValue:C.value,"onUpdate:modelValue":l[0]||(l[0]=$=>C.value=$),placeholder:"搜索字段名称、类型",onClick:O},null,8,["modelValue"]),a(I,{onClick:V,height:34},{default:i(()=>[qe,F("新增字段")]),_:1})]),m("div",Me,[K((f(),S(v,{data:u.value,border:"",style:{width:"100%"},"cell-style":G(ue),"header-cell-style":G(ce),"max-height":730},{default:i(()=>[a(c,{type:"index",label:"序号",width:"80",align:"center"}),a(c,{prop:"field_name",label:"字段名称","min-width":"210",align:"center"}),a(c,{prop:"field_type_cn",label:"字段类型","min-width":"100",align:"center"}),a(c,{prop:"data_validation_cn",label:"数据校验","min-width":"100",align:"center"}),a(c,{label:"是否脱敏","min-width":"60",align:"center"},{default:i(({row:$})=>[F(be($.is_masked?"是":"否"),1)]),_:1}),a(c,{prop:"prefix_keep_chars",label:"前保留字符数","min-width":"70",align:"center"}),a(c,{prop:"suffix_keep_chars",label:"后保留字符数","min-width":"70",align:"center"}),a(c,{label:"操作",width:"310",fixed:"right",align:"center"},{default:i(({row:$,$index:Q})=>[m("div",Ke,[m("div",{onClick:A=>d($),class:"operation-btn edit-btn"},"编辑",8,Re),m("div",{onClick:A=>T(Q),class:"operation-btn move-up-btn"},"上移",8,Xe),m("div",{onClick:A=>n(Q),class:"operation-btn move-down-btn"},"下移",8,Le),m("div",{onClick:A=>B($),class:"operation-btn delete-btn"},"删除",8,Je)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[_,k.value]])]),a(Ue,{visible:e.value,onClose:w,onSuccess:b},null,8,["visible"]),a(Ne,{visible:y.value,"edit-data":g.value,onClose:p,onSuccess:U},null,8,["visible","edit-data"])])}}});const sl=J(Qe,[["__scopeId","data-v-9a60409a"]]);export{sl as default};
