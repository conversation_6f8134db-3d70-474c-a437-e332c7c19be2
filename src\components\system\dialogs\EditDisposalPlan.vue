<script lang="ts" setup>
import { ref, type Ref, watch, computed } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
import { ElMessage } from "element-plus";
import type { FieldType, PlanField, EditPlanParams, Plan, FieldTypeOption } from "../auth/type";
import { FieldType as FieldTypeEnum } from "../auth/type";
import { getMediationPlanDetail, getMediationCase, getDataImportDetail, expressionCalculation } from '@/axios/system';

const props = defineProps<{
  showDialog: boolean,
  planData: Plan,  // 要编辑的方案数据
  currentMode?: 'mediationCases' | 'asset', // 当前模式
  mediationCaseOptions?: Array<{ label: string; value: string }>, // 调解案件选项
  assetPackageOptions?: Array<{ label: string; value: string }> // 资产包选项
}>()

const emit = defineEmits<{
  (e: 'close'): void,
  (e: 'ensure', params: EditPlanParams): void
}>()

// 表单引用
const planFormRef = ref()
const loading = ref(false)

// 方案基础信息
const planForm = ref({
  asset_package: null, // 资产包下拉框
  mediation_case: null, // 调解案件下拉框
  plan_name: '', // 方案名称
})

// 动态字段列表（编辑模式下支持新增、删除和修改）
const dynamicFields: Ref<PlanField[]> = ref([])

// 表达式变量列表
const expressionVariables = ref<Array<{code: string, name: string}>>([])

// 监听弹框显示状态，获取变量数据
watch(() => props.showDialog, (newVal) => {
  if (newVal && props.planData) {
    loadExpressionVariables()
  }
})

// 获取表达式变量数据
async function loadExpressionVariables() {
  try {
    // 从方案详情中获取mapped_field_names
    if (props.planData?.id) {
      const response = await getMediationPlanDetail(Number(props.planData.id))
      if (response.data.state === 'success') {
        expressionVariables.value = (response.data.data.mapped_field_names || []).map((name: string) => ({
          code: name,
          name: name
        }))
      }
    }
  } catch (error) {
    console.error('获取表达式变量失败:', error)
    expressionVariables.value = []
  }
}

// 自定义表达式编辑器相关状态
const showVariableDropdown: Ref<Record<number, boolean>> = ref({})
const filteredVariables: Ref<Record<number, Array<{ code: string, name: string }>>> = ref({})
const cursorPosition: Ref<Record<number, number>> = ref({})
const variableSearchText: Ref<Record<number, string>> = ref({})

// 字段类型选项配置
const fieldTypeOptions: FieldTypeOption[] = [
  { label: '文本输入', value: FieldTypeEnum.TEXTAREA, icon: 'jt-24-edit' },
  { label: '日期选择', value: FieldTypeEnum.DATE, icon: 'jt-24-calendar' },
  { label: '金额输入', value: FieldTypeEnum.AMOUNT, icon: 'jt-24-money' },
]

// 计算当前模式，默认为调解案件模式
const currentMode = computed(() => props.currentMode || 'mediationCases')

// 计算对话框标题
const dialogTitle = computed(() => {
  return currentMode.value === 'asset' ? '编辑资产包方案' : '编辑调解案件方案'
})

// 表单验证规则
const rules = computed(() => {
  const baseRules =  {
    plan_name: [
      { required: true, message: '请输入方案名称', trigger: 'blur' },
    ]
  }
  if (currentMode.value === 'asset') {
    return {
      ...baseRules,
      asset_package: [
        { required: true, message: '请选择资产包', trigger: 'change' }
      ]
    }
  } else {
    return {
      ...baseRules,
      mediation_case: [
        { required: true, message: '请选择调解案件', trigger: 'change' }
      ]
    }
  }
})

// 监听弹框显示状态
watch(() => props.showDialog, onOpenDialog)

// 监听方案数据变化
watch(() => props.planData, loadPlanData, { deep: true })

// 弹框打开时加载数据
function onOpenDialog(newVal: boolean) {
  if(newVal) {
    loadPlanData(props.planData)
  }
}

// 加载方案数据
function loadPlanData(planData: Plan) {
  if (!planData) return
  if (currentMode.value === 'asset') {
    // 资产包模式：设置资产包ID
    planForm.value.asset_package = planData.asset_package
  } else {
    // 调解案件模式：设置调解案件ID
    planForm.value.mediation_case = planData.mediation_case
  }
  
  // 设置方案名称
  planForm.value.plan_name = planData.plan_name

  // 深拷贝字段数据，已存在的字段默认设为必填
  dynamicFields.value = planData.plan_config ? JSON.parse(JSON.stringify(planData.plan_config)).map((field: PlanField) => {
    // 已创建的字段默认设为必填
    field.required = true
    // 确保有默认逻辑类型
    if (!field.logic_type) {
      field.logic_type = 'result_calculation'
    }
    // 确保有表达式和预览字段
    if (!field.expression) {
      field.expression = ''
    }
    if (!field.preview) {
      field.preview = ''
    }
    // 修复InvalidCharacterError: 确保字段ID为有效字符串
    if (!field.id || typeof field.id !== 'string' || field.id.trim() === '') {
      field.id = generateFieldId()
    }
    return field
  }) : []
}

// 关闭弹框
function close() {
  emit('close')
}

// 生成唯一字段ID - 修复InvalidCharacterError: 确保ID始终为有效字符串
function generateFieldId(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 11)
  return `field_${timestamp}_${random}`
}

// 添加新字段
function addField() {
  const newField: PlanField = {
    id: generateFieldId(),
    title: '',
    type: FieldTypeEnum.TEXTAREA,
    value: '',
    required: true, // 新增字段也默认设为必填
    logic_type: 'result_calculation', // 默认逻辑处理类型：结果运算
    expression: '', // 表达式
    preview: '' // 预览内容
  }
  dynamicFields.value.push(newField)
}

// 删除字段
function removeField(index: number) {
  dynamicFields.value.splice(index, 1)
}

// 字段类型改变处理
function onFieldTypeChange(field: PlanField) {
  // 重置字段值
  field.value = getDefaultValueByType(field.type || FieldTypeEnum.TEXTAREA)
}

// 根据字段类型获取默认值
function getDefaultValueByType(type: FieldType): any {
  switch (type) {
    case FieldTypeEnum.TEXTAREA:
      return ''
    case FieldTypeEnum.DATE:
      return ''
    case FieldTypeEnum.AMOUNT:
      return 0
    default:
      return ''
  }
}

// 处理逻辑处理类型变化
function handleLogicTypeChange(value: any, fieldIndex: number) {
  const logicType = String(value)
  console.log('逻辑处理类型变化:', value, 'fieldIndex:', fieldIndex)
  
  if (dynamicFields.value[fieldIndex]) {
    dynamicFields.value[fieldIndex].logic_type = logicType
  }
}

// 表达式编辑相关函数
function handleExpressionInput(displayValue: string, fieldIndex: number) {
  console.log('输入事件 - 显示值:', displayValue)
  if (dynamicFields.value[fieldIndex]) {
    dynamicFields.value[fieldIndex].expression = displayValue
  }
}

async function handleExpressionBlurEvent(event: Event, fieldIndex: number) {
  const target = event.target as HTMLTextAreaElement
  const displayValue = target.value
  console.log('失焦事件 - 输入框显示值:', displayValue)
  
  if (!dynamicFields.value[fieldIndex]) return
  
  const field = dynamicFields.value[fieldIndex]
  field.expression = displayValue
  
  // 隐藏变量下拉框
  hideVariableDropdown(fieldIndex)
  
  // 如果表达式为空，清空预览
  if (!displayValue.trim()) {
    field.preview = ''
    return
  }
  
  // 调用表达式计算接口
  const logicType = field.logic_type || 'result_calculation'
  const response = await expressionCalculation({
    asset_package_id: props.planData?.asset_package ? Number(props.planData.asset_package) : props.planData.id,
    expression: displayValue,
    logic_type: logicType
  })
  
  if (response.data.state === 'success') {
    field.preview = String(response.data.data || '')
  } else {
    field.preview = response.data.msg
  }
}

function handleExpressionKeydown(event: KeyboardEvent, fieldIndex: number) {
  const target = event.target as HTMLTextAreaElement
  cursorPosition.value[fieldIndex] = target.selectionStart || 0
  
  if (event.key === '@') {
    showVariableDropdown.value[fieldIndex] = true
    filteredVariables.value[fieldIndex] = expressionVariables.value
    variableSearchText.value[fieldIndex] = ''
  } else if (event.key === 'Escape') {
    hideVariableDropdown(fieldIndex)
  }
}

function handleExpressionFocus(fieldIndex: number) {
  console.log('表达式输入框聚焦:', fieldIndex)
}

function searchVariables(searchText: string, fieldIndex: number) {
  if (!searchText || searchText.trim() === '') {
    filteredVariables.value[fieldIndex] = expressionVariables.value
  } else {
    const filtered = expressionVariables.value.filter(variable =>
      variable.name.toLowerCase().includes(searchText.toLowerCase()) ||
      variable.code.toLowerCase().includes(searchText.toLowerCase())
    )
    filteredVariables.value[fieldIndex] = filtered
  }
}

function hideVariableDropdown(fieldIndex: number) {
  showVariableDropdown.value[fieldIndex] = false
  filteredVariables.value[fieldIndex] = []
}

function insertVariableAtCursor(variable: any, fieldIndex: number) {
  if (!dynamicFields.value[fieldIndex]) return
  
  const field = dynamicFields.value[fieldIndex]
  const currentExpression = field.expression || ''
  const cursorPos = cursorPosition.value[fieldIndex] || currentExpression.length
  
  const beforeCursor = currentExpression.substring(0, cursorPos)
  const afterCursor = currentExpression.substring(cursorPos)
  const finalBefore = beforeCursor.endsWith('@') ? beforeCursor.slice(0, -1) : beforeCursor
  const variableName = `{${variable.name}}`
  
  const newExpression = finalBefore + variableName + afterCursor
  field.expression = newExpression
  
  hideVariableDropdown(fieldIndex)
}

function getExpressionPlaceholder(expression: string) {
  if (!expression || expression.trim() === '') {
    return '请输入表达式，支持中文、数字、运算符（+ - * /），输入@可选择变量'
  }
  return ''
}

function formatExpressionForDisplay(expression: string): string {
  if (!expression) return ''
  return expression.replace(/\{([^}]+)\}/g, '<span class="variable-highlight">{$1}</span>')
}
/* 
// 生成微信小程序渲染格式的JSON数据
function generateWechatRenderData(): string {
  const planTitle = planForm.value.plan_name

  const renderData = {
    planTitle,
    sections: dynamicFields.value.map(field => ({
      title: field.title,
      content: formatFieldValueForRender(field),
      type: field.type,
    }))
  }
  return JSON.stringify(renderData, null, 2)
}

// 格式化字段值用于渲染
function formatFieldValueForRender(field: PlanField): string {
  switch (field.type) {
    case FieldTypeEnum.DATE:
      return field.value ? new Date(field.value).toLocaleDateString() : ''
    case FieldTypeEnum.AMOUNT:
      return field.value ? `¥${Number(field.value).toFixed(2)}` : '¥0.00'
    default:
      return String(field.value || '')
  }
} */

// 验证动态字段
function validateDynamicFields(): boolean {
  for (let i = 0; i < dynamicFields.value.length; i++) {
    const field = dynamicFields.value[i]
    
    if (!field.title.trim()) {
      ElMessage.error(`第${i + 1}个字段的标题不能为空`)
      return false
    }
    
    /* if (field.required) {
      if (!field.value || String(field.value).trim() === '') {
        ElMessage.error(`${field.title}是必填字段，请填写内容`)
        return false
      }
    } */
  }
  return true
}

// 确认编辑方案
async function ensureEdit() {
  if (!planFormRef.value) return
  
  loading.value = true
  try {
    // 验证基础表单
    const isValidForm = await new Promise((resolve) => {
      planFormRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValidForm) return
    
    // 验证动态字段
    if (!validateDynamicFields()) return
    
    // 检查是否至少有一个字段
    if (dynamicFields.value.length === 0) {
      ElMessage.error('请至少保留一个字段')
      return
    }
    
    // 构造提交数据
    const params: EditPlanParams = {
      id: props.planData.id!,
      plan_name: planForm.value.plan_name,
      plan_config: dynamicFields.value,
      // jsonData: generateWechatRenderData(),
      // 根据模式添加可选参数
      ...(currentMode.value === 'asset' && {
        asset_package: planForm.value.asset_package
      }),
      ...(currentMode.value === 'mediationCases' && {
        mediation_case: planForm.value.mediation_case
      })
    }
    console.log(params,'===编辑弹框保存参数')
    emit('ensure', params)
  } finally {
    loading.value = false
  }
}

// 获取字段类型标签
/* function getFieldTypeLabel(type?: FieldType): string {
  const option = fieldTypeOptions.find(opt => opt.value === type)
  return option?.label || '未知类型'
} 

// 处理文件变更
function handleFileChange(file: File, field: PlanField) {
  if (!field.fileList) {
    field.fileList = []
  }

  const uploadFile = {
    id: Date.now().toString(),
    name: file.name,
    size: file.size,
    type: file.type,
    file: file,
    status: 'success' as const
  }

  field.fileList.push(uploadFile)
}

// 移除文件
function removeFile(field: PlanField, fileIndex: number) {
  if (field.fileList) {
    field.fileList.splice(fileIndex, 1)
  }
}*/
</script>

<template>
  <CustomDialog :visible="showDialog" @update:visible="close" width="1200px" :title="dialogTitle">
    <div class="edit-plan-content">
      <el-form ref="planFormRef" :model="planForm" :rules="rules" label-width="110px">
        <!-- 资产包模式：资产包下拉框 -->
        <el-form-item v-if="currentMode === 'asset'" label="资产包" prop="asset_package">
          <el-select
            v-model="planForm.asset_package"
            filterable
            placeholder="请选择资产包"
            style="width: 100%">
            <el-option
              v-for="option in assetPackageOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>

        <!-- 调解案件模式：调解案件下拉框 -->
        <el-form-item v-else label="调解案件" prop="mediation_case">
          <el-select
            v-model="planForm.mediation_case"
            filterable
            placeholder="请选择调解案件"
            style="width: 100%">
            <el-option
              v-for="option in mediationCaseOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>

        <!-- 方案名称输入框 -->
        <el-form-item label="方案名称" prop="plan_name">
          <el-input
            v-model="planForm.plan_name"
            placeholder="例如方案一、方案二"
            maxlength="50"
            show-word-limit />
        </el-form-item>
      </el-form>
      <div class="dynamic-fields-section">
        <div class="section-header">
          <h3>字段配置</h3>
          <CustomButton @click="addField" :height="34" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>
        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div
            v-for="(field, index) in dynamicFields"
            :key="field.id || `field-${index}`"
            class="field-item">
            
            <!-- 字段头部：显示序号和删除按钮 -->
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton 
                @click="removeField(index)" 
                :height="28" 
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>

            <!-- 字段配置 -->
            <div class="field-config">
              <!-- 字段类型 -->
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select
                  v-model="field.type"
                  @change="onFieldTypeChange(field)"
                  style="width: 280px;">
                  <el-option
                    v-for="option in fieldTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                    <span style="display: flex; align-items: center;">
                      <i :class="option.icon" style="margin-right: 8px;"></i>
                      {{ option.label }}
                    </span>
                  </el-option>
                </el-select>
              </div>

              <!-- 字段标题 -->
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <el-input
                  v-model="field.title"
                  placeholder="请输入字段标题"
                  style="width: 280px;" />
              </div>
            </div>

            <!-- 逻辑处理类型选择区域 -->
            <div class="logic-type-section" :key="`logic-type-${field.id}-${index}`">
              <label class="config-label">逻辑类型：</label>
              <el-radio-group
                v-model="field.logic_type"
                class="logic-type-radio-group"
                @change="(value) => handleLogicTypeChange(value, index)"
                :key="`radio-group-${field.id}-${index}`"
              >
                <el-radio
                  label="text_format"
                  size="small"
                  :key="`text-format-${field.id}-${index}`"
                >
                  文本格式化
                </el-radio>
                <el-radio
                  label="result_calculation"
                  size="small"
                  :key="`result-calculation-${field.id}-${index}`"
                >
                  结果运算
                </el-radio>
              </el-radio-group>
            </div>

            <!-- 表达式编辑区域 -->
            <div class="expression-section">
              <label class="config-label">表达式编辑：</label>

              <!-- 自定义表达式编辑器 -->
              <div class="custom-expression-editor">
                <div class="expression-input-container">
                  <el-input
                    v-model="field.expression"
                    @input="handleExpressionInput($event, index)"
                    type="textarea"
                    :rows="3"
                    :placeholder="getExpressionPlaceholder(field.expression)"
                    @blur="handleExpressionBlurEvent($event, index)"
                    @keydown="handleExpressionKeydown($event, index)"
                    @focus="handleExpressionFocus(index)"
                    class="expression-textarea"
                  />

                  <!-- 变量选择下拉框 -->
                  <div
                    v-if="showVariableDropdown[index]"
                    class="variable-dropdown"
                  >
                    <div class="dropdown-header">
                      <span>选择变量：</span>
                      <el-button size="small" text @click="hideVariableDropdown(index)">×</el-button>
                    </div>

                    <!-- 变量搜索框 -->
                    <div class="variable-search">
                      <el-input
                        v-model="variableSearchText[index]"
                        size="small"
                        placeholder="搜索变量名称..."
                        @input="searchVariables(variableSearchText[index] || '', index)"
                        clearable
                      />
                    </div>

                    <div class="variable-list">
                      <div
                        v-for="variable in filteredVariables[index] || expressionVariables"
                        :key="variable.code"
                        @click.stop="insertVariableAtCursor(variable, index)"
                        @mousedown.prevent
                        class="variable-item"
                      >
                        <span class="variable-name">{{ variable.name }}</span>
                      </div>

                      <!-- 无搜索结果提示 -->
                      <div
                        v-if="(filteredVariables[index] || expressionVariables).length === 0"
                        class="no-results"
                      >
                        未找到匹配的变量
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表达式预览区域 -->
            <div v-if="field.expression && field.expression.trim()" class="expression-preview">
              <span class="preview-label">表达式预览：</span>
              <span class="preview-content" v-html="formatExpressionForDisplay(field.expression)"></span>
            </div>

            <!-- 内容预览区域 -->
            <div class="preview-section">
              内容预览：<div class="preview-content">{{ field.preview }}</div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-fields">
          <p>还没有添加任何字段，点击"添加字段"开始配置方案内容</p>
        </div>
      </div>
      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-plan-content {
  .dynamic-fields-section {
    margin-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .empty-fields {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      border-radius: 8px;
      border: 1px dashed #ddd;
    }
  }
  
  .fields-list {
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }
      
      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 15px;
        
        .config-row {
          display: flex;
          align-items: center;
          gap: 5px;
        }
        
        .config-label {
          min-width: 90px;
          font-size: 16px;
          color: #666;
          font-weight: 500;
        }
      }
      
      .expression-section {
        margin-bottom: 22px;
        display: flex;

        .config-label {
          display: block;
          margin-bottom: 8px;
        }
      }
    }
  }

  .expression-preview {
    margin-top: 16px;
    margin-bottom: 18px;
    line-height: 1.4;
    .config-label{
      display: inline-block;
      width: 96px;
      font-size: 16px;
      color: #666;
      font-weight: 500;
    }
    .preview-content {
      font-size: 16px;
      color: #666;
      font-weight: 500;

      :deep(.variable-highlight) {
        background: #e3f2fd;
        color: #1976d2;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: 500;
        border: 1px solid #bbdefb;
      }
    }
  }
  
  .preview-section {
    display: flex;
    font-size: 16px;
    color: #666;
    font-weight: 500;
    margin-bottom: 8px;
    gap: 16px;
  }

  // 逻辑处理类型选择区域样式
  .logic-type-section {
    margin-bottom: 16px;

    .config-label {
      display: inline-block;
      width: 96px;
      font-size: 16px;
      color: #666;
      font-weight: 500;
    }

    .logic-type-radio-group {
      .el-radio {
        margin-right: 16px;

        :deep(.el-radio__label) {
          font-size: 16px;
        }

        &.is-checked .el-radio__label {
          color: #409eff;
        }
      }
    }
  }

  // 自定义表达式编辑器样式
  .custom-expression-editor {
    position: relative;
    width: 89%;

    .expression-input-container {
      position: relative;

      .expression-textarea {
        :deep(.el-textarea__inner) {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
          line-height: 1.5;
          resize: vertical;
          min-height: 80px;
        }
      }

      .variable-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 2000;
        max-height: 300px;
        overflow-y: auto;
        pointer-events: auto;

        .dropdown-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #ebeef5;
          background: #f5f7fa;
          font-size: 12px;
          color: #606266;
          font-weight: 500;
        }

        .variable-search {
          padding: 8px 12px;
          border-bottom: 1px solid #ebeef5;
          background: #fafafa;
        }

        .variable-list {
          .variable-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
            user-select: none;
            pointer-events: auto;

            &:hover {
              background: #f5f7fa;
              color: #409eff;
            }

            &:active {
              background: #e6f7ff;
            }

            &:last-child {
              border-bottom: none;
            }

            .variable-name {
              font-size: 13px;
              font-weight: 500;
            }
          }

          .no-results {
            padding: 16px 12px;
            text-align: center;
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .edit-plan-content {
    .dynamic-fields-section {
      .fields-list {
        .field-item {
          .field-config {
            grid-template-columns: 1fr;
          }
        }
      }
    }
    
    .btns-group {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>
