/* empty css             */import{C as ae,c as te,h as ne}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{a as P,r as m,c as oe,s as z,o as f,q as j,w as o,J as K,f as k,h as e,g as u,F as N,A as B,i as S,E as g,j as se,k as ie,T as ue,Q as q,S as A,l as ce,M as J,p as Q,m as G,z as de,I as re,n as L,D as pe,ap as _e,aq as me,K as ve,L as be,t as T,N as he,V as fe}from"./index-8a4876d8.js";import{_ as ge}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";/* empty css                     *//* empty css                       *//* empty css                  */import{C as O}from"./CustomButton-ea16d5c5.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";const R=v=>(Q("data-v-a72493ec"),v=v(),G(),v),ye={class:"edit-complaint-form"},we={class:"readonly-section"},De={class:"editable-section"},ke={class:"dialog-footer"},Ce=R(()=>u("i",{class:"jt-20-ensure"},null,-1)),Ve=R(()=>u("i",{class:"jt-20-delete"},null,-1)),Ee=P({__name:"EditComplaintDialog",props:{visible:{type:Boolean,default:!1},complaintData:{default:null}},emits:["update:visible","confirm"],setup(v,{emit:w}){const i=v,C=localStorage.getItem("userName"),p=m(!1),a=m({handler:C||"",handle_time:"",process_status:"",handle_result:""}),V=[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已解决",value:"resolved"},{label:"已关闭",value:"closed"}],E=[{label:"意见建议",value:"suggestion"},{label:"服务投诉",value:"complaint "}],x=oe(()=>{if(!i.complaintData)return"";const n=E.find(s=>s.value===i.complaintData.feedback_type_cn);return(n==null?void 0:n.label)||i.complaintData.feedback_type_cn});z(()=>[i.visible,i.complaintData],([n,s])=>{n&&s&&typeof s=="object"&&y(s.id)},{immediate:!0});async function y(n){if(i.complaintData)try{p.value=!0,console.log("获取投诉反馈详细信息:",n),await new Promise(s=>setTimeout(s,500)),a.value={handler:i.complaintData.handler||"",handle_time:i.complaintData.handle_time||"",process_status:i.complaintData.process_status,handle_result:i.complaintData.handle_result||""},console.log("获取详细信息成功:",a.value)}catch(s){console.error("获取详细信息失败:",s),g.error("获取详细信息失败")}finally{p.value=!1}}function I(){w("update:visible")}async function F(){if(!i.complaintData){g.warning("数据异常，请重试");return}if(!a.value.process_status){g.warning("请选择处理状态");return}if(a.value.process_status==="已解决"&&!a.value.handle_result.trim()){g.warning("处理状态为已解决时，处理结果不能为空");return}try{p.value=!0,console.log("提交处理结果:",{id:i.complaintData.id,...a.value}),await new Promise(n=>setTimeout(n,1e3)),w("confirm",{handler:a.value.handler,handle_time:a.value.handle_time,process_status:a.value.process_status,handle_result:a.value.handle_result.trim()}),console.log("处理完成")}catch(n){console.error("处理失败:",n),g.error("处理失败，请重试")}finally{p.value=!1}}function D(){a.value={handler:"",handle_time:"",process_status:"",handle_result:""}}return z(()=>i.visible,n=>{n||D()}),(n,s)=>{const b=se,_=ie,$=ue,U=q,d=A,c=ce,h=J;return f(),j(ge,{visible:i.visible,title:"编辑投诉反馈",width:"600px","onUpdate:visible":I},{default:o(()=>[K((f(),k("div",ye,[e(c,{model:a.value,"label-width":"120px","label-position":"right"},{default:o(()=>[u("div",we,[e(_,{label:"案件编号："},{default:o(()=>{var l;return[e(b,{value:(l=n.complaintData)==null?void 0:l.case_number,disabled:"",class:"readonly-input"},null,8,["value"])]}),_:1}),e(_,{label:"反馈类型："},{default:o(()=>[e(b,{value:x.value,disabled:"",class:"readonly-input"},null,8,["value"])]),_:1}),e(_,{label:"具体类别："},{default:o(()=>{var l;return[e(b,{value:(l=n.complaintData)==null?void 0:l.category_cn,disabled:"",class:"readonly-input"},null,8,["value"])]}),_:1}),e(_,{label:"详细描述："},{default:o(()=>{var l;return[e(b,{type:"textarea",value:(l=n.complaintData)==null?void 0:l.description,disabled:"",rows:3,class:"readonly-textarea"},null,8,["value"])]}),_:1}),e(_,{label:"联系方式："},{default:o(()=>{var l;return[e(b,{value:(l=n.complaintData)==null?void 0:l.phone_number,disabled:"",class:"readonly-input"},null,8,["value"])]}),_:1})]),u("div",De,[e(_,{label:"处理人："},{default:o(()=>[e(b,{modelValue:a.value.handler,"onUpdate:modelValue":s[0]||(s[0]=l=>a.value.handler=l),placeholder:"请输入处理人"},null,8,["modelValue"])]),_:1}),e(_,{label:"处理时间："},{default:o(()=>[e($,{modelValue:a.value.handle_time,"onUpdate:modelValue":s[1]||(s[1]=l=>a.value.handle_time=l),type:"date",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",placeholder:"请选择处理时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(_,{label:"处理状态："},{default:o(()=>[e(d,{modelValue:a.value.process_status,"onUpdate:modelValue":s[2]||(s[2]=l=>a.value.process_status=l),placeholder:"请选择处理状态",style:{width:"100%"}},{default:o(()=>[(f(),k(N,null,B(V,l=>e(U,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"处理结果："},{default:o(()=>[e(b,{modelValue:a.value.handle_result,"onUpdate:modelValue":s[3]||(s[3]=l=>a.value.handle_result=l),type:"textarea",placeholder:"请输入处理结果",rows:4,maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"]),u("div",ke,[e(O,{onClick:F,height:34,loading:p.value,"btn-type":"blue"},{default:o(()=>[Ce,S("确认 ")]),_:1},8,["loading"]),e(O,{onClick:I,height:34},{default:o(()=>[Ve,S("取消 ")]),_:1})])])),[[h,p.value]])]),_:1},8,["visible"])}}});const xe=H(Ee,[["__scopeId","data-v-a72493ec"]]),W=v=>(Q("data-v-6816b05d"),v=v(),G(),v),Se={class:"complaint-feedback-page"},Ie={class:"search-header"},Te={class:"search-row"},Fe={class:"search-item"},$e={class:"search-item"},Ue=W(()=>u("label",null,"处理状态：",-1)),Ye={class:"search-item"},Me=W(()=>u("label",null,"反馈类型：",-1)),Ne={class:"table-container"},Be={class:"result-text"},ze={class:"operation-buttons"},Le=["onClick"],Oe={key:0,class:"pagination-wrapper"},M=10,Pe=P({__name:"complaintFeedback",setup(v){const w=m(!1),i=m([]),C=m(0),p=m(1),a=m(""),V=m(""),E=m(""),x=m(!1),y=m(null),I=[{label:"意见建议",value:"suggestion"},{label:"服务投诉",value:"complaint "}],F=[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已解决",value:"resolved"},{label:"已关闭",value:"closed"}];async function D(){w.value=!0;const d={page:p.value,page_size:M,search:a.value,process_status:V.value,feedback_type:E.value},{data:c}=await _e(d),{state:h,msg:l}=c;if(h=="success"){const{results:r,count:Y}=c.data;i.value=r,C.value=Y}else g.error(l);w.value=!1}function n(){p.value=1,D()}function s(d){p.value=d,D()}async function b(d,c){y.value=d,x.value=!0}function _(){x.value=!1,y.value=null}async function $(d){if(!y.value)return;const{data:c}=await me(d,y.value.id),{state:h,msg:l}=c;h=="success"?(g.success(l),D(),_()):g.error(l)}function U(d){return d?d.split("T")[0]:" "}return de(()=>{D()}),(d,c)=>{const h=q,l=A,r=he,Y=fe,X=ve,Z=be,ee=J;return f(),k("div",Se,[u("div",Ie,[u("div",Te,[u("div",Fe,[e(ae,{modelValue:a.value,"onUpdate:modelValue":c[0]||(c[0]=t=>a.value=t),placeholder:"搜索案件编号",onKeyup:re(n,["enter"]),onClick:n},null,8,["modelValue","onKeyup"])]),u("div",$e,[Ue,e(l,{modelValue:V.value,"onUpdate:modelValue":c[1]||(c[1]=t=>V.value=t),placeholder:"请选择处理状态",clearable:"",onChange:n},{default:o(()=>[(f(),k(N,null,B(F,t=>e(h,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),u("div",Ye,[Me,e(l,{modelValue:E.value,"onUpdate:modelValue":c[2]||(c[2]=t=>E.value=t),placeholder:"请选择反馈类型",clearable:"",onChange:n},{default:o(()=>[(f(),k(N,null,B(I,t=>e(h,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])])])]),u("div",Ne,[K((f(),j(X,{data:i.value,border:"",style:{width:"100%"},"cell-style":L(te),"header-cell-style":L(ne)},{default:o(()=>[e(r,{type:"index",label:"序号",width:"60",align:"center"},{default:o(({$index:t})=>[S(T((p.value-1)*M+t+1),1)]),_:1}),e(r,{prop:"case_number",label:"案件编号",width:"150",align:"center"}),e(r,{label:"反馈类型",width:"100",align:"center"},{default:o(({row:t})=>[e(Y,{type:t.feedback_type_cn==="服务投诉"?"danger":"primary",size:"small"},{default:o(()=>[S(T(t.feedback_type_cn),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"category_cn",label:"具体类别",width:"190",align:"center"}),e(r,{prop:"description",label:"详细描述","min-width":"200",align:"left"}),e(r,{prop:"phone_number",label:"联系方式",width:"130",align:"center"}),e(r,{prop:"process_status_cn",label:"处理状态",width:"100",align:"center"}),e(r,{prop:"handler",label:"处理人",width:"100",align:"center"}),e(r,{label:"处理时间",width:"120",align:"center"},{default:o(({row:t})=>[S(T(U(t.handle_time)),1)]),_:1}),e(r,{label:"处理结果","min-width":"150",align:"left"},{default:o(({row:t})=>[u("div",Be,T(t.handle_result),1)]),_:1}),e(r,{label:"操作",width:"100",align:"center",fixed:"right"},{default:o(({row:t,$index:le})=>[u("div",ze,[u("div",{class:"operation-btn edit-btn",onClick:je=>b(t,le)},"编辑",8,Le)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[ee,w.value]]),C.value>0?(f(),k("div",Oe,[e(Z,{background:"",layout:"prev, pager, next",total:C.value,"current-page":p.value,"page-size":M,onCurrentChange:s},null,8,["total","current-page"])])):pe("",!0)]),e(xe,{visible:x.value,"complaint-data":y.value,"onUpdate:visible":_,onConfirm:$},null,8,["visible","complaint-data"])])}}});const el=H(Pe,[["__scopeId","data-v-6816b05d"]]);export{el as default};
