<script lang="ts" setup>
import { ref, type Ref, watch, computed } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
import { ElMessage } from "element-plus";
import type { FieldType, PlanField, EditPlanParams, Plan, FieldTypeOption } from "../auth/type";
import { FieldType as FieldTypeEnum } from "../auth/type";

const props = defineProps<{
  showDialog: boolean,
  planData: Plan,  // 要编辑的方案数据
  currentMode?: 'mediationCases' | 'asset', // 当前模式
  mediationCaseOptions?: Array<{ label: string; value: string }>, // 调解案件选项
  assetPackageOptions?: Array<{ label: string; value: string }> // 资产包选项
}>()

const emit = defineEmits<{
  (e: 'close'): void,
  (e: 'ensure', params: EditPlanParams): void
}>()

// 表单引用
const planFormRef = ref()
const loading = ref(false)

// 方案基础信息
const planForm = ref({
  asset_package: null, // 资产包下拉框
  mediation_case: null, // 调解案件下拉框
  plan_name: '', // 方案名称
})

// 动态字段列表（编辑模式下支持新增、删除和修改）
const dynamicFields: Ref<PlanField[]> = ref([])

// 字段类型选项配置
const fieldTypeOptions: FieldTypeOption[] = [
  { label: '文本输入', value: FieldTypeEnum.TEXTAREA, icon: 'jt-24-edit' },
  { label: '日期选择', value: FieldTypeEnum.DATE, icon: 'jt-24-calendar' },
  { label: '金额输入', value: FieldTypeEnum.AMOUNT, icon: 'jt-24-money' },
]

// 计算当前模式，默认为调解案件模式
const currentMode = computed(() => props.currentMode || 'mediationCases')

// 计算对话框标题
const dialogTitle = computed(() => {
  return currentMode.value === 'asset' ? '编辑资产包方案' : '编辑调解案件方案'
})

// 表单验证规则
const rules = computed(() => {
  return {
    plan_name: [
      { required: true, message: '请输入方案名称', trigger: 'blur' },
    ]
  }

  /* if (currentMode.value === 'asset') {
    return {
      ...baseRules,
      asset_package: [
        { required: true, message: '请选择资产包', trigger: 'change' }
      ]
    }
  } else {
    return {
      ...baseRules,
      mediation_case: [
        { required: true, message: '请选择调解案件', trigger: 'change' }
      ]
    }
  } */
})

// 监听弹框显示状态
watch(() => props.showDialog, onOpenDialog)

// 监听方案数据变化
watch(() => props.planData, loadPlanData, { deep: true })

// 弹框打开时加载数据
function onOpenDialog(newVal: boolean) {
  if(newVal) {
    loadPlanData(props.planData)
  }
}

// 加载方案数据
function loadPlanData(planData: Plan) {
  if (!planData) return
  if (currentMode.value === 'asset') {
    // 资产包模式：设置资产包ID
    planForm.value.asset_package = planData.asset_package
  } else {
    // 调解案件模式：设置调解案件ID
    planForm.value.mediation_case = planData.mediation_case
  }
  
  // 设置方案名称
  planForm.value.plan_name = planData.plan_name

  // 深拷贝字段数据，已存在的字段默认设为必填
  dynamicFields.value = planData.plan_config ? JSON.parse(JSON.stringify(planData.plan_config)).map((field: PlanField) => {
    // 已创建的字段默认设为必填
    field.required = true
    // 修复InvalidCharacterError: 确保字段ID为有效字符串
    if (!field.id || typeof field.id !== 'string' || field.id.trim() === '') {
      field.id = generateFieldId()
    }
    return field
  }) : []
}

// 关闭弹框
function close() {
  emit('close')
}

// 生成唯一字段ID - 修复InvalidCharacterError: 确保ID始终为有效字符串
function generateFieldId(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 11)
  return `field_${timestamp}_${random}`
}

// 添加新字段
function addField() {
  const newField: PlanField = {
    id: generateFieldId(),
    title: '',
    type: FieldTypeEnum.TEXTAREA,
    value: '',
    required: true, // 新增字段也默认设为必填
  }
  dynamicFields.value.push(newField)
}

// 删除字段
function removeField(index: number) {
  dynamicFields.value.splice(index, 1)
}

// 字段类型改变处理
function onFieldTypeChange(field: PlanField) {
  // 重置字段值
  field.value = getDefaultValueByType(field.type || FieldTypeEnum.TEXTAREA)
}

// 根据字段类型获取默认值
function getDefaultValueByType(type: FieldType): any {
  switch (type) {
    case FieldTypeEnum.TEXTAREA:
      return ''
    case FieldTypeEnum.DATE:
      return ''
    case FieldTypeEnum.AMOUNT:
      return 0
    default:
      return ''
  }
}
/* 
// 生成微信小程序渲染格式的JSON数据
function generateWechatRenderData(): string {
  const planTitle = planForm.value.plan_name

  const renderData = {
    planTitle,
    sections: dynamicFields.value.map(field => ({
      title: field.title,
      content: formatFieldValueForRender(field),
      type: field.type,
    }))
  }
  return JSON.stringify(renderData, null, 2)
}

// 格式化字段值用于渲染
function formatFieldValueForRender(field: PlanField): string {
  switch (field.type) {
    case FieldTypeEnum.DATE:
      return field.value ? new Date(field.value).toLocaleDateString() : ''
    case FieldTypeEnum.AMOUNT:
      return field.value ? `¥${Number(field.value).toFixed(2)}` : '¥0.00'
    default:
      return String(field.value || '')
  }
} */

// 验证动态字段
function validateDynamicFields(): boolean {
  for (let i = 0; i < dynamicFields.value.length; i++) {
    const field = dynamicFields.value[i]
    
    if (!field.title.trim()) {
      ElMessage.error(`第${i + 1}个字段的标题不能为空`)
      return false
    }
    
    /* if (field.required) {
      if (!field.value || String(field.value).trim() === '') {
        ElMessage.error(`${field.title}是必填字段，请填写内容`)
        return false
      }
    } */
  }
  return true
}

// 确认编辑方案
async function ensureEdit() {
  if (!planFormRef.value) return
  
  loading.value = true
  try {
    // 验证基础表单
    const isValidForm = await new Promise((resolve) => {
      planFormRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValidForm) return
    
    // 验证动态字段
    if (!validateDynamicFields()) return
    
    // 检查是否至少有一个字段
    if (dynamicFields.value.length === 0) {
      ElMessage.error('请至少保留一个字段')
      return
    }
    
    // 构造提交数据
    const params: EditPlanParams = {
      id: props.planData.id!,
      plan_name: planForm.value.plan_name,
      plan_config: dynamicFields.value,
      // jsonData: generateWechatRenderData(),
      // 根据模式添加可选参数
      ...(currentMode.value === 'asset' && {
        asset_package: planForm.value.asset_package
      }),
      ...(currentMode.value === 'mediationCases' && {
        mediation_case: planForm.value.mediation_case
      })
    }
    console.log(params,'===编辑弹框保存参数')
    emit('ensure', params)
  } finally {
    loading.value = false
  }
}

// 获取字段类型标签
/* function getFieldTypeLabel(type?: FieldType): string {
  const option = fieldTypeOptions.find(opt => opt.value === type)
  return option?.label || '未知类型'
} 

// 处理文件变更
function handleFileChange(file: File, field: PlanField) {
  if (!field.fileList) {
    field.fileList = []
  }

  const uploadFile = {
    id: Date.now().toString(),
    name: file.name,
    size: file.size,
    type: file.type,
    file: file,
    status: 'success' as const
  }

  field.fileList.push(uploadFile)
}

// 移除文件
function removeFile(field: PlanField, fileIndex: number) {
  if (field.fileList) {
    field.fileList.splice(fileIndex, 1)
  }
}*/
</script>

<template>
  <CustomDialog :visible="showDialog" @update:visible="close" width="1200px" :title="dialogTitle">
    <div class="edit-plan-content">
      <el-form ref="planFormRef" :model="planForm" :rules="rules" label-width="110px">
        <!-- 资产包模式：资产包下拉框 -->
        <el-form-item v-if="currentMode === 'asset'" label="资产包" prop="asset_package">
          <el-select
            v-model="planForm.asset_package"
            filterable
            placeholder="请选择资产包"
            style="width: 100%">
            <el-option
              v-for="option in assetPackageOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>

        <!-- 调解案件模式：调解案件下拉框 -->
        <el-form-item v-else label="调解案件" prop="mediation_case">
          <el-select
            v-model="planForm.mediation_case"
            filterable
            placeholder="请选择调解案件"
            style="width: 100%">
            <el-option
              v-for="option in mediationCaseOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>

        <!-- 方案名称输入框 -->
        <el-form-item label="方案名称" prop="plan_name">
          <el-input
            v-model="planForm.plan_name"
            placeholder="例如方案一、方案二"
            maxlength="50"
            show-word-limit />
        </el-form-item>
      </el-form>
      <div class="dynamic-fields-section">
        <div class="section-header">
          <h3>字段配置</h3>
          <CustomButton @click="addField" :height="34" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>
        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div
            v-for="(field, index) in dynamicFields"
            :key="field.id || `field-${index}`"
            class="field-item">
            
            <!-- 字段头部：显示序号和删除按钮 -->
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton 
                @click="removeField(index)" 
                :height="32" 
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>

            <!-- 字段配置 -->
            <div class="field-config">
              <!-- 字段类型 -->
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select
                  v-model="field.type"
                  @change="onFieldTypeChange(field)"
                  style="width: 280px;">
                  <el-option
                    v-for="option in fieldTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                    <span style="display: flex; align-items: center;">
                      <i :class="option.icon" style="margin-right: 8px;"></i>
                      {{ option.label }}
                    </span>
                  </el-option>
                </el-select>
              </div>

              <!-- 字段标题 -->
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <el-input
                  v-model="field.title"
                  placeholder="请输入字段标题"
                  style="width: 280px;" />
              </div>
            </div>

            <!-- 字段内容预览和编辑区域 -->
            <div class="field-preview">
              <label class="config-label">
                内容预览：
                <!-- 显示字段类型 -->
                <!-- <el-tag size="small" type="info">
                  {{ getFieldTypeLabel(field.type) }}
                </el-tag> -->
              </label>
              
              <div class="preview-content">
                <!-- 文本输入 -->
                <el-input
                  v-if="field.type === FieldTypeEnum.TEXTAREA"
                  v-model="field.value"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入文本内容" />

                <!-- 日期选择器 -->
                <el-date-picker
                  v-else-if="field.type === FieldTypeEnum.DATE"
                  v-model="field.value"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />

                <!-- 金额输入框 -->
                <el-input-number
                  v-else-if="field.type === FieldTypeEnum.AMOUNT"
                  v-model="field.value"
                  :min="0"
                  :precision="2"
                  placeholder="请输入金额"
                  style="width: 200px;" />
                
                <!-- 文件上传区域 -->
                <!-- <div v-if="field.type === FieldTypeEnum.FILE" class="file-upload-area">
                  <el-upload
                    :auto-upload="false"
                    :on-change="(file) => handleFileChange(file.raw, field)"
                    :show-file-list="false"
                    multiple>
                    <CustomButton :height="32">
                      <i class="jt-24-upload"></i>选择文件
                    </CustomButton>
                  </el-upload>
                  
                  <div v-if="field.fileList && field.fileList.length > 0" class="file-list">
                    <div 
                      v-for="(file, fileIndex) in field.fileList" 
                      :key="file.id"
                      class="file-item">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-size" v-if="file.size">({{ (file.size / 1024).toFixed(1) }}KB)</span>
                      <i 
                        class="jt-20-delete file-remove" 
                        @click="removeFile(field, fileIndex)"></i>
                    </div>
                  </div>
                </div> -->
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-fields">
          <p>还没有添加任何字段，点击"添加字段"开始配置方案内容</p>
        </div>
      </div>
      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-plan-content {
  .dynamic-fields-section {
    margin-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .empty-fields {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      border-radius: 8px;
      border: 1px dashed #ddd;
    }
  }
  
  .fields-list {
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }
      
      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 10px;
        
        .config-row {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        
        .config-label {
          min-width: 90px;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
      
      .field-preview {
        .config-label {
          display: block;
          margin-bottom: 12px;
          font-weight: bold;
          color: #333;
          font-size: 15px;
          
          .el-tag {
            margin-left: 8px;
          }
        }
        
        .preview-content {
          .file-upload-area {
            .file-list {
              margin-top: 12px;
              max-height: 200px;
              overflow-y: auto;
              
              .file-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                background-color: #fff;
                border: 1px solid #e6e6e6;
                border-radius: 6px;
                margin-bottom: 8px;
                transition: all 0.2s ease;
                
                &:hover {
                  background-color: #f9f9f9;
                  border-color: #409eff;
                }
                
                .file-name {
                  flex: 1;
                  color: #333;
                  font-weight: 500;
                }
                
                .file-size {
                  color: #999;
                  font-size: 12px;
                  background-color: #f0f0f0;
                  padding: 2px 8px;
                  border-radius: 12px;
                }
                
                .file-remove {
                  color: #f56c6c;
                  cursor: pointer;
                  padding: 4px;
                  border-radius: 50%;
                  transition: all 0.2s ease;
                  
                  &:hover {
                    color: #fff;
                    background-color: #f56c6c;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .edit-plan-content {
    .dynamic-fields-section {
      .fields-list {
        .field-item {
          .field-config {
            grid-template-columns: 1fr;
          }
        }
      }
    }
    
    .btns-group {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>
