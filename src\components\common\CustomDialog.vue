<template>
  <div class="pack_dialog" v-if="props.visible" @click="props.markclose ? close : null" tabindex="-1">
    <div class="dialog_wrap" :style="{ width: props.width }">
      <div class="dialog_title">{{ props.title }}</div>
      <div class="dialog_close" @click="close"  @keyup.esc="close">
        <i class="jt-48-close" tabindex="0"></i>
      </div>
      <div class="dialog_container">
        <slot></slot>
      </div>
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';

type CustomDialogProps = {
  visible?: boolean,
  title?: string,
  width?: string,
  markclose?: boolean,
}

const props = withDefaults(defineProps<CustomDialogProps>(), {
  visible: false,
  title: '标题',
  width: '380px',
  markclose: true
})


const emit = defineEmits<{
  (e: 'update:visible'): void
}>()

watch(() => props.visible, (newValue) => {
  if(newValue) {
    window.addEventListener('keyup', closeByEsc)
  }
})

function close() {
  emit('update:visible')
}

function closeByEsc(e: KeyboardEvent) {
  if(e.key === 'Escape' || e.keyCode === 27) {
    close()
    window.removeEventListener('keyup', closeByEsc)
  }
}
</script>

<style lang="scss">
.pack_dialog {
  position: fixed;
  z-index: 99;
  top: 0px;
  left: 0px;
  bottom: 0px;
  right: 0px;
  background: #00000033;
  display: flex;
  align-items: center;
  justify-content: center;

  .dialog_wrap {
    position: absolute;
    padding: 0px 6px 6px;
    
    background-image: linear-gradient(180deg, 
		#f9fcff 0%, 
		#a7dcff 100%);
    border-radius: 4px;
    border: solid 1px #0d457a;
  }
  .dialog_container {
    background: #fff;
    border: 1px solid #8496ac;
    border-radius: 4px;
    padding: 24px;
    max-height: 78vh;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .dialog_title {
    color: #0d457a;
    height: 25px;
    line-height: 26px;
    overflow: hidden;
    font-size: 16px;
    text-align: left;
  }
  .dialog_close {
    position: absolute;
    top: -1px;
    right: 16px;
    cursor: pointer;
  }
  .el-input {
    height: 36px !important;
  }
}
</style>