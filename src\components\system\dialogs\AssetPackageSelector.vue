<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import { ElMessage } from 'element-plus'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import { getDataImportList, previewMappedData } from '@/axios/system'

/**
 * 文件信息接口
 */
interface FileInfo {
  id: string
  name: string
  description?: string
}

/**
 * 表格数据接口
 */
interface TableRow {
  [key: string]: any
}

/**
 * 表头信息接口
 */
interface TableHeader {
  prop: string
  label: string
  width?: number
}

/**
 * 选择结果接口
 */
interface SelectionResult {
  fileName: string
  selectedRow: TableRow
  headers: TableHeader[]
  // 新增字段
  asset_package: number | null
  creditor_name: string
  creditor: number | null
  asset_package_row_number: number
}

// 组件属性
const props = defineProps<{
  visible: boolean
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', result: SelectionResult): void
}>()

// 响应式数据
const tableLoading = ref(false)
const selectedFileName = ref('')
const searchText = ref('')
const selectedRow = ref<TableRow | null>(null)
const tableData = ref<TableRow[]>([])
const tableHeaders = ref<TableHeader[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

/**
 * 资产包列表数据
 */
const fileList = ref<FileInfo[]>([])

/**
 * 完整的资产包数据（用于获取creditor信息）
 */
const fullAssetPackageData = ref<any[]>([])

/**
 * 获取资产包列表
 */
async function getAssetPackageList() {
  // package_status (字符串, 可选): 资产包状态，可选值：available(可用)、unavailable(不可用)
  const { data } = await getDataImportList({ page: 1, page_size: 1000, package_status:'available' })
  const { state, msg } = data

  if (state === 'success') {
    // 存储完整数据用于后续获取creditor信息
    fullAssetPackageData.value = data.data.results

    fileList.value = data.data.results.map((item: any) => ({
      id: item.id.toString(),
      name: item.package_name,
    }))
  } else {
    ElMessage.error(msg)
  }
}

/**
 * 过滤后的文件列表（支持搜索）
 */
const filteredFileList = computed(() => {
  if (!searchText.value.trim()) {
    return fileList.value
  }
  return fileList.value.filter(file => 
    file.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

/**
 * 分页后的表格数据
 */
const paginatedTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})

/**
 * 监听文件选择变化，获取对应的表格数据
 */
watch(selectedFileName, async (newFileName) => {
  if (newFileName) {
    await fetchTableData(newFileName)
  } else {
    resetTableData()
  }
})

/**
 * 监听弹框显示状态
 */
watch(() => props.visible, (visible) => {
  if (visible) {
    // 弹框打开时加载资产包列表
    getAssetPackageList()
  } else {
    resetForm()
  }
})

/**
 * 重置表单数据
 */
function resetForm() {
  selectedFileName.value = ''
  searchText.value = ''
  selectedRow.value = null
  resetTableData()
}

/**
 * 重置表格数据
 */
function resetTableData() {
  tableData.value = []
  tableHeaders.value = []
  currentPage.value = 1
  total.value = 0
}

/**
 * 根据资产包ID获取预览数据
 */
async function fetchTableData(fileName: string) {
  tableLoading.value = true
 
  // 根据资产包名称找到对应的资产包ID
  const selectedFile = fileList.value.find(file => file.name === fileName)
  if (!selectedFile) {
    ElMessage.error('未找到对应的资产包')
    return
  }

  // 调用预览数据接口
  const response = await previewMappedData(Number(selectedFile.id))
  const { state } = response.data
  if (state === 'success') {
    const previewData = response.data.data
    if (previewData.columns && Array.isArray(previewData.columns)) {
      tableHeaders.value = previewData.columns.map((col: any) => ({
        prop: col.key,  // 使用column.key作为prop，参考DataPreviewDialog
        label: col.label || col.key, // 优先使用label，如果没有则使用key
        width: col.width || 0
      }))
    } else {
      tableHeaders.value = []
    }

    // 设置表格数据 - 确保数据结构正确
    if (previewData.data && Array.isArray(previewData.data)) {
      tableData.value = previewData.data
      total.value = previewData.data.length
    } else {
      tableData.value = []
      total.value = 0
    }

    currentPage.value = 1
    selectedRow.value = null
  } else {
    ElMessage.error(response.data.msg || '数据加载失败')
    resetTableData()
  }
  tableLoading.value = false
}



/**
 * 处理表格行选择 - 通过 radio 按钮
 */
function handleRowSelect(row: TableRow) {
  selectedRow.value = row
}

/**
 * 处理表格行点击 - 保持原有的行点击功能
 */
function handleRowClick(row: TableRow) {
  // 检查行是否可选择
  if (selectable(row)) {
    selectedRow.value = row
  }
}

/**
 * 判断行是否被选中
 */
function isRowSelected(row: TableRow): boolean {
  return selectedRow.value === row
}

/**
 * 判断行是否可以被选择 - 可以根据业务需求自定义
 */
function selectable(_row: TableRow): boolean {
  // 默认所有行都可选择，可以根据具体业务需求添加条件
  // 例如：return row.status === 'active'
  return true
}

/**
 * 处理分页变化
 */
function handlePageChange(page: number) {
  currentPage.value = page
}

/**
 * 关闭弹框
 */
function closeDialog() {
  emit('update:visible', false)
}

/**
 * 确认选择
 */
function confirmSelection() {
  if (!selectedFileName.value) {
    ElMessage.error('请选择资产包名称')
    return
  }

  if (!selectedRow.value) {
    ElMessage.error('请选择表格中的一行数据')
    return
  }

  // 根据选中的资产包名称获取对应的creditor信息
  const selectedAssetPackage = fullAssetPackageData.value.find(
    item => item.package_name === selectedFileName.value
  )

  // 获取选中行在当前页面中的序号
  const selectedRowIndex = paginatedTableData.value.findIndex(row => row === selectedRow.value)
  const asset_package_row_number = (currentPage.value - 1) * pageSize.value + selectedRowIndex + 1

  // 构造选择结果
  const result: SelectionResult = {
    fileName: selectedFileName.value,
    selectedRow: selectedRow.value,
    headers: tableHeaders.value,
    // 添加新的字段
    asset_package: selectedAssetPackage?.id || null,
    creditor_name: selectedAssetPackage?.creditor_name || '',
    creditor: selectedAssetPackage?.creditor || null,
    asset_package_row_number: asset_package_row_number
  }

  console.log('选择结果:', result)
  emit('confirm', result)
  emit('update:visible', false)
}
</script>

<template>
  <CustomDialog 
    :visible="visible" 
    @update:visible="closeDialog"
    width="1200px" 
    title="选择资产信息">
    <div class="asset-selector-content">
      <div class="file-selection-area">
        <div class="selection-row">
          <label class="selection-label">资产包名称：</label>
          <el-select
            v-model="selectedFileName"
            filterable
            placeholder="请选择资产包名称"
            style="width: 400px;">
            <el-option
              v-for="file in filteredFileList"
              :key="file.id"
              :label="file.name"
              :value="file.name">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="table-area" v-if="selectedFileName">
        <el-table
          :data="paginatedTableData"
          v-loading="tableLoading"
          border
          highlight-current-row
          :cell-style="cellStyle"
          :header-cell-style="headerCellStyle"
          @row-click="handleRowClick"
          style="width: 100%; margin-bottom: 20px;"
          :row-class-name="({ row }) => isRowSelected(row) ? 'selected-row' : ''">

          <!-- 单选列 -->
          <el-table-column label="选择" width="65" align="center">
            <template #default="{ row }">
              <el-radio
                :model-value="isRowSelected(row)"
                :disabled="!selectable(row)"
                @change="() => handleRowSelect(row)"
                :label="true">
                <span></span>
              </el-radio>
            </template>
          </el-table-column>

          <el-table-column
            type="index"
            label="序号"
            :index="(index) => (currentPage - 1) * pageSize + index + 1"
            width="85"
            align="center" />

          <el-table-column
            v-for="header in tableHeaders"
            :key="header.prop"
            :prop="header.prop"
            :label="header.label"
            :width="header.width"
            align="center" />
        </el-table>
        <div class="pagination-wrapper" v-if="total > pageSize">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :current-page="currentPage"
            :page-size="pageSize"
            @current-change="handlePageChange" />
        </div>
        <div class="dialog-footer">
          <CustomButton @click="confirmSelection" :height="34" btn-type="blue">
            <i class="jt-20-ensure"></i>确认
          </CustomButton>
          <CustomButton @click="closeDialog" :height="34">
            <i class="jt-20-delete"></i>取消
          </CustomButton>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <p>请先选择资产包名称查看数据</p>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.asset-selector-content {
  min-height: 500px;
  
  .file-selection-area {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .selection-row {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .selection-label {
        min-width: 80px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  
  .table-area {

    :deep(.selected-row) {
      background-color: #a7d8ee !important;

      &:hover {
        background-color: #079eef !important;
      }
    }

    :deep(.el-table__row) {
      cursor: pointer;
    }

    // Radio 按钮样式优化
    :deep(.el-radio) {
      .el-radio__input {
        &.is-checked {
          .el-radio__inner {
            background-color: #409eff;
            border-color: #409eff;
          }
        }

        &.is-disabled {
          .el-radio__inner {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            cursor: not-allowed;
          }
        }
      }

      .el-radio__label {
        display: none; // 隐藏 radio 标签文本
      }
    }
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
  }
  
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #999;
    font-size: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #ddd;
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 26px 0 8px 0;
}
</style> 