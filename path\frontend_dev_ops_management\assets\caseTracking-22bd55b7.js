/* empty css             */import{C as x,h as E,c as L}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{a as T,r as d,z as V,o as l,f as r,g as i,h as a,I as S,J as B,q as D,w as h,n as y,O as K,E as M,K as N,L as P,M as I,F as w,A as k,t as v,D as U,N as F}from"./index-8a4876d8.js";import{_ as q}from"./_plugin-vue_export-helper-c27b6911.js";const A={class:"case-tracking"},J={class:"search-area"},O={class:"search-form"},j={class:"table-container"},G={class:"fields-preview"},H={class:"field-types"},Q=["title"],R={key:0,class:"more-fields"},W={class:"file-name"},X={class:"pagination-container"},Y=T({__name:"caseTracking",setup(Z){const t=d({search:"",page:1,page_size:10}),p=d(!1),f=d([]),b=d(0);function C(){t.value.page=1,_()}async function _(){p.value=!0;const c={page:t.value.page,page_size:t.value.page_size,search:t.value.search},{data:n}=await K(c),{state:e,msg:u}=n;if(e==="success"){const{results:g,count:m}=n.data;f.value=g,b.value=m}else M.error(u);p.value=!1}function z(c){t.value.page=c,_()}return V(()=>{_()}),(c,n)=>{const e=F,u=N,g=P,m=I;return l(),r("div",A,[i("div",J,[i("div",O,[a(x,{modelValue:t.value.search,"onUpdate:modelValue":n[0]||(n[0]=s=>t.value.search=s),placeholder:"请输入案件号、债务人、调解员或调解信息",class:"search-input",onKeydown:S(C,["enter"])},null,8,["modelValue","onKeydown"])])]),i("div",j,[B((l(),D(u,{data:f.value,style:{width:"100%"},"header-cell-style":y(E),"cell-style":y(L),border:"",stripe:""},{default:h(()=>[a(e,{prop:"case_number",label:"调解案件号",align:"center",width:"180"}),a(e,{prop:"case_status_cn",label:"案件状态",align:"center",width:"150"}),a(e,{prop:"creditor_name",label:"债权人",align:"center",width:"150"}),a(e,{prop:"debtor_name",label:"债务人",align:"center",width:"150"}),a(e,{prop:"mediator_name",label:"调解员",align:"center",width:"150"}),a(e,{prop:"asset_package_name",label:"资产包名称",align:"center",width:"150"}),a(e,{prop:"mediation_agreement_name",label:"调解协议",align:"center",width:"150"}),a(e,{prop:"notarization_status_cn",label:"协议公证状态",align:"center",width:"150"}),a(e,{label:"调解信息配置",align:"center","min-width":"180"},{default:h(({row:s})=>[i("div",G,[i("div",H,[(l(!0),r(w,null,k(s.mediation_config.slice(0,3),o=>(l(),r("span",{key:o.id,class:"field-type-tag",title:o.title},v(o.title),9,Q))),128)),s.mediation_config.length>3?(l(),r("span",R," +"+v(s.fields.length-3),1)):U("",!0)])])]),_:1}),a(e,{prop:"fileList",label:"相关文件",align:"center","min-width":"100"},{default:h(({row:s})=>[(l(!0),r(w,null,k(s.fileList,o=>(l(),r("div",{class:"file-list",key:o.id},[i("span",W,v(o.name),1)]))),128))]),_:1})]),_:1},8,["data","header-cell-style","cell-style"])),[[m,p.value]])]),i("div",X,[a(g,{class:"page",background:"","current-page":t.value.page,"onUpdate:currentPage":n[1]||(n[1]=s=>t.value.page=s),"page-size":t.value.page_size,"onUpdate:pageSize":n[2]||(n[2]=s=>t.value.page_size=s),total:b.value,layout:"prev, pager, next",onCurrentChange:z},null,8,["current-page","page-size","total"])])])}}});const le=q(Y,[["__scopeId","data-v-fc169da7"]]);export{le as default};
