<script lang="ts" setup>
import { RouterView } from "vue-router"
import HeaderView from "./HeaderView.vue"
// import MenuView from "./MenuView.vue"
</script>

<template>
  <div class="home-view">
    <HeaderView></HeaderView>
    <!-- <MenuView></MenuView> -->
    <RouterView class="content-height"></RouterView>
  </div>
</template>

<style lang="scss" scoped>
.home-view {
  font-size: 16px;
  .content-height {
    min-height: calc(100vh - 56px);
  }
}
</style>