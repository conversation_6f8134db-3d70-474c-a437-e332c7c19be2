// 字段类型枚举
export enum FieldType {
  TEXT = 'text',           // 单行文本
  TEXTAREA = 'textarea',   // 多行文本
  DATE = 'date',          // 日期
  AMOUNT = 'amount',      // 金额
  FILE = 'file'           // 文件
}

// 案件状态枚举
/* export enum CaseStatus {
  DRAFT = 'draft',         // 待发起
  INITIATED = 'initiated', // 已发起
  PENDING_CONFIRM = 'pending_confirm', // 待确认
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed',   // 已完成
  CLOSED = 'closed'         // 已关闭
} */

// 案件状态选项
export interface CaseStatusOption {
  label: string;
  value: string;
  color?: string;
}

// 动态字段接口
export interface PlanField {
  id: string;              // 字段唯一标识
  title: string;           // 字段标题
  type?: FieldType;        // 字段类型枚举
  logic_type?: string;     // 逻辑处理类型
  expression?: any;        // 表达式编辑
  preview?: any;           // 内容预览
}

// 文件上传接口
export interface UploadFile {
  id?: string;             // 文件ID
  file_name: string;            // 文件名
  file?: File;            // 文件URL（用于预览和下载）
}

// 方案接口
export interface Plan {
  id?: string;             // 方案ID
  plan_name: string;       // 方案名称
  title?: string;          // 方案标题（兼容字段）
  asset_package?: string;  // 资产包
  mediation_case?: string; // 调解案件
  caseNumber?: string;     // 案件号（资产包模式使用）
  createTime?: string;     // 创建时间
  updateTime?: string;     // 更新时间
  plan_config: PlanField[]; // 动态字段列表
  fields?: PlanField[];     // 动态字段列表（兼容字段）
  jsonData?: string;       // 序列化的JSON数据
  caseStatus?: string;     // 案件状态
  approvalStatus?: string; // 审批状态
  plan_status_cn?: string;   // 方案状态
}

// 方案查询参数
export interface PlanParams {
  ordering?: string;
  search?: string;
  page: number;
  page_size: number;
}

// 新增方案参数
export interface AddPlanParams {
  plan_name: string;           // 方案名称
  plan_config: PlanField[];    // 字段配置
  asset_package?: string;      // 资产包（可选）
  mediation_case?: string;     // 调解案件（可选）
}

// 编辑方案参数
export interface EditPlanParams {
  id: string;                  // 方案ID
  plan_name: string;           // 方案名称
  plan_config: PlanField[];    // 字段配置
  asset_package?: string;      // 资产包（可选）
  mediation_case?: string;     // 调解案件（可选）
}

// 微信小程序渲染格式
export interface WechatRenderData {
  planId: number;
  planTitle: string;
  sections: {
    title: string;          // 标题行
    content: string;        // 内容行
    type: FieldType;        // 内容类型
    fileList?: string[];    // 文件名列表（仅文件类型）
  }[];
}

// 字段类型选项
export interface FieldTypeOption {
  label: string;
  value: FieldType;
  icon: string;
}

// 调解信息字段接口
export interface MediationField {
  id: string;              // 字段唯一标识
  title: string;           // 字段标题
  type: FieldType;         // 字段类型
  expression: any;          // 表达式编辑
  preview:any;              // 内容预览
  logic_type: string;       // 逻辑处理类型
}

// 调解信息接口
export interface Mediation {
  id?: string;             // 方案ID
  asset_package_name: string;           // 资产包名称
  mediation_config: MediationField[];     // 动态字段列表
  // 固定字段
  fileList?: UploadFile[]; // 相关文件列表
}

// 调解信息查询参数
export interface MediationParams {
  search?: string;
  page: number;
  page_size: number;
}

// 新增调解方案参数
export interface AddMediationParams {
  asset_package?: number;         // 资产包id
  asset_package_name: string;   // 资产包名称
  mediation_config: MediationField[]; //字段配置
  // 固定字段
  file?: UploadFile[]; // 相关文件列表
  creditor?: number;         // 债权人
  debtor?: number;         // 债务人
}

// 编辑调解方案参数
export interface EditMediationParams {
  id: string;
  asset_package_name: string;
  mediation_config: MediationField[];
  // 固定字段
  caseStatus: string;  // 案件状态
  fileList?: UploadFile[]; // 相关文件列表
  remark?: string;         // 备注
  // FormData相关
  formData?: FormData;     // 用于文件上传的FormData对象
}

// 案件跟踪状态枚举
export enum CaseTrackingStatus {
  TO_INITIATE = 'to_initiate',     // 待发起
  INITIATED = 'initiated',         // 已发起
  TO_CONFIRM = 'to_confirm',       // 待确认
  IN_PROGRESS = 'in_progress',     // 进行中
  COMPLETED = 'completed',         // 已完成
  CLOSED = 'closed'               // 已关闭
}

// 案件跟踪状态选项（移除color属性）
export interface CaseTrackingStatusOption {
  label: string;
  value: CaseTrackingStatus;
}

// 案件跟踪数据接口
export interface CaseTracking {
  id: string;                      // 案件ID
  caseNumber: string;              // 调解案件号
  status: CaseTrackingStatus;      // 案件状态
  debtor: string;                  // 债务人
  mediator: string;                // 调解员
  mediationInfo: string;           // 调解信息
  relatedFiles: UploadFile[];      // 相关文件
  mediationPlan: string;           // 调解方案
  initiateDate: string;            // 发起日期
  closeDate?: string;              // 关闭日期
}

// 案件跟踪查询参数
export interface CaseTrackingParams {
  ordering?: string;
  search?: string;
  page: number;
  page_size: number;
}

// 人员调度
export interface PersonnelDispatch {
  id: string;                      // 案件ID
  caseNumber: string;              // 调解案件号
  status: CaseTrackingStatus;      // 案件状态
  mediator: string;                // 调解员
}
// 人员调度查询参数
export interface PersonnelDispatchParams {
  ordering?: string;
  search?: string;
  page: number;
  page_size: number;
}

// 债权人类型枚举
/* export enum CreditorType {
  BANK = 'bank',                           // 银行
  FINANCIAL_AMC = 'financial_amc',         // 金融资产管理公司
  CONSUMER_FINANCE = 'consumer_finance',   // 消费金融公司
  AUTO_FINANCE = 'auto_finance',           // 汽车金融公司
  FINANCIAL_LEASE = 'financial_lease',     // 金融租赁公司
  MICROCREDIT = 'microcredit',            // 小额贷款公司
  GUARANTEE = 'guarantee',                 // 融资担保公司
  FINANCING_LEASE = 'financing_lease',     // 融资租赁公司
  LOCAL_AMC = 'local_amc',                // 地方资产管理公司
  LENDING_PLATFORM = 'lending_platform',  // 助贷机构
  INSURANCE = 'insurance',                 // 保险公司
  TECHNOLOGY = 'technology',               // 科技公司
  INDIVIDUAL = 'individual',               // 自然人
  OTHER = 'other'                         // 其他
}

// 证件类型枚举
export enum CertificateType {
  ID_CARD = 'id_card',         // 身份证
  PASSPORT = 'passport',       // 护照
  BUSINESS_LICENSE = 'business_license'  // 营业执照
} */

// 联系方式接口 - 电话
export interface PhoneInfo {
  phone: string               // 电话号码
  phone_type?: string         // 电话类型（默认为空）
  is_primary: boolean         // 是否为主要联系方式
}

// 联系方式接口 - 邮箱
export interface EmailInfo {
  email: string               // 邮箱地址
  email_type?: string         // 邮箱类型（默认为空）
  is_primary: boolean         // 是否为主要联系方式
}

// 联系方式接口 - 地址
export interface AddressInfo {
  address: string             // 地址
  address_type?: string       // 地址类型（默认为空）
  is_primary: boolean         // 是否为主要联系方式
}

// 联系方式接口 - 微信
export interface WechatInfo {
  wechat: string             // 微信
  wechat_type?: string       // 微信类型（默认为空）
  is_primary: boolean         // 是否为主要联系方式
}

// 债权人数据接口
export interface Creditor {
  id?: number                  // 债权人ID
  creditor_type: string          // 类型
  name: string                // 姓名
  id_type: string  // 证件类型
  id_number: string  // 证件号码
  phones?: PhoneInfo[]        // 联系方式-电话列表
  emails?: EmailInfo[]        // 联系方式-邮箱列表
  addresses?: AddressInfo[]   // 联系方式-地址列表
}

// 债权人类型选项
export interface CreditorTypeOption {
  label: string
  value: string
}

// 证件类型选项
export interface CertificateTypeOption {
  label: string
  value: string
}


// 债权人查询参数
export interface CreditorParams {
  search?: string
  creditor_type?: string         // 债权人类型筛选
  id_type?: string  // 证件类型筛选
  page: number
  page_size: number
}

// 新增债权人参数
export interface AddCreditorParams {
  creditor_type: string
  name: string
  id_type: string
  id_number: string
  phones?: PhoneInfo[]        // 电话列表
  emails?: EmailInfo[]        // 邮箱列表
  addresses?: AddressInfo[]   // 地址列表
}

// 编辑债权人参数
export interface EditCreditorParams {
  id: number
  creditor_type: string
  name: string
  id_type: string
  id_number: string
  phones?: PhoneInfo[]        // 电话列表
  emails?: EmailInfo[]        // 邮箱列表
  addresses?: AddressInfo[]   // 地址列表
}

 

// 债务人数据接口
export interface Debtor {
  id?: number                  // 债务人ID
  debtor_type: string          // 类型
  debtor_name: string                // 姓名
  id_type: string  // 证件类型
  id_number: string  // 证件号码
  phones?: PhoneInfo[]        // 电话列表
  emails?: EmailInfo[]        // 邮箱列表
  addresses?: AddressInfo[]   // 地址列表
  wechats?: WechatInfo[]      // 微信列表
} 

// 债务人查询参数
export interface DebtorParams {
  search?: string
  page: number
  page_size: number
  debtor_type?: string         // 债权人类型筛选
  id_type?: string  // 证件类型筛选
}

// 新增债务人参数
export interface AddDebtorParams {
  debtor_type: string
  debtor_name: string
  id_type: string
  id_number: string
  phones?: PhoneInfo[]        // 电话列表
  emails?: EmailInfo[]        // 邮箱列表
  addresses?: AddressInfo[]   // 地址列表
  wechats?: WechatInfo[]      // 微信列表
}

// 编辑债务人参数
export interface EditDebtorParams {
  id: number
  debtor_type: string
  debtor_name: string
  id_type: string
  id_number: string
  phones?: PhoneInfo[]        // 电话列表
  emails?: EmailInfo[]        // 邮箱列表
  addresses?: AddressInfo[]   // 地址列表
  wechats?: WechatInfo[]      // 微信列表
}

/**
 * 字段类型枚举
 */
export enum FieldType {
  TEXT = 'text',
  DATE = 'date'
}

/**
 * 数据校验类型枚举
 */
export enum ValidationType {
  NONE = 'none',
  PHONE = 'phone',
  ID_CARD = 'id_card'
}

/**
 * 字段配置接口定义
 */
export interface FieldConfig {
  id?: number
  field_name: string          // 字段名称
  field_type: FieldType       // 字段类型
  data_validation: ValidationType // 数据校验
  is_masked: boolean     // 是否脱敏
  prefixKeepCount: number    // 前保留字符数
  suffix_keep_chars: number    // 后保留字符数
}

/**
 * 分页接口定义
 */
export interface Pagination {
  page: number           // 当前页码
  page_size: number         // 每页条数
  total: number           // 总条数
}

/**
 * 分页查询参数
 */
export interface PageQuery {
  page: number
  page_size: number
  fieldName?: string      // 字段名称搜索
  fieldType?: FieldType   // 字段类型筛选
  search?: string         // 搜索关键字
}

/**
 * 分页响应数据
 */
export interface PageResult<T = any> {
  records: T[]            // 数据列表
  total: number          // 总条数
  current: number        // 当前页码
  size: number          // 每页条数
}

/**
 * 字段类型选项
 */
export interface FieldTypeOption {
  label: string
  value: FieldType
}

/**
 * 数据校验选项
 */
export interface ValidationOption {
  label: string
  value: ValidationType
}

/**
 * 数据导入记录接口定义
 */
export interface DataImportRecord {
  id?: number
  package_name: string        // 资产包名称
  source_file: string         // 原文件
  source_file_name: string         // 原文件名称
  fileSize: string          // 文件大小
  uploader: string          // 上传人
  uploadTime: string        // 上传时间
  packageStatusCn: string   // 资产包状态
  unavailableReason?: string // 不可用原因
}

/**
 * 字段映射配置接口定义
 */
export interface FieldMapping {
  id?: number                           // 映射记录ID
  fieldName: string                     // 字段名称
  fieldLabel: string                    // 字段标签
  original_field_name?: string          // 原表头字段名称
  mapped_field_config?: {               // 映射的字段配置
    id: number                          // 字段配置ID
    field_name: string                  // 字段配置名称
  } | null
  mapped_debtor_field_config?: string | null  // 债务人字段映射
  columnName?: string                   // 兼容旧版本的列名（将被废弃）
  isRequired?: boolean                  // 是否必填（兼容旧版本）
}

/**
 * 文件列信息接口定义
 */
export interface FileColumn {
  name: string              // 列名
  label: string             // 列标签
}

/**
 * 字段配置选项接口定义（用于下拉框）
 */
export interface FieldConfigOption {
  id: number                // 字段配置ID
  field_name: string        // 字段配置名称
}

/**
 * 债务人字段选择选项接口定义
 */
export interface DebtorFieldOption {
  value: string             // 字段值
  label: string             // 字段标签
}

/**
 * 数据导入详情接口定义
 */
export interface DataImportDetail {
  id: number
  package_name: string       // 资产包名称
  source_file: string        // 原文件名
  creditorId: number        // 债权人ID
  creditor: string      // 债权人名称
  field_mappings: FieldMapping[] // 字段映射配置
  file_columns: FileColumn[] // 文件列信息
}

/**
 * 预览数据类型枚举
 */
export enum PreviewDataType {
  OPERATION = 'operation',   // 运营数据
  ORIGINAL = 'original'      // 原始数据
}

/**
 * 动态表头定义
 */
export interface DynamicColumn {
  key: string               // 列标识
  label: string            // 列名称
  type?: 'text' | 'number' | 'date' | 'currency'  // 数据类型
  width?: number           // 列宽度
  align?: 'left' | 'center' | 'right'  // 对齐方式
}

/**
 * 预览数据接口定义
 */
export interface PreviewData {
  package_name: string        // 资产包名称
  file_name: string          // Excel文件名
  columns: DynamicColumn[]   // 动态表头
  data: Record<string, any>[] // 表格数据
  total: number             // 总条数
}

/**
 * API预览响应接口定义
 */
export interface PreviewResponse {
  code: number
  msg: string
  data: PreviewData
}