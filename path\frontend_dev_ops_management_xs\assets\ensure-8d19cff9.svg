<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }

      .cls-4 {
        fill: url(#linear-gradient-3);
      }

      .cls-5 {
        fill: url(#linear-gradient-4);
      }

      .cls-6 {
        fill: url(#linear-gradient-5);
      }

      .cls-7 {
        fill: url(#linear-gradient-6);
      }

      .cls-8 {
        fill: url(#linear-gradient-7);
      }

      .cls-9 {
        fill: #d2ff96;
      }
    </style>
    <linearGradient id="linear-gradient" x1="0.366" y1="0.926" x2="0.366" y2="-0.074" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4a7d07"/>
      <stop offset="0.4" stop-color="#689e20"/>
      <stop offset="1" stop-color="#86be3c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.365" y1="0.925" x2="0.365" y2="-0.075" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4d8207"/>
      <stop offset="0.4" stop-color="#6ca521"/>
      <stop offset="1" stop-color="#8cc63f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.363" y1="0.924" x2="0.363" y2="-0.076" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#508708"/>
      <stop offset="0.4" stop-color="#71ab23"/>
      <stop offset="1" stop-color="#91ce41"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.362" y1="0.922" x2="0.362" y2="-0.078" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#548d08"/>
      <stop offset="0.4" stop-color="#75b224"/>
      <stop offset="1" stop-color="#97d644"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.36" y1="0.921" x2="0.36" y2="-0.079" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#579208"/>
      <stop offset="0.4" stop-color="#79b925"/>
      <stop offset="1" stop-color="#9cdd46"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.358" y1="0.92" x2="0.358" y2="-0.08" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5a9709"/>
      <stop offset="0.4" stop-color="#7ebf27"/>
      <stop offset="1" stop-color="#a2e549"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="0.357" y1="0.919" x2="0.357" y2="-0.081" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5d9c09"/>
      <stop offset="0.4" stop-color="#82c628"/>
      <stop offset="1" stop-color="#a7ed4b"/>
    </linearGradient>
  </defs>
  <g id="icon_nnix" transform="translate(-1553 -183)">
    <rect id="矩形_1087" data-name="矩形 1087" class="cls-1" width="24" height="24" transform="translate(1553 183)"/>
    <g id="组_1871" data-name="组 1871" transform="translate(1094.071 -359.721)">
      <g id="组_1870" data-name="组 1870" transform="translate(462.04 549.064)">
        <g id="组_1863" data-name="组 1863" transform="translate(0 0)">
          <path id="路径_5262" data-name="路径 5262" class="cls-2" d="M479.637,552.779a.625.625,0,0,0,0-.879l-2.653-2.653a.62.62,0,0,0-.88,0l-5.881,5.881a.628.628,0,0,1-.881,0l-3.587-3.587a.627.627,0,0,0-.881,0l-2.653,2.653a.629.629,0,0,0,0,.882l7.122,7.12a.626.626,0,0,0,.883,0Z" transform="translate(-462.04 -549.064)"/>
        </g>
        <g id="组_1864" data-name="组 1864" transform="translate(0.098 0.099)">
          <path id="路径_5263" data-name="路径 5263" class="cls-3" d="M462.534,555.293a.625.625,0,0,1,0-.882l2.554-2.553a.623.623,0,0,1,.881,0l3.587,3.585a.627.627,0,0,0,.881,0l5.881-5.88a.62.62,0,0,1,.88,0l2.557,2.554a.628.628,0,0,1,0,.879l-9.315,9.319a.627.627,0,0,1-.883,0Z" transform="translate(-462.352 -549.38)"/>
        </g>
        <g id="组_1865" data-name="组 1865" transform="translate(0.195 0.197)">
          <path id="路径_5264" data-name="路径 5264" class="cls-4" d="M462.846,555.507a.628.628,0,0,1,0-.882l2.457-2.457a.626.626,0,0,1,.881,0l3.587,3.586a.625.625,0,0,0,.881,0l5.881-5.881a.624.624,0,0,1,.88,0l2.457,2.458a.621.621,0,0,1,0,.879l-9.215,9.221a.627.627,0,0,1-.883,0Z" transform="translate(-462.665 -549.692)"/>
        </g>
        <g id="组_1866" data-name="组 1866" transform="translate(0.293 0.295)">
          <path id="路径_5265" data-name="路径 5265" class="cls-5" d="M463.159,555.722a.626.626,0,0,1,0-.882l2.359-2.359a.624.624,0,0,1,.881,0l3.587,3.586a.624.624,0,0,0,.881,0l5.881-5.88a.621.621,0,0,1,.88,0l2.36,2.359a.621.621,0,0,1,0,.879l-9.119,9.125a.632.632,0,0,1-.883,0Z" transform="translate(-462.977 -550.005)"/>
        </g>
        <g id="组_1867" data-name="组 1867" transform="translate(0.391 0.392)">
          <path id="路径_5266" data-name="路径 5266" class="cls-6" d="M463.473,555.937a.622.622,0,0,1,0-.882l2.26-2.26a.621.621,0,0,1,.88,0l3.587,3.586a.624.624,0,0,0,.881,0l5.881-5.881a.621.621,0,0,1,.88,0l2.261,2.261a.622.622,0,0,1,0,.879l-9.019,9.026a.633.633,0,0,1-.883,0Z" transform="translate(-463.289 -550.317)"/>
        </g>
        <g id="组_1868" data-name="组 1868" transform="translate(0.489 0.49)">
          <path id="路径_5267" data-name="路径 5267" class="cls-7" d="M463.786,556.151a.621.621,0,0,1,0-.882l2.162-2.162a.623.623,0,0,1,.88,0l3.587,3.585a.625.625,0,0,0,.881,0l5.881-5.881a.623.623,0,0,1,.88,0l2.164,2.164a.625.625,0,0,1,0,.879l-8.922,8.928a.629.629,0,0,1-.883,0Z" transform="translate(-463.602 -550.63)"/>
        </g>
        <g id="组_1869" data-name="组 1869" transform="translate(0.588 0.587)">
          <path id="路径_5268" data-name="路径 5268" class="cls-8" d="M464.1,556.365a.628.628,0,0,1,0-.882l2.066-2.065a.624.624,0,0,1,.879,0L470.632,557a.626.626,0,0,0,.881,0l5.881-5.88a.62.62,0,0,1,.88,0l2.065,2.066a.621.621,0,0,1,0,.879l-8.823,8.829a.628.628,0,0,1-.883,0Z" transform="translate(-463.919 -550.941)"/>
        </g>
      </g>
      <path id="路径_5269" data-name="路径 5269" class="cls-9" d="M489.029,557.4c.078.077.339-.057.583-.3l4.987-4.989c.243-.244.377-.5.3-.583s-.341.056-.581.3l-4.99,4.988C489.085,557.062,488.95,557.322,489.029,557.4Z" transform="translate(-18.529 -1.681)"/>
      <path id="路径_5270" data-name="路径 5270" class="cls-9" d="M476.6,562.458c.078-.077-.057-.34-.3-.583l-2.715-2.716c-.24-.242-.5-.376-.581-.3s.056.339.3.581l2.714,2.716C476.258,562.4,476.522,562.535,476.6,562.458Z" transform="translate(-7.518 -6.718)"/>
    </g>
  </g>
</svg>
