@mixin normal-style() {
  font-family: Microsoft YaHei;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  background-repeat: no-repeat;
  background-size: 100% auto;
}

[class^="jt-16"],
[class*=" jt-16"] {
  height: 16px;
  width: 16px;
  @include normal-style;
}

[class^="jt-20"],
[class*=" jt-20"] {
  height: 20px;
  width: 20px;
  @include normal-style;
}

[class^="jt-24"],
[class*=" jt-24"] {
  height: 24px;
  width: 24px;
  @include normal-style;
}

[class^="jt-40"],
[class*=" jt-40"] {
  height: 40px;
  width: 40px;
  @include normal-style;
}

[class^="jt-48"],
[class*=" jt-48"] {
  height: 22px;
  width: 48px;
  @include normal-style;
}

[class^="jt-60"],
[class*=" jt-60"] {
  height: 60px;
  width: 60px;
  @include normal-style;
}

[class^="jt-50"],
[class*=" jt-50"] {
  height: 50px;
  width: 50px;
  @include normal-style;
}

.jt-16-edit {
  background-image: url("@/assets/images/icon/edit.svg");
}
.jt-16-delete {
  background-image: url("@/assets/images/icon/delete.svg");
}
// 白色图标
.jt-20-remove {
  background-image: url("@/assets/images/icon/remove.svg");
}
.jt-16-reset {
  background-image: url("@/assets/images/icon/reset.png");
}
.jt-20-delete {
  background-image: url("@/assets/images/icon/delete.svg");
}
.jt-20-wrong {
  background-image: url("@/assets/images/icon/delete_wrong.svg");
}
.jt-20-add {
  background-image: url("@/assets/images/icon/add.svg");
}
// 白色图标
.jt-20-addition {
  background-image: url("@/assets/images/icon/addition.svg");
}
.jt-20-ensure {
  background-image: url("@/assets/images/icon/ensure.svg");
}
.jt-24-back {
  background-image: url("@/assets/images/icon/back.svg");
}
.jt-24-user {
  background-image: url("@/assets/images/icon/icon_yonghu.svg");
}
.jt-24-search {
  background-image: url("@/assets/images/icon/icon_search.svg");
}
.jt-24-section {
  background-image: url("@/assets/images/icon/icon_section.svg");
}
.jt-24-characters {
  background-image: url("@/assets/images/icon/icon_characters.svg");
}
.jt-24-user {
  background-image: url("@/assets/images/icon/icon_user.svg");
}
.jt-24-log {
  background-image: url("@/assets/images/icon/icon_log.svg");
}
.jt-24-controls {
  background-image: url("@/assets/images/icon/icon_controls.svg");
}

.jt-40-setting {
  background-image: url("@/assets/images/icon/dh_sz.svg");
}

.jt-48-close {
  background-image: url("@/assets/images/icon/close.png");
}

.jt-60-party {
  background-image: url("@/assets/images/icon/party_logo.svg");
}

.jt-50-party {
  background-image: url("@/assets/images/icon/party_logo.svg");
}

.jt-60-delete {
  background-image: url("@/assets/images/icon/trash_bin.svg");
}

.jt-60-reset {
  background-image: url("@/assets/images/icon/reset_pwd.png");
}

.jt-20-electronic-seal {
  background-image: url("@/assets/images/icon/icon_electronic_seal.svg");
}
.jt-20-outbound-call-line {
  background-image: url("@/assets/images/icon/icon_outbound_call_line.svg");
}
.jt-20-route-application {
  background-image: url("@/assets/images/icon/icon_route_application.svg");
}
.jt-20-sms-application {
  background-image: url("@/assets/images/icon/icon_sms_application.svg");
}
.jt-16-arrow-down{
  background-image: url("@/assets/images/icon/icon_arrow_down.svg");
}
.jt-16-arrow-up{
  background-image: url("@/assets/images/icon/icon_arrow_up.svg");
}
// 系统菜单图标
.jt-20-mediation {
  background-image: url("@/assets/images/icon/icon_mediation.svg");
}
.jt-20-disposal {
  background-image: url("@/assets/images/icon/icon_disposal.svg");
}
.jt-20-outbound-call {
  background-image: url("@/assets/images/icon/icon_outbound_call.svg");
}
.jt-20-outbound-voice-call {
  background-image: url("@/assets/images/icon/icon_outbound_voice_call.svg");
}
.jt-20-message {
  background-image: url("@/assets/images/icon/icon_message.svg");
}
.jt-20-asset-package{
  background-image: url("@/assets/images/icon/icon_asset_package.svg");
}
.jt-20-debtor-evaluation{
  background-image: url("@/assets/images/icon/icon_debtor_evaluation.svg");
}
.jt-20-disposal-plan{
  background-image: url("@/assets/images/icon/icon_disposal_plan.svg");
}
.jt-20-creditor{
  background-image: url("@/assets/images/icon/icon_creditor.svg");
}
.jt-20-debtor{
  background-image: url("@/assets/images/icon/icon_debtor.svg");
}
.jt-20-pre-action{
  background-image: url("@/assets/images/icon/icon_pre_action.svg");
}
.jt-20-information-repair{
  background-image: url("@/assets/images/icon/icon_information_repair.svg");
}
.jt-20-personnel-dispatch{
  background-image: url("@/assets/images/icon/icon_personnel_dispatch.svg");
}
.jt-20-complaint{
  background-image: url("@/assets/images/icon/icon_complaint.svg");
}
.jt-20-data-acquisition{
  background-image: url("@/assets/images/icon/icon_data_acquisition.svg");
}
.jt-20-data-governance{
  background-image: url("@/assets/images/icon/icon_data_governance.svg");
}
.jt-20-data-analysis{
  background-image: url("@/assets/images/icon/icon_data_analysis.svg");
}

.jt-20-upload{
  background-image: url("@/assets/images/icon/icon_upload.svg");
}
.jt-20-import{
  background-image: url("@/assets/images/icon/icon_import.svg");
  margin-right: 8px;
}