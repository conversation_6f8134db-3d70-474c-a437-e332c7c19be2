import { createRouter, createWebHistory } from 'vue-router'

const HomeView  = () => import("@/views/HomeView.vue")
const PatrolWork = () => import("@/components/system/PatrolWork.vue")
const Setting = () => import("@/components/system/SystemView.vue")

// 运营管理(资产包管理)
const assetPackage  = () => import("@/components/system/operation/assetPackage.vue")

// 调解管理
const caseTracking  = () => import("@/components/system/operation/mediation/caseTracking.vue")
// 调解信息
const mediationInformation  = () => import("@/components/system/operation/mediation/mediationInformation.vue")
// 处置方案 
const disposalPlan  = () => import("@/components/system/operation/mediation/disposalPlan.vue")
// 人员调度
const personnelDispatch  = () => import("@/components/system/operation/mediation/personnelDispatch.vue")

// 债权人管理
const creditor  = () => import("@/components/system/operation/creditor.vue")
// 债务人管理
const debtor  = () => import("@/components/system/operation/debtor.vue")
// 诉前保全
const preAction  = () => import("@/components/system/operation/preAction.vue")
// 信息修复
const informationRepair  = () => import("@/components/system/operation/informationRepair.vue")
// 案例展示
const caseShow  = () => import("@/components/system/operation/caseShow.vue")
// 投诉建议
const complaintFeedback  = () => import("@/components/system/operation/complaintFeedback.vue")

// 外呼管理
const outboundCall  = () => import("@/components/system/operation/outboundCall/outboundCall.vue")
const messageRecord  = () => import("@/components/system/operation/outboundCall/messageRecord.vue")

// 数据管理(数据治理)
const fieldConfiguration  = () => import("@/components/system/data/dataGovernance/fieldConfiguration.vue")
const dataImport  = () => import("@/components/system/data/dataGovernance/dataImport.vue")
// 数据管理(数据分析)
const dataAnalysis  = () => import("@/components/system/data/dataAnalysis.vue")

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
     {
      path: '/',
      redirect: '/home'
    },
    {
      path: '/home',
      component: HomeView,
      redirect: '/home/<USER>',
      children: [
        {
          path: 'patrolwork',
          component: PatrolWork
        },
        {
          path: 'setting',
          component: Setting,
          redirect: '/home/<USER>/assetPackage',
          children: [
            {
              path: 'assetPackage',
              component: assetPackage
            },
            {
              path: 'caseTracking',
              component: caseTracking
            },
            {
              path: 'mediationInformation',
              component: mediationInformation
            },
            {
              path: 'disposalPlan',
              component: disposalPlan
            },
            {
              path: 'personnelDispatch',
              component: personnelDispatch
            },
            {
              path: 'creditor',
              component: creditor
            },
            {
              path: 'debtor',
              component: debtor
            },
            {
              path: 'preAction',
              component: preAction
            },
            {
              path: 'informationRepair',
              component: informationRepair
            },
            {
              path: 'caseShow',
              component: caseShow
            },
            {
              path: 'complaintFeedback',
              component: complaintFeedback
            },
            {
              path: 'outboundCall',
              component: outboundCall
            },
            {
              path: 'messageRecord',
              component: messageRecord
            },
            {
              path: 'fieldConfiguration',
              component: fieldConfiguration
            },
            {
              path: 'dataImport',
              component: dataImport
            },
            {
              path: 'dataAnalysis',
              component: dataAnalysis
            },
          ]
        },
        
      ]
    }
  ]
})

export default router
