export type ImageBackground = [string, string]

// 三级菜单 - 最底层菜单项（原二级菜单，现为三级）
export interface GrandChildSideMenuItem {
  isGrandChildActive: boolean, // 是否激活状态
  name: string, // 菜单名称
  link: string, // 路由链接
  icon: string, // 图标类名
}

// 二级菜单 - 中层菜单项（原一级菜单，现为二级，支持子菜单）
export interface ChildSideMenuItem {
  isChildActive: boolean, // 是否激活状态
  isChildCollapse?: boolean, // 是否折叠状态（新增）
  name: string, // 菜单名称
  link?: string, // 路由链接（可选，如果有子菜单则不需要）
  icon: string, // 图标类名
  children?: GrandChildSideMenuItem[], // 三级子菜单（新增）
}

// 一级菜单 - 顶层菜单项（支持二级子菜单）
export interface SideMenuItem {
  link: string, // 路由链接
  isActive: boolean, // 是否激活状态
  isCollapse: boolean, // 是否折叠状态
  parent: string, // 父级菜单标题
  children: ChildSideMenuItem[] // 二级子菜单列表
}

// 操作日志记录接口
export interface OperationLogData {
  page_plate: string;           // 菜单名称 (例如: "运营管理 > 债权人管理")
  button_name: string;         // 按钮名称 (例如: "新增", "编辑", "删除")
  button_type: string;         // 按钮类型 (例如: "新增", "编辑", "删除")
  page_url: string;        // 浏览器路径 (例如: "/home/<USER>/creditor")
}

// 菜单权限映射接口
export interface MenuPermissionMap {
  [menuPath: string]: string[]; // 路径对应的权限编码数组
}

// 权限编码映射接口  
export interface PermissionCodeMap {
  [permissionName: string]: string; // 权限名称对应的编码
}