/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    0703: typeof import('./src/components/system/operation/mediation/disposalPlan 0703.vue')['default']
    0704: typeof import('./src/components/system/dialogs/EditPlan 0704.vue')['default']
    071812: typeof import('./src/components/system/operation/mediation/mediationInformation 071812.vue')['default']
    071815: typeof import('./src/components/system/operation/mediation/mediationInformation 071815.vue')['default']
    07291652: typeof import('./src/components/system/dialogs/EditMediationInformationDialog 07291652.vue')['default']
    0730: typeof import('./src/components/system/dialogs/EditMediationInformationDialog 0730.vue')['default']
    0731: typeof import('./src/components/system/dialogs/EditMediationInformationDialog 0731.vue')['default']
    07311033: typeof import('./src/components/system/dialogs/EditMediationInformationDialog 07311033.vue')['default']
    AddCase: typeof import('./src/components/system/dialogs/AddCase.vue')['default']
    AddCreditor: typeof import('./src/components/system/dialogs/AddCreditor.vue')['default']
    AddDebtor: typeof import('./src/components/system/dialogs/AddDebtor.vue')['default']
    AddDisposalPlan: typeof import('./src/components/system/dialogs/AddDisposalPlan.vue')['default']
    'AddDisposalPlan 0731': typeof import('./src/components/system/dialogs/AddDisposalPlan 0731.vue')['default']
    AddMediationInformation: typeof import('./src/components/system/dialogs/AddMediationInformation.vue')['default']
    'AddMediationInformation copy': typeof import('./src/components/system/dialogs/AddMediationInformation copy.vue')['default']
    ApprovalDialog: typeof import('./src/components/system/dialogs/ApprovalDialog.vue')['default']
    AssetPackage: typeof import('./src/components/system/operation/assetPackage.vue')['default']
    AssetPackageSelector: typeof import('./src/components/system/dialogs/AssetPackageSelector.vue')['default']
    CaseShow: typeof import('./src/components/system/operation/caseShow.vue')['default']
    CaseTracking: typeof import('./src/components/system/operation/mediation/caseTracking.vue')['default']
    ComplaintFeedback: typeof import('./src/components/system/operation/complaintFeedback.vue')['default']
    copy: typeof import('./src/components/system/dialogs/EditMediationInformation copy.vue')['default']
    Creditor: typeof import('./src/components/system/operation/creditor.vue')['default']
    CustomButton: typeof import('./src/components/common/CustomButton.vue')['default']
    CustomDialog: typeof import('./src/components/common/CustomDialog.vue')['default']
    CustomFocusTrap: typeof import('./src/components/common/focustrap/src/CustomFocusTrap.vue')['default']
    CustomGoback: typeof import('./src/components/common/CustomGoback.vue')['default']
    CustomInput: typeof import('./src/components/common/CustomInput.vue')['default']
    DataAcquisition: typeof import('./src/components/system/data/dataAcquisition.vue')['default']
    DataAnalysis: typeof import('./src/components/system/data/dataAnalysis.vue')['default']
    DataImport: typeof import('./src/components/system/data/dataGovernance/dataImport.vue')['default']
    DataImportDialog: typeof import('./src/components/system/dialogs/DataImportDialog.vue')['default']
    DataPreviewDialog: typeof import('./src/components/system/dialogs/DataPreviewDialog.vue')['default']
    Debtor: typeof import('./src/components/system/operation/debtor.vue')['default']
    'Debtor copy': typeof import('./src/components/system/operation/debtor copy.vue')['default']
    DeleteConfirmDialog: typeof import('./src/components/common/dialog/DeleteConfirmDialog.vue')['default']
    DeleteTips: typeof import('./src/components/system/dialogs/DeleteTips.vue')['default']
    DialogContent: typeof import('./src/components/common/dialog/DialogContent.vue')['default']
    DisposalPlan: typeof import('./src/components/system/operation/mediation/disposalPlan.vue')['default']
    'DisposalPlan 0703': typeof import('./src/components/system/operation/mediation/disposalPlan 0703.vue')['default']
    'DisposalPlan 0731': typeof import('./src/components/system/operation/mediation/disposalPlan 0731.vue')['default']
    EditCase: typeof import('./src/components/system/dialogs/EditCase.vue')['default']
    EditComplaintDialog: typeof import('./src/components/system/dialogs/EditComplaintDialog.vue')['default']
    EditCreditor: typeof import('./src/components/system/dialogs/EditCreditor.vue')['default']
    EditDebtor: typeof import('./src/components/system/dialogs/EditDebtor.vue')['default']
    EditDialog: typeof import('./src/components/system/dialogs/EditDialog.vue')['default']
    EditDisposalPlan: typeof import('./src/components/system/dialogs/EditDisposalPlan.vue')['default']
    'EditDisposalPlan 0731': typeof import('./src/components/system/dialogs/EditDisposalPlan 0731.vue')['default']
    EditMediationInformation: typeof import('./src/components/system/dialogs/EditMediationInformation(废弃）.vue')['default']
    'EditMediationInformation copy': typeof import('./src/components/system/dialogs/EditMediationInformation copy.vue')['default']
    'EditMediationInformation(废弃）': typeof import('./src/components/system/dialogs/EditMediationInformation(废弃）.vue')['default']
    EditMediationInformationDialog: typeof import('./src/components/system/dialogs/EditMediationInformationDialog.vue')['default']
    'EditMediationInformationDialog 07291652': typeof import('./src/components/system/dialogs/EditMediationInformationDialog 07291652.vue')['default']
    'EditMediationInformationDialog 0730': typeof import('./src/components/system/dialogs/EditMediationInformationDialog 0730.vue')['default']
    'EditMediationInformationDialog 0731': typeof import('./src/components/system/dialogs/EditMediationInformationDialog 0731.vue')['default']
    'EditMediationInformationDialog 07311033': typeof import('./src/components/system/dialogs/EditMediationInformationDialog 07311033.vue')['default']
    EditMediationInformationDialog0729: typeof import('./src/components/system/dialogs/EditMediationInformationDialog0729.vue')['default']
    'EditPlan 0704': typeof import('./src/components/system/dialogs/EditPlan 0704.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExpressionEditor: typeof import('./src/components/common/ExpressionEditor.vue')['default']
    FieldConfiguration: typeof import('./src/components/system/data/dataGovernance/fieldConfiguration.vue')['default']
    FieldConfigurationAdd: typeof import('./src/components/system/dialogs/fieldConfigurationAdd.vue')['default']
    FieldConfigurationEdit: typeof import('./src/components/system/dialogs/fieldConfigurationEdit.vue')['default']
    FileUpload: typeof import('./src/components/common/FileUpload.vue')['default']
    InformationRepair: typeof import('./src/components/system/operation/informationRepair.vue')['default']
    MediationInfoPreviewDialog: typeof import('./src/components/system/dialogs/MediationInfoPreviewDialog.vue')['default']
    MediationInformation: typeof import('./src/components/system/operation/mediation/mediationInformation.vue')['default']
    'MediationInformation 071812': typeof import('./src/components/system/operation/mediation/mediationInformation 071812.vue')['default']
    'MediationInformation 071815': typeof import('./src/components/system/operation/mediation/mediationInformation 071815.vue')['default']
    MediationInformation0729: typeof import('./src/components/system/operation/mediation/mediationInformation0729.vue')['default']
    MediationPlanPreviewDialog: typeof import('./src/components/system/dialogs/MediationPlanPreviewDialog.vue')['default']
    MessageRecord: typeof import('./src/components/system/operation/outboundCall/messageRecord.vue')['default']
    OptimizeDialog: typeof import('./src/components/common/OptimizeDialog.vue')['default']
    OutboundCall: typeof import('./src/components/system/operation/outboundCall/outboundCall.vue')['default']
    PatrolWork: typeof import('./src/components/system/PatrolWork.vue')['default']
    PersonnelDispatch: typeof import('./src/components/system/operation/mediation/personnelDispatch.vue')['default']
    PreAction: typeof import('./src/components/system/operation/preAction.vue')['default']
    ResetTips: typeof import('./src/components/system/dialogs/ResetTips.vue')['default']
    ReuploadDialog: typeof import('./src/components/system/dialogs/ReuploadDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SidebarComp: typeof import('./src/components/common/SidebarComp.vue')['default']
    SystemView: typeof import('./src/components/system/SystemView.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
