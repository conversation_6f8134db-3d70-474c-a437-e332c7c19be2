<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomButton from '@/components/common/CustomButton.vue'
import CustomInput from '@/components/common/CustomInput.vue'
import AddDebtor from '@/components/system/dialogs/AddDebtor.vue'
import EditDebtor from '@/components/system/dialogs/EditDebtor.vue'
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import { checkTokenExists } from '@/utils/tokenValidator'
import { logButtonClick } from '@/utils/operationLogger'
import type { 
  Debtor, 
  DebtorParams, 
  AddDebtorParams, 
  EditDebtorParams,
  DebtorTypeOption,
  CertificateTypeOption,
} from '../auth/type'
import { getDebtor, getDebtorOptions, addDebtor, editDebtor, deleteDebtor, getDebtorDetail } from '@/axios/system'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const page = ref(1)
const pageSize = 10

// 搜索条件
const searchValue = ref('')
const selectedDebtorType = ref('')
const selectedCertificateType = ref('')

// 弹框控制
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const currentEditItem = ref<Debtor | null>(null)
const showDeleteDialog = ref(false)
const deleteDebtorItem = ref<Debtor | null>(null)

// 选项数据
const debtorTypeOptions = ref([])
const certificateTypeOptions = ref([])
const loadingOptions = ref(false)

/**
 * 获取选项数据
 */
async function fetchOptions() {
  loadingOptions.value = true
  const { data } = await getDebtorOptions()
  const { state, msg } = data
  if(state === 'success') {
    console.log(data.data,'data.data')
    const {debtor_types,id_types} = data.data;
    if (data.data) {
      const debtorTypesArray = Object.entries(debtor_types).map(([key, value]) => ({
        label: value,
        value: key
      }));
      debtorTypeOptions.value = debtorTypesArray;
      const idTypesArray = Object.entries(id_types).map(([key, value]) => ({
        label: value,
        value: key
      }));
      certificateTypeOptions.value = idTypesArray;
    }
  }else{
    ElMessage.error(msg)
  }
  loadingOptions.value = false
}

/**
 * 获取债务人列表
 */
async function fetchCreditorList() {
  // 检查token是否存在
  if (!checkTokenExists()) {
    ElMessage.error('登录状态已失效，请重新登录')
    return
  }

  loading.value = true
  const params: DebtorParams = {
    page: page.value,
    page_size: pageSize,
  }
  
  // 添加搜索条件
  if (searchValue.value.trim()) {
    params.search = searchValue.value.trim()
  }
  
  // 添加类型筛选
  if (selectedDebtorType.value) {
    params.debtor_type = selectedDebtorType.value
  }
  
  // 添加证件类型筛选
  if (selectedCertificateType.value) {
    params.id_type = selectedCertificateType.value
  }
  
  const { data } = await getDebtor(params)
  const { state, msg } = data
  if(state === 'success') {
    tableData.value = data.data.results
    total.value = data.data.count
  }else{
    ElMessage.error(msg)
  }
  loading.value = false
}

/**
 * 处理搜索
 */
function handleSearch() {
  logButtonClick('搜索', '债务人管理') // 记录操作日志
  page.value = 1
  fetchCreditorList()
}

/**
 * 分页改变
 */
function handlePageChange(newPage: number) {
  page.value = newPage
  fetchCreditorList()
}

/**
 * 打开新增弹框
 */
function openAddDialog() {
  showAddDialog.value = true
}

/**
 * 关闭新增弹框
 */
function closeAddDialog() {
  showAddDialog.value = false
}

/**
 * 处理新增确认
 */
async function handleAddConfirm(creditorData: AddDebtorParams) {
  const { data } = await addDebtor(creditorData)
  const { state , msg } = data
  if(state === 'success') {
    ElMessage.success(msg)
    showAddDialog.value = false
    page.value = 1
    fetchCreditorList()
  }
}

/**
 * 打开编辑弹框
 */
async function openEditDialog(row: Debtor) {
  const { data } = await getDebtorDetail(row.id)
  const { state , msg } = data
  if(state === 'success') {
    console.log(data.data,'data.data')
    currentEditItem.value = data.data
    showEditDialog.value = true
  }else{
    ElMessage.error(msg)
  }
}

/**
 * 关闭编辑弹框
 */
function closeEditDialog() {
  showEditDialog.value = false
  currentEditItem.value = null
}

/**
 * 处理编辑确认
 */
async function handleEditConfirm(creditorData: EditDebtorParams) {
  const { data } = await editDebtor(creditorData, creditorData.id)
  const { state , msg } = data
  if(state === 'success') {
    ElMessage.success(msg)
    showEditDialog.value = false
    currentEditItem.value = null
    fetchCreditorList()
  }
}

/**
 * 打开删除确认弹框
 */
function handleDeleteCreditor(row: Debtor) {
  // 检查token是否存在
  if (!checkTokenExists()) {
    ElMessage.error('登录状态已失效，请重新登录')
    return
  }

  deleteDebtorItem.value = row
  showDeleteDialog.value = true
}

/**
 * 确认删除债务人
 */
async function confirmDeleteDebtor() {
  if (!deleteDebtorItem.value) return

  try {
    // 调用删除API
    await deleteDebtor(deleteDebtorItem.value.id!)

    ElMessage.success('删除成功')
    fetchCreditorList()
  } catch (error) {
    ElMessage.error(error)
  } finally {
    showDeleteDialog.value = false
    deleteDebtorItem.value = null
  }
}

/**
 * 获取主要联系方式
 */
function getPrimaryContact(contactList: any[] | undefined, field: string): string {
  if (!contactList || contactList.length === 0) return ''
  
  // 查找主要联系方式
  const primaryContact = contactList.find(item => item.is_primary)
  if (primaryContact) {
    return primaryContact[field] || ''
  }
  // 如果没有主要联系方式，返回第一个
  return contactList[0][field]
}

/**
 * 格式化日期显示
 */
function formatDate(dateString?: string): string {
  if (!dateString) return '-'
  return dateString.split('T')[0]
}

// 组件挂载时获取数据
onMounted(async () => {
  await fetchOptions()
  await fetchCreditorList()
})
</script>

<template>
  <div class="creditor-management-page">
    <!-- 搜索区域 -->
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput 
            v-model="searchValue" 
            placeholder="搜索姓名、证件号码" 
            @keyup.enter="handleSearch"
            @click="handleSearch" />
        </div>
        <div class="search-item">
          <label>债务人类型</label>
          <el-select 
            v-model="selectedDebtorType" 
            placeholder="债务人类型" 
            clearable
            @change="handleSearch"
            :loading="loadingOptions"
            style="width: 200px">
            <el-option
              v-for="option in debtorTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
        <div class="search-item">
          <label>证件类型</label>
          <el-select 
            v-model="selectedCertificateType" 
            placeholder="证件类型" 
            clearable
            @change="handleSearch"
            :loading="loadingOptions"
            style="width: 140px">
            <el-option
              v-for="option in certificateTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
        <div class="search-item">
          <CustomButton @click="openAddDialog" :height="34">
            <i class="jt-20-add"></i>新增债务人
          </CustomButton>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column type="index" label="序号" width="80" align="center">
          <template #default="{ $index }">
            {{ pageSize * (page - 1) + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="类型" width="140" align="center" prop="debtor_type_cn"></el-table-column>
        <el-table-column align="center" prop="debtor_name" label="姓名" min-width="200" />
        <el-table-column label="证件类型" width="120" align="center" prop="id_type_cn" />
        <el-table-column align="center" prop="id_number" label="证件号码" min-width="200" />
        <el-table-column align="center" label="联系电话" width="130">
          <template #default="{ row }">
            {{ getPrimaryContact(row.phones, 'phone')}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="联系邮箱" width="180">
          <template #default="{ row }">
            {{ getPrimaryContact(row.emails, 'email')}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="联系地址" min-width="200">
          <template #default="{ row }">
            {{ getPrimaryContact(row.addresses, 'address') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="联系微信" min-width="200">
          <template #default="{ row }">
            {{ getPrimaryContact(row.wechats, 'wechat') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <div class="operation-buttons">
              <div @click="openEditDialog(row)" class="operation-btn edit-btn">编辑</div>
              <div @click="handleDeleteCreditor(row)" class="operation-btn delete-btn">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="page"
          :page-size="pageSize"
          @current-change="handlePageChange" />
      </div>
    </div>

    <!-- 新增弹框 -->
    <AddDebtor 
      :visible="showAddDialog"
      @update:visible="closeAddDialog"
      @confirm="handleAddConfirm" />

    <!-- 编辑弹框 -->
    <EditDebtor
      :visible="showEditDialog"
      :creditor-data="currentEditItem"
      @update:visible="closeEditDialog"
      @confirm="handleEditConfirm" />

    <!-- 删除确认弹框 -->
    <DeleteConfirmDialog
      :visible="showDeleteDialog"
      title="删除债务人"
      :message="`确定要删除债务人「${deleteDebtorItem?.debtor_name}」吗？此操作不可撤销。`"
      confirm-text="确认"
      cancel-text="取消"
      @update:visible="showDeleteDialog = $event"
      @confirm="confirmDeleteDebtor"
      @cancel="showDeleteDialog = false" />
  </div>
</template>

<style lang="scss" scoped>
.creditor-management-page {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    margin-bottom: 20px;
    
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          min-width: 80px;
          font-weight: 500;
          color: #333;
        }
        
        :deep(.el-select) {
          width: 160px;
        }
        
        :deep(.el-date-editor) {
          height: 36px;
        }
      }
    }
  }
  
  .table-container {
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>