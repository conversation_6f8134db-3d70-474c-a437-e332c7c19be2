<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import CustomInput from '@/components/common/CustomInput.vue'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import { getSmsRecords } from "@/axios/system"
import { ElMessage } from 'element-plus'

const loading = ref(false)
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const recordList = ref([])

function setPage1() {
  page.value = 1
  searchRecordList()
}

async function searchRecordList() {
  loading.value = true
	const params = {
		search:search.value,
		page:page.value,
		page_size,
	}
	const { data } = await getSmsRecords(params)
	const { state, msg } = data
	if(state == 'success'){
		recordList.value = data.data.results
		total.value = data.data.count
	}else{
		ElMessage.error(msg)
	}
	loading.value = false
}

function pageChanged(p: number) {
  page.value = p
  searchRecordList()
}

onMounted(() => {
  searchRecordList()
})
</script>

<template>
  <div class="sms-record">
    <div class="search-header">
      <CustomInput v-model="search" placeholder="搜索接收手机号、短信内容、发送失败原因" @click="setPage1"></CustomInput>
    </div>
    <div>
      <el-table
        v-loading="loading"
        :data="recordList"
        border
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle"
        class="record-table">
        <el-table-column type="index" label="序号" width="60" align="center">
          <template v-slot="{$index}">
            {{page_size * (page - 1) + $index + 1}}
          </template>
        </el-table-column>
        <el-table-column prop="send_time" label="发送时间" align="center" width="190"></el-table-column>
        <el-table-column prop="recipient_phone" label="接收手机号" align="center" width="130"></el-table-column>
        <el-table-column prop="content_preview" label="短信内容预览" align="center" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sms_status_cn" label="发送状态" align="center" width="90">
          <template v-slot="{row}">
            <!-- sms_status =  pending待发送、sending发送中、sent_success发送成功、sent_failed发送失败 -->
            <span 
              :class="{
                'text-success': row.sms_status_cn === '发送成功',
                'text-danger': row.sms_status_cn === '待发送' || row.sms_status_cn === '发送中',
                'text-warning': row.sms_status_cn == '发送失败'
              }">
              {{ row.sms_status_cn }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="sms_type_cn" label="短信类型" align="center" width="110">
          <template v-slot="{row}">
            <!-- sms_type = notification通知短信、verification验证码短信、reminder提醒短信、collection催收短信、other其他 -->
             <span 
              :class="{
                'text-success': row.sms_type_cn === '验证码短信' || row.sms_type_cn === '通知短信',
                'text-danger': row.sms_type_cn === '提醒短信' || row.sms_type_cn === '其他',
                'text-warning': row.sms_type_cn == '催收短信'
              }">
              {{ row.sms_type_cn }}
            </span>
            <!-- <el-tag
              :type="row.sms_type_cn === '提醒短信' || row.sms_type_cn === '催收短信' ? 'danger' : row.sms_type_cn === '通知短信' || row.sms_type_cn === '验证码短信' ? 'primary' : 'success'"
              size="small">
              {{ row.sms_type_cn }}
            </el-tag> -->
          </template>
        </el-table-column>
        <el-table-column prop="creditor_name" label="债权人名称" align="center" min-width="150"></el-table-column>
        <el-table-column prop="debtor_name" label="债务人名称" align="center" width="150"></el-table-column>
        <el-table-column prop="content_length" label="内容长度" align="center" min-width="90"></el-table-column>
        <el-table-column prop="delivery_time" label="送达时间" align="center" width="190"></el-table-column>
        <el-table-column prop="task_batch_id" label="任务批次号" align="center" min-width="130"></el-table-column>
        <el-table-column prop="failure_reason" label="失败原因" align="center" width="150" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>

    <div class="pagination-wrapper">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="page"
        :page-size="page_size"
        @current-change="pageChanged"
      ></el-pagination>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sms-record {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .search-header {
    display: grid;
    grid-template-columns: 400px;
    gap: 20px;
    margin-bottom: 20px;
  }
}

.record-table {
  border-radius: 8px;
  overflow: hidden;
}
.text-success{
  color:#1377C4;
}
.text-warning{
  color:#D94223;
}
.text-danger{
  color:#e6a23c;
}
.pagination-wrapper {
  margin-top: 20px;
  padding-bottom: 20px;
	display: flex;
	justify-content: center; 
}

// 响应式适配
@media (max-width: 1200px) {
  .sms-record {
    .search-header {
      grid-template-columns: 1fr;
    }
  }

  .record-table {
    .content-preview {
      max-width: 140px;
    }

    .text-ellipsis {
      max-width: 120px;
    }
  }
}
</style>