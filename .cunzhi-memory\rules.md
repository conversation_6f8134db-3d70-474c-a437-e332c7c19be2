# 开发规范和规则

- mediationCaseOptions下拉框调用getMediationCase接口，下拉框key是case_number，value是mediation_plan_name
- 编辑弹框可参考src\components\system\dialogs\EditDisposalPlan.vue页面的currentMode === 'asset'的字段配置；不要生成总结性Markdown文档；不要生成测试脚本；不要编译，用户自己编译；不要运行，用户自己运行
- 资产包列表新增参数search和package_status：available；调解案件列表接口新增参数search；两个tab列表没有新增按钮；编辑调解案件信息弹框需要回显"字段配置信息展示（禁用）"
- EditMediationInformationDialog.vue编辑弹框需要根据editMode调用对应的详情接口：资产包模式调用getDataImportDetail，调解案件模式调用getMediationCaseDetail
- 用户反馈修复后VueExpressionEditor仍然不能输入中文，单选框仍然不能正确选中单个选项，需要更深入的修复方案
