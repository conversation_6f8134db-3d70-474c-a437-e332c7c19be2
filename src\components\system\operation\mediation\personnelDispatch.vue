<script lang="ts" setup>
import { onMounted, ref, type Ref } from 'vue'
import CustomInput from '@/components/common/CustomInput.vue';
import CustomDialog from '@/components/common/CustomDialog.vue';
import CustomButton from '@/components/common/CustomButton.vue';
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import type { PersonnelDispatch, PersonnelDispatchParams, CaseTrackingStatus, CaseTrackingStatusOption } from '../../auth/type';
import { CaseTrackingStatus as CaseTrackingStatusEnum } from '../../auth/type';
import { getMediationCase, editUpdateMediatorn, getMediatorsOptions } from '@/axios/system'
import { ElMessage } from 'element-plus';

// 搜索参数
const searchParams: Ref<PersonnelDispatchParams> = ref({
	search: '',
	page: 1,
	page_size: 10
})

const loading = ref(false)
// 表格数据
const tableData: Ref<PersonnelDispatch[]> = ref([])

const total = ref(0)

// 编辑弹框相关数据
const showEditDialog = ref(false)
const editLoading = ref(false)
const currentEditRow = ref<any>(null)

// 调解员选项数据
interface MediatorOption {
  label: string
  value: number
}
const mediatorOptions = ref<MediatorOption[]>([])
const mediatorLoading = ref(false)

// 编辑表单数据
const editFormData = ref({
  case_number: '',
  case_status_cn: '',
  mediator: null as number | null,
  mediator_name: ''
})

function handleSearch() {
  searchParams.value.page = 1
  searchCaseTrackingList()
}

// 调用列表接口
async function searchCaseTrackingList() {
	loading.value = true
    const params  = {
      page: searchParams.value.page,
      page_size: searchParams.value.page_size,
      search: searchParams.value.search
    }

    const { data } = await getMediationCase(params)
    const { state, msg } = data

    if (state === 'success') {
      const { results, count } = data.data
      tableData.value = results
      total.value = count
    } else {
      ElMessage.error(msg)
    }
	loading.value = false
}

// 分页改变
function handlePageChange(p: number) {
  searchParams.value.page = p
	// 调用列表接口
	searchCaseTrackingList()
}

// 打开编辑弹框
function handleEdit(row: any, index: number) {
  currentEditRow.value = row
  // 设置表单数据
  editFormData.value = {
	case_number: row.case_number,
    case_status_cn: row.case_status_cn,
    mediator: row.mediator || null,
	mediator_name: row.mediator_name || ''
  }
  // 加载调解员下拉框
  loadMediatorOptions()
  showEditDialog.value = true
}

// 获取调解员选项列表
async function loadMediatorOptions() {
  	mediatorLoading.value = true
	const { data } = await getMediatorsOptions()
	const { state, msg } = data
	if (state === 'success') {
		mediatorOptions.value = data.data.map((user: any) => ({
			label: user.username,
			value: user.id
		}))
	} else {
		ElMessage.error(msg || '获取调解员数据失败')
	}
	mediatorLoading.value = false
}

// 关闭编辑弹框
function closeEditDialog() {
  showEditDialog.value = false
  currentEditRow.value = null
  editFormData.value = {
    case_number: '',
    case_status_cn: '',
    mediator: null,
    mediator_name: ''
  }
}

// 提交编辑
async function handleEditSubmit() {
	if (!currentEditRow.value || !editFormData.value.mediator) {
		ElMessage.error('请选择调解员')
		return
	}
  	editLoading.value = true
	const params = {
		mediator:editFormData.value.mediator
	}
	const { data } = await editUpdateMediatorn(params,currentEditRow.value.id)
	const { state, msg } = data

	if (state === 'success') {
		ElMessage.success('编辑成功')
		closeEditDialog()
		// 刷新列表
		searchCaseTrackingList()
	} else {
		ElMessage.error(msg || '编辑失败')
	}
	editLoading.value = false
}

onMounted(() => {
	searchCaseTrackingList()
})
</script>

<template>
	<div class="case-tracking">
		<div class="search-area">
			<div class="search-form">
				<CustomInput 
					v-model="searchParams.search"
					placeholder="请输入调解案件号"
					class="search-input"
					@keydown.enter="handleSearch"
					@click="handleSearch"
				/>
			</div>
		</div>
		<div class="table-container">
			<el-table 
        		v-loading="loading"
				:data="tableData" 
				style="width: 100%"
				:header-cell-style="headerCellStyle"
				:cell-style="cellStyle"
				border
				stripe
			>
				<el-table-column prop="case_number" label="调解案件号" align="center" ></el-table-column>
				<el-table-column prop="case_status_cn" label="案件状态" align="center" ></el-table-column>
				<el-table-column prop="mediator_name" label="调解员" align="center" ></el-table-column>
				
				<el-table-column label="操作" align="center" width="180">
					<template v-slot="{row, $index}">
						<div class="operation-buttons">
							<div @click="handleEdit(row,$index)" class="operation-btn edit-btn">编辑</div>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div class="pagination-container">
			<el-pagination
				class="pagi"
				background
				v-model:current-page="searchParams.page"
				v-model:page-size="searchParams.page_size"
				:total="total"
				layout="prev, pager, next"
				@current-change="handlePageChange"
			/>
		</div>
	</div>

	<!-- 编辑弹框 -->
	<CustomDialog
		:visible="showEditDialog"
		@update:visible="closeEditDialog"
		width="600px"
		title="编辑人员调度">
		<div class="edit-form-container" v-loading="editLoading">
			<el-form
				:model="editFormData"
				label-width="120px"
				class="edit-form">
				<el-form-item label="调解案件号">
					<el-input
						v-model="editFormData.case_number"
						disabled
						class="readonly-input" />
				</el-form-item>
				<el-form-item label="案件状态">
					<el-input
						v-model="editFormData.case_status_cn"
						disabled
						class="readonly-input" />
				</el-form-item>
				<el-form-item label="调解员" required>
					<el-select
						v-model="editFormData.mediator"
						placeholder="请选择调解员"
						:loading="mediatorLoading"
						style="width: 100%"
						clearable>
						<el-option
							v-for="option in mediatorOptions"
							:key="option.value"
							:label="option.label"
							:value="option.value" />
					</el-select>
				</el-form-item>
			</el-form>

			<div class="dialog-footer">
				<CustomButton
					@click="handleEditSubmit"
					:loading="editLoading"
					:height="34"
					btn-type="blue">
					<i class="jt-20-ensure"></i>确认
				</CustomButton>
				<CustomButton
					@click="closeEditDialog"
					:height="34">
					<i class="jt-20-delete"></i>取消
				</CustomButton>
			</div>
		</div>
	</CustomDialog>
</template>

<style lang="scss" scoped>
.case-tracking{
	margin: 24px;
	background-color: #fff;
	height: calc(100% - 65px);
	padding: 20px 20px 0 20px;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	.search-area{
		display: grid;
		grid-template-columns: 300px 125px;
		gap: 20px;
		margin-bottom: 20px;
	}
	.pagination-container{
		margin-top: 20px;
	}
	:deep(.el-pagination){
		justify-content: center;
	}
}
// 编辑弹框样式
.edit-form-container {
	padding: 20px 0;

	.edit-form {
		:deep(.el-form-item) {
			margin-bottom: 24px;

			.el-form-item__label {
				color: #303133;
				font-weight: 500;
				font-size: 14px;
			}
		}

		// 只读输入框样式
		.readonly-input {
			:deep(.el-input__wrapper) {
				background-color: #f5f7fa;
				cursor: not-allowed;

				.el-input__inner {
					background-color: #f5f7fa;
					color: #606266;
					cursor: not-allowed;
				}
			}
		}
	}

	.dialog-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 12px;
		padding-top: 30px;
	}
}

// Element Plus 组件样式覆盖
:deep(.el-select) {
	.el-select__wrapper {
		border-radius: 4px;
	}
}

:deep(.el-input) {
	.el-input__wrapper {
		border-radius: 4px;
	}
}

.outbound-e-seal{
	.no_content {
	text-align: center;
	margin: 17% 0;
	color: #7a7a7a;
	font-size: 14px;
		div {
			padding-top: 24px;
		}
	}
}
</style>