<script lang="ts" setup>
import { ref, watch } from 'vue'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import { ElMessage } from 'element-plus'


/**
 * 案例数据接口定义
 */
interface CaseInfo {
  id?: number
  case_name: string
  case_date: string
  amount: number
  mediation_result: string
  case_details: string
}

// 组件属性定义
const props = defineProps<{
  showDialog: boolean
}>()

// 组件事件定义
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'ensure', caseData: CaseInfo): void
}>()

// 表单引用和加载状态
const formRef = ref()
const loading = ref(false)

// 新增表单数据
const formData = ref<CaseInfo>({
  case_name: '',
  case_date: '',
  amount: 0,
  mediation_result: '',
  case_details: ''
})

// 表单验证规则
const rules = {
  case_name: [
    { required: true, message: '请输入案例名称', trigger: 'blur' },
  ],
  case_date: [
    { required: true, message: '请选择案例日期', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入涉及金额', trigger: 'blur' },
    { pattern: /^\d*(\.\d{1,2})?$/, message: '金额不能包含字母,小数点后两位', trigger: 'blur' },
  ],
  mediation_result: [
    { required: true, message: '请选择调解结果', trigger: 'change' }
  ],
  case_details: [
    { required: true, message: '请输入案例详情', trigger: 'blur' },
    { min: 10, max: 1000, message: '案例详情长度在10到1000个字符', trigger: 'blur' }
  ]
}

// 监听弹框显示状态
watch(() => props.showDialog, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

/**
 * 重置表单数据
 */
function resetForm() {
  formData.value = {
    case_name: '',
    case_date: '',
    amount: 0,
    mediation_result: '',
    case_details: ''
  }
  
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

/**
 * 关闭弹框
 */
function close() {
  emit('close')
}

/**
 * 确认新增案例
 */
async function ensureAdd() {
  if (!formRef.value) return
  loading.value = true
  try {
    // 验证表单
    const isValid = await new Promise((resolve) => {
      formRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValid) {
      return
    }
    
    // 提交数据
    emit('ensure', { ...formData.value })
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('表单验证失败，请检查输入内容')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog 
    :visible="showDialog" 
    @update:visible="close"
    width="600px" 
    title="新增案例信息">
    <div class="form-container" v-loading="loading">
      <el-form 
        ref="formRef"
        :model="formData" 
        :rules="rules"
        label-width="100px" 
        class="case-form">
        
        <!-- 案例名称 -->
        <el-form-item label="案例名称" prop="case_name">
          <el-input 
            v-model="formData.case_name" 
            placeholder="请输入案例名称"
            maxlength="100"
            style="width: 100%" />
        </el-form-item>
        
        <!-- 案例日期 -->
        <el-form-item label="案例日期" prop="case_date">
          <el-date-picker
            v-model="formData.case_date"
            type="date"
            placeholder="选择案例日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%" />
        </el-form-item>
        
        <!-- 涉及金额 -->
        <el-form-item label="涉及金额" prop="amount">
          <el-input
            v-model="formData.amount"
            placeholder="请输入涉及金额"
            style="width: 100%" />
        </el-form-item>
        
        <!-- 调解结果 -->
        <el-form-item label="调解结果" prop="mediation_result">
          <el-input 
            v-model="formData.mediation_result" 
            placeholder="请输入调解结果"
            maxlength="100"
            style="width: 100%" />
        </el-form-item>
        
        <!-- 案例详情 -->
        <el-form-item label="案例详情" prop="case_details">
          <el-input 
            v-model="formData.case_details"
            type="textarea"
            placeholder="请详细描述案例的具体情况、处理过程等"
            :rows="5"
            maxlength="1000"
            show-word-limit
            style="width: 100%" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <CustomButton @click="ensureAdd" :height="34" :loading="loading" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.form-container {
  .case-form {
    padding: 16px 0;
    
    :deep(.el-form-item) {
      margin-bottom: 20px;
      
      .el-form-item__label {
        color: #606266;
        line-height: 32px;
      }
      
      .el-form-item__content {
        line-height: 32px;
      }
    }
    
    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: 4px;
      }
    }
    
    :deep(.el-textarea) {
      .el-textarea__inner {
        border-radius: 4px;
      }
    }
    
    :deep(.el-select) {
      .el-select__wrapper {
        border-radius: 4px;
      }
    }
    
    :deep(.el-input-number) {
      .el-input__wrapper {
        border-radius: 4px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 16px 0 8px 0;
}

// 响应式适配
@media (max-width: 768px) {
  .form-container {
    .case-form {
      :deep(.el-form-item) {
        .el-form-item__label {
          font-size: 14px;
        }
      }
    }
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }
}
</style> 