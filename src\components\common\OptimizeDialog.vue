
<script lang="ts" setup>
import { computed } from 'vue';
import DialogContent from "./dialog/DialogContent.vue"
import { CustomFocusTrap } from './focustrap';

const props = defineProps<{
  showDialog: boolean
}>()

const emit = defineEmits<{
  (e: 'ensure'): void,
  (e: 'close'): void
}>()

let dialogVisible = computed({
  get() {
    return props.showDialog
  },
  set() {
    emit('close')
  }
})
function updateVisible() {
  emit('close')
}
function ensureEditDepart() {
  emit('ensure')
}
function onFocusoutPrevented(event: CustomEvent) {
  if (event.detail?.focusReason === 'pointer') {
    event.preventDefault()
  }
}
</script>

<template>
  <div>
    <CustomFocusTrap
      loop
      :trapped="true"
      @focusout-prevented="onFocusoutPrevented"
    >
      <DialogContent
        title="删除" 
        :visible="dialogVisible"
        width="300px" 
        :markclose="true" 
        @update:visible="updateVisible">
        <div>
          <slot></slot>
        </div>
        <div name="footer">
          <slot name="footer"></slot>
        </div>
      </DialogContent>
    </CustomFocusTrap>
  </div>
</template>

<style lang="scss" scoped></style>