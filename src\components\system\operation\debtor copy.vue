<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomButton from '@/components/common/CustomButton.vue'
import CustomInput from '@/components/common/CustomInput.vue'
import AddDebtor from '@/components/system/dialogs/AddDebtor.vue'
import EditDebtor from '@/components/system/dialogs/EditDebtor.vue'
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import { checkTokenExists } from '@/utils/tokenValidator'
import type { 
  Debtor, 
  DebtorParams, 
  AddDebtorParams, 
  EditDebtorParams,
  DebtorType,
  CertificateType,
  DebtorTypeOption,
  CertificateTypeOption
} from '../auth/type'
import { DebtorType as DebtorTypeEnum, CertificateType as CertificateTypeEnum } from '../auth/type'

// 响应式数据
const loading = ref(false)
const tableData = ref<Debtor[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = 10

// 搜索条件
const searchValue = ref('')

// 弹框控制
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const currentEditItem = ref<Debtor | null>(null)
const showDeleteDialog = ref(false)
const deleteDebtorItem = ref<Debtor | null>(null)

// 债权人类型选项配置
const DebtorTypeOptions: DebtorTypeOption[] = [
  { label: '自然人', value: DebtorTypeEnum.INDIVIDUAL },
  { label: '法人', value: DebtorTypeEnum.CORPORATION },
  { label: '其他', value: DebtorTypeEnum.OTHER }
]

// 证件类型选项配置
const certificateTypeOptions: CertificateTypeOption[] = [
  { label: '身份证', value: CertificateTypeEnum.ID_CARD },
  { label: '护照', value: CertificateTypeEnum.PASSPORT },
  { label: '营业执照', value: CertificateTypeEnum.BUSINESS_LICENSE }
]

/**
 * 获取债权人类型显示文本
 */
function getDebtorTypeLabel(type: DebtorType): string {
  const option = DebtorTypeOptions.find(opt => opt.value === type)
  return option?.label || type
}

/**
 * 获取证件类型显示文本
 */
function getCertificateTypeLabel(type: CertificateType): string {
  const option = certificateTypeOptions.find(opt => opt.value === type)
  return option?.label || type
}

/**
 * 生成模拟数据
 */
function generateMockData(): Debtor[] {
  const names = ['中国银行股份有限公司', '华融资产管理股份有限公司', '招联消费金融有限公司', '上汽通用汽车金融有限责任公司', '张三', '李四', '王五赵六']
  
  return Array.from({ length: 12 }, (_, index) => ({
    id: index + 1,
    type: Object.values(DebtorTypeEnum)[index % Object.keys(DebtorTypeEnum).length] as DebtorType,
    name: names[index % names.length],
    certificate_type: index % 3 === 0 ? CertificateTypeEnum.BUSINESS_LICENSE : 
                     index % 2 === 0 ? CertificateTypeEnum.ID_CARD : CertificateTypeEnum.PASSPORT,
    certificate_number: index % 3 === 0 ? `91${String(index + **********).padStart(17, '0')}` : 
                       `${String(index + **********00000000).slice(0, 18)}`,
    phone: index % 2 === 0 ? `138${String(index).padStart(8, '0')}` : undefined,
    email: index % 3 === 0 ? `Debtor${index + 1}@example.com` : undefined,
    address: index % 4 === 0 ? `北京市朝阳区示例地址${index + 1}号` : undefined,
    wechat: index % 2 === 0 ? `wechat${index + 1}` : undefined,
  }))
}

/**
 * 获取债权人列表
 */
async function fetchDebtorList() {
  // 检查token是否存在
  if (!checkTokenExists()) {
    ElMessage.error('登录状态已失效，请重新登录')
    return
  }

  loading.value = true
  try {
    // 使用模拟数据替代API调用
    const mockData = generateMockData()
    
    // 模拟搜索过滤
    let filteredData = mockData
    if (searchValue.value.trim()) {
      filteredData = mockData.filter(Debtor => 
        Debtor.name.toLowerCase().includes(searchValue.value.toLowerCase()) ||
        Debtor.certificate_number.includes(searchValue.value) ||
        (Debtor.phone && Debtor.phone.includes(searchValue.value))
      )
    }
    
    // 模拟分页
    const startIndex = (page.value - 1) * pageSize
    const endIndex = startIndex + pageSize
    tableData.value = filteredData.slice(startIndex, endIndex)
    total.value = filteredData.length
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
  } catch (error) {
    ElMessage.error('获取债权人列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
function handleSearch() {
  logButtonClick('搜索债权人', '债权人管理') // 记录操作日志
  page.value = 1
  fetchDebtorList()
}

/**
 * 分页改变
 */
function handlePageChange(newPage: number) {
  page.value = newPage
  fetchDebtorList()
}

/**
 * 打开新增弹框
 */
function openAddDialog() {
  logButtonClick('新增债权人', '债权人管理') // 记录操作日志
  showAddDialog.value = true
}

/**
 * 关闭新增弹框
 */
function closeAddDialog() {
  showAddDialog.value = false
}

/**
 * 处理新增确认
 */
async function handleAddConfirm(DebtorData: AddDebtorParams) {
  try {
    // 检查token是否存在
    if (!checkTokenExists()) {
      ElMessage.error('登录状态已失效，请重新登录')
      return
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('债权人新增成功')
    showAddDialog.value = false
    page.value = 1
    fetchDebtorList()
  } catch (error) {
    ElMessage.error('新增债权人失败，请重试')
  }
}

/**
 * 打开编辑弹框
 */
function openEditDialog(row: Debtor) {
  logButtonClick('编辑债权人', '债权人管理') // 记录操作日志
  currentEditItem.value = { ...row }
  showEditDialog.value = true
}

/**
 * 关闭编辑弹框
 */
function closeEditDialog() {
  showEditDialog.value = false
  currentEditItem.value = null
}

/**
 * 处理编辑确认
 */
async function handleEditConfirm(DebtorData: EditDebtorParams) {
  try {
    // 检查token是否存在
    if (!checkTokenExists()) {
      ElMessage.error('登录状态已失效，请重新登录')
      return
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('债权人编辑成功')
    showEditDialog.value = false
    currentEditItem.value = null
    fetchDebtorList()
  } catch (error) {
    ElMessage.error('编辑债权人失败，请重试')
  }
}

/**
 * 打开删除确认弹框
 */
function deleteDebtor(row: Debtor) {
  // 检查token是否存在
  if (!checkTokenExists()) {
    ElMessage.error('登录状态已失效，请重新登录')
    return
  }

  deleteDebtorItem.value = row
  showDeleteDialog.value = true
}

/**
 * 确认删除债权人
 */
async function confirmDeleteDebtor() {
  if (!deleteDebtorItem.value) return

  try {
    logButtonClick('删除债权人', '债权人管理') // 记录操作日志

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    ElMessage.success('债权人删除成功')
    fetchDebtorList()
  } catch (error) {
    ElMessage.error('删除债权人失败，请重试')
  } finally {
    showDeleteDialog.value = false
    deleteDebtorItem.value = null
  }
}

/**
 * 格式化日期显示
 */
function formatDate(dateString?: string): string {
  if (!dateString) return '-'
  return dateString.split('T')[0]
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDebtorList()
})
</script>

<template>
  <div class="Debtor-management-page">
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput 
            v-model="searchValue" 
            placeholder="搜索债务人姓名、证件号码、联系电话" 
            @keyup.enter="handleSearch"
            @click="handleSearch" />
        </div>
        <div class="search-item">
          <CustomButton @click="openAddDialog" :height="34">
            <i class="jt-20-add"></i>新增债务人
          </CustomButton>
        </div>
      </div>
    </div>
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column type="index" label="序号" width="80" align="center">
          <template #default="{ $index }">
            {{ pageSize * (page - 1) + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="类型" width="140" align="center">
          <template #default="{ row }">
            {{ getDebtorTypeLabel(row.type) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="name" label="姓名" min-width="200" />
        <el-table-column label="证件类型" width="120" align="center">
          <template #default="{ row }">
            {{ getCertificateTypeLabel(row.certificate_type) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="certificate_number" label="证件号码" min-width="190" />
        <el-table-column align="center" prop="phone" label="联系电话" width="140">
          <template #default="{ row }">
            {{ row.phone}}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="email" label="联系邮箱" width="180">
          <template #default="{ row }">
            {{ row.email}}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="address" label="联系地址" min-width="200">
          <template #default="{ row }">
            {{ row.address}}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="wechat" label="联系微信" min-width="200">
          <template #default="{ row }">
            {{ row.wechat}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <div class="operation-buttons">
              <div @click="openEditDialog(row)" class="operation-btn edit-btn">编辑</div>
              <div @click="deleteDebtor(row)" class="operation-btn delete-btn">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="page"
          :page-size="pageSize"
          @current-change="handlePageChange" />
      </div>
    </div>

    <!-- 新增弹框 -->
    <AddDebtor 
      :visible="showAddDialog"
      @update:visible="closeAddDialog"
      @confirm="handleAddConfirm" />

    <!-- 编辑弹框 -->
    <EditDebtor
      :visible="showEditDialog"
      :Debtor-data="currentEditItem"
      @update:visible="closeEditDialog"
      @confirm="handleEditConfirm" />

    <!-- 删除确认弹框 -->
    <DeleteConfirmDialog
      :visible="showDeleteDialog"
      title="删除债权人"
      :message="`确定要删除债权人「${deleteDebtorItem?.debtor_name || deleteDebtorItem?.name}」吗？此操作不可撤销。`"
      confirm-text="确认"
      cancel-text="取消"
      @update:visible="showDeleteDialog = $event"
      @confirm="confirmDeleteDebtor"
      @cancel="showDeleteDialog = false" />
  </div>
</template>

<style lang="scss" scoped>
.Debtor-management-page {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    margin-bottom: 20px;
    
    .search-row {
      display: grid;
      grid-template-columns: 400px 140px;
      gap: 20px;
    }
  }
  
  .table-container {
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>