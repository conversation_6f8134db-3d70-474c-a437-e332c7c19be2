<script lang="ts">
export default {
  name: 'SidebarComp'
}
</script>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue';
import type { Ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { SideMenuItem, ImageBackground, ChildSideMenuItem, GrandChildSideMenuItem } from './type';

const router = useRouter()
const route = useRoute()

const props = defineProps<{
  background?: string,
  childImage?: ImageBackground,
  parentImage?: ImageBackground,
  menuList: SideMenuItem[]
}>()

const emit = defineEmits<{
  (e: 'click-parent', menuItem: SideMenuItem, index: number): void
  (e: 'click-child', childMenuItem: ChildSideMenuItem, index: number): void
  (e: 'click-grandchild', grandChildMenuItem: GrandChildSideMenuItem, parentIndex: number, childIndex: number, grandChildIndex: number): void
}>()

// 菜单状态管理
let parentIndex = ref(0) // 当前激活的一级菜单索引
let childIndex: Ref<undefined | number> = ref(0) // 当前激活的二级菜单索引
let grandChildIndex: Ref<undefined | number> = ref(undefined) // 当前激活的三级菜单索引

const menuListParsed = JSON.parse(JSON.stringify(props.menuList))
let menuListInner = reactive(menuListParsed)

// 一级菜单点击处理（原父级菜单）
function clickParentMenu(index: number) {
  // 如果一级菜单没有子菜单，直接跳转
  if(!menuListInner[index].children || menuListInner[index].children.length <= 0) {
    // 重置其他菜单状态
    resetMenuStates()
    menuListInner[index].isActive = true
    const { link } = menuListInner[index]
    router.push(link)
    parentIndex.value = index
    childIndex.value = undefined
    grandChildIndex.value = undefined
  }
  // 切换折叠状态
  menuListInner[index].isCollapse = !menuListInner[index].isCollapse
  /* // 只切换折叠状态，保持位置固定
  menuListInner[index].isCollapse = !menuListInner[index].isCollapse; */
}

// 二级菜单点击处理（原子级菜单）
function clickChildMenu(pindex: number, index: number) {
  const child = menuListInner[pindex].children[index];
  
  // 如果二级菜单有三级子菜单，只切换折叠状态
  if (child.children && child.children.length > 0) {
    child.isChildCollapse = !child.isChildCollapse;
    return;
  }
  
  // 如果二级菜单没有子菜单，直接跳转
  resetMenuStates()
  menuListInner[pindex].isActive = true
  menuListInner[pindex].isCollapse = true
  menuListInner[pindex].children[index].isChildActive = true
  parentIndex.value = pindex
  childIndex.value = index
  grandChildIndex.value = undefined
  
  if (child.link) {
    router.push(child.link)
  }
}

// 三级菜单点击处理（新增）
function clickGrandChildMenu(pindex: number, cindex: number, gindex: number) {
  resetMenuStates()
  
  // 激活对应的菜单层级
  menuListInner[pindex].isActive = true
  menuListInner[pindex].isCollapse = true
  menuListInner[pindex].children[cindex].isChildActive = true
  menuListInner[pindex].children[cindex].isChildCollapse = true
  menuListInner[pindex].children[cindex].children[gindex].isGrandChildActive = true
  
  parentIndex.value = pindex
  childIndex.value = cindex
  grandChildIndex.value = gindex
  
  const { link } = menuListInner[pindex].children[cindex].children[gindex]
  router.push(link)
}

// 重置所有菜单状态
function resetMenuStates() {
  menuListInner.forEach((parent: SideMenuItem) => {
    parent.isActive = false
    if (parent.children) {
      parent.children.forEach((child: ChildSideMenuItem) => {
        child.isChildActive = false
        if (child.children) {
          child.children.forEach((grandChild: GrandChildSideMenuItem) => {
            grandChild.isGrandChildActive = false
          })
        }
      })
    }
  })
}

// 组件挂载时初始化菜单状态
onMounted(() => {
  menuListInner.forEach((element: SideMenuItem, pIndex: number) => {
    element.children.forEach((child: ChildSideMenuItem, cIndex: number) => {
      // 检查二级菜单路由匹配
      if (child.link && child.link.indexOf(route.path) > -1) {
        // 展开对应的顶级菜单，但不改变其他状态
        menuListInner[pIndex].isCollapse = true
        clickChildMenu(pIndex, cIndex)
        return
      }
      // 检查三级菜单路由匹配
      if (child.children) {
        child.children.forEach((grandChild: GrandChildSideMenuItem, gIndex: number) => {
          if (grandChild.link.indexOf(route.path) > -1) {
            // 展开对应的顶级菜单和一级菜单
            menuListInner[pIndex].isCollapse = true
            child.isChildCollapse = true
            clickGrandChildMenu(pIndex, cIndex, gIndex)
          }
        })
      }
    })
  })
})
</script>

<template>
  <div class="side-bar">
    <ul class="parent-level">
      <!-- 一级菜单循环 -->
      <li v-for="({isActive, isCollapse, parent, children}, pIndex) in menuListInner" :key="pIndex" 
        :class="['parent-list-item']">
        <!-- 一级菜单标题 -->
        <div 
          :class="['parent-title', props.parentImage ? (isActive ? props.parentImage[0] : props.parentImage[1]) : (isActive ? 'default-active-parent': '')]"
          @click="clickParentMenu(pIndex)">
          <div class="parent-title-content">
            <slot name="custom-parent">
              {{ parent }}
            </slot>
            <!-- 顶级菜单箭头图标 -->
            <span 
              v-if="children && children.length > 0">
              <i :class="['arrow-icon', isCollapse ? 'jt-16-arrow-up' : 'jt-16-arrow-down']"></i>
            </span>
          </div>
        </div>
        
        <!-- 二级菜单容器 -->
        <ElCollapseTransition>
          <ul class="child-level" v-if="children && children.length > 0 && isCollapse">
            <template v-for="(child, cIndex) in children" :key="cIndex">
              <li :class="['child-list-item']">
                <!-- 二级菜单项 -->
                <div 
                  :class="[
                    'child-menu-title',
                    props.childImage ? (child.isChildActive ? props.childImage[0] : props.childImage[1]) : (child.isChildActive ? 'default-active-child': '')
                  ]"
                  @click="clickChildMenu(pIndex, cIndex)">
                  <div class="child-title-content">
                    <div class="child-title-left">
                      <i :class="child.icon"></i>
                      <slot name="custom-child">
                        {{ child.name }}
                      </slot>
                    </div>
                    <!-- 一级菜单箭头图标 -->
                    <span 
                      v-if="child.children && child.children.length > 0"
                      :class="['arrow-icon', child.isChildCollapse ? 'jt-16-arrow-up' : 'jt-16-arrow-down']">
                      <!-- ▼ -->
                    </span>
                  </div>
                </div>
                
                <!-- 三级菜单容器 -->
                <ElCollapseTransition>
                  <ul class="grandchild-level" v-if="child.children && child.children.length > 0 && child.isChildCollapse">
                    <li 
                      v-for="(grandChild, gIndex) in child.children" :key="gIndex"
                      :class="[
                        'grandchild-list-item',
                        grandChild.isGrandChildActive ? 'default-active-grandchild' : ''
                      ]"
                      @click="clickGrandChildMenu(pIndex, cIndex, gIndex)">
                      <i :class="grandChild.icon"></i>
                      <span>{{ grandChild.name }}</span>
                    </li>
                  </ul>
                </ElCollapseTransition>
              </li>
            </template>
          </ul>
        </ElCollapseTransition>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.side-bar {
  width: 200px;
  background-color: #1377c4;
  background-repeat: no-repeat;
  background-position: bottom;
  
  .parent-level {
    display: flex;
    // justify-content: center;
    flex-direction: column;
    // align-items: center;
    margin-top: 16px;
    
    .parent-list-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      // 一级菜单标题样式
      .parent-title {
        text-align: left;
        margin-left: 15px;
        margin-right: 15px;
        margin-top: 18px;
        font-size: 16px;
        color: rgb(255 255 255 / 62%);
        cursor: pointer;
        user-select: none;
        
        .parent-title-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          
          .arrow-icon {
            font-size: 12px;
            transition: transform 0.3s ease;
            margin-left: 10px;
            
            &.arrow-down {
              transform: rotate(0deg);
            }
            
            &.arrow-up {
              transform: rotate(180deg);
            }
          }
        }
      }
      
      // 二级菜单容器样式
      .child-level {
        margin-top: 10px;
        
        .child-list-item {
          // 二级菜单标题样式
          .child-menu-title {
            width: 210px;
            margin-top: 10px;
            margin-right: 14px;
            text-align: center;
            font-size: 16px;
            color: #fff;
            height: 50px;
            line-height: 50px;
            background-repeat: no-repeat;
            cursor: pointer;
            user-select: none;
            
                          .child-title-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-left: 30px;
                padding-right: 25px;
                height: 100%;
              
              .child-title-left {
                display: flex;
                align-items: center;
                
                i {
                  margin-right: 5px;
                }
              }
              
              .arrow-icon {
                font-size: 10px;
                transition: transform 0.3s ease;
                opacity: 0.8;
                
                &.arrow-down {
                  transform: rotate(0deg);
                }
                
                &.arrow-up {
                  transform: rotate(180deg);
                }
              }
            }
          }
          
          // 三级菜单容器样式（新增）
          .grandchild-level {
            // margin-left: 20px;
            
            .grandchild-list-item {
              width: 190px;
              margin-top: 8px;
              margin-right: 14px;
              padding-left: 51px;
              // text-align: center;
              font-size: 14px;
              color: #fff;
              height: 40px;
              line-height: 40px;
              background-repeat: no-repeat;
              cursor: pointer;
              user-select: none;
              // border-left: 2px solid transparent;
              
              i {
                margin-right: 5px;
              }
              
              // 三级菜单激活状态
              &.default-active-grandchild {
                background-color: #0d5aa7 !important;
                border-left-color: #fff;
              }
              
              &:hover {
                background-color: rgba(255, 255, 255, 0.1);
              }
            }
          }
        }
      }
    }
  }
}

// 保持原有的激活状态样式
.default-active-child {
  transition: background-image .38s;
  background-color: #1468b1 !important;
}
</style>