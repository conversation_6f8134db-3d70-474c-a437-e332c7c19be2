<script lang="ts" setup>
import { ref, type Ref, watch, computed } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
import { ElMessage } from "element-plus";
import type { FieldType, PlanField, EditPlanParams, Plan, UploadFile } from '../auth/type';
import { FieldType as FieldTypeEnum } from '../auth/type';

const props = defineProps<{
  showDialog: boolean,
  planData: Plan  // 要编辑的方案数据
}>()

const emit = defineEmits<{
  (e: 'close'): void,
  (e: 'ensure', params: EditPlanParams): void
}>()

// 表单引用
const planFormRef = ref()
const loading = ref(false)

// 方案基础信息
const planForm = ref({
  title: ''
})

// 动态字段列表（编辑模式下不能新增删除字段，只能修改值）
const dynamicFields: Ref<PlanField[]> = ref([])

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入方案名称', trigger: 'blur' },
    { min: 2, max: 50, message: '方案名称长度在2到50个字符', trigger: 'blur' }
  ]
}

// 监听弹框显示状态
watch(() => props.showDialog, onOpenDialog)

// 监听方案数据变化
watch(() => props.planData, loadPlanData, { deep: true })

// 弹框打开时加载数据
function onOpenDialog(newVal: boolean) {
  if(newVal) {
    loadPlanData(props.planData)
  }
}

// 加载方案数据
function loadPlanData(planData: Plan) {
  if (!planData) return
  
  planForm.value.title = planData.title || ''
  dynamicFields.value = planData.fields ? JSON.parse(JSON.stringify(planData.fields)) : []
}

// 关闭弹框
function close() {
  emit('close')
}

// 文件上传处理（编辑模式下允许修改文件）
function handleFileChange(file: any, field: PlanField) {
  if (!field.fileList) field.fileList = []
  
  const uploadFile: UploadFile = {
    id: 'edit_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
    name: file.name,
    size: file.size,
    type: file.type,
    url: URL.createObjectURL(file)
  }
  
  field.fileList.push(uploadFile)
  field.value = field.fileList.map(f => f.name)
}

// 删除文件
function removeFile(field: PlanField, fileIndex: number) {
  if (field.fileList) {
    field.fileList.splice(fileIndex, 1)
    field.value = field.fileList.map(f => f.name)
  }
}

// 生成微信小程序渲染格式的JSON数据
function generateWechatRenderData(): string {
  const renderData = {
    planTitle: planForm.value.title,
    sections: dynamicFields.value.map(field => ({
      title: field.title,
      content: formatFieldValueForRender(field),
      type: field.type,
      fileList: field.type === FieldTypeEnum.FILE ? field.fileList?.map(f => f.name) || [] : undefined
    }))
  }
  return JSON.stringify(renderData, null, 2)
}

// 格式化字段值用于渲染
function formatFieldValueForRender(field: PlanField): string {
  switch (field.type) {
    case FieldTypeEnum.DATE:
      return field.value ? new Date(field.value).toLocaleDateString() : ''
    case FieldTypeEnum.AMOUNT:
      return field.value ? `¥${Number(field.value).toFixed(2)}` : '¥0.00'
    case FieldTypeEnum.FILE:
      return field.fileList?.length ? `${field.fileList.length}个文件` : '无文件'
    default:
      return String(field.value || '')
  }
}

// 验证动态字段
function validateDynamicFields(): boolean {
  for (let i = 0; i < dynamicFields.value.length; i++) {
    const field = dynamicFields.value[i]
    
    if (field.required) {
      if (field.type === FieldTypeEnum.FILE) {
        if (!field.fileList || field.fileList.length === 0) {
          ElMessage.error(`${field.title}是必填字段，请上传文件`)
          return false
        }
      } else if (!field.value || String(field.value).trim() === '') {
        ElMessage.error(`${field.title}是必填字段，请填写内容`)
        return false
      }
    }
  }
  return true
}

// 获取字段类型显示名称
function getFieldTypeLabel(type: FieldType): string {
  const typeMap: Record<FieldType, string> = {
    [FieldTypeEnum.TEXT]: '单行文本',
    [FieldTypeEnum.TEXTAREA]: '多行文本',
    [FieldTypeEnum.DATE]: '日期选择',
    [FieldTypeEnum.AMOUNT]: '金额输入',
    [FieldTypeEnum.FILE]: '文件上传'
  }
  return typeMap[type] || '未知类型'
}

// 确认编辑方案
async function ensureEdit() {
  if (!planFormRef.value) return
  
  loading.value = true
  try {
    // 验证基础表单
    const isValidForm = await new Promise((resolve) => {
      planFormRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValidForm) return
    
    // 验证动态字段
    if (!validateDynamicFields()) return
    
    // 构造提交数据
    const params: EditPlanParams = {
      id: props.planData.id!,
      title: planForm.value.title,
      fields: dynamicFields.value,
      jsonData: generateWechatRenderData()
    }
    
    emit('ensure', params)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog :visible="showDialog" @update:visible="close" width="1200px" title="编辑方案">
    <div class="edit-plan-content">
      <!-- 基础信息表单 -->
      <el-form ref="planFormRef" :model="planForm" :rules="rules" label-width="100px">
        <el-form-item label="方案名称" prop="title">
          <el-input 
            v-model="planForm.title" 
            placeholder="请输入方案名称"
            maxlength="50"
            show-word-limit />
        </el-form-item>
      </el-form>

      <!-- 动态字段编辑区域 -->
      <div class="dynamic-fields-section">
        <div class="section-header">
          <h3>字段内容编辑</h3>
          <el-tag type="info" size="small">编辑模式：只能修改字段内容，不能新增或删除字段</el-tag>
        </div>

        <!-- 字段列表 -->
        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div 
            v-for="(field, index) in dynamicFields" 
            :key="field.id"
            class="field-item">
            
            <!-- 字段头部：显示字段信息 -->
            <div class="field-header">
              <div class="field-info">
                <span class="field-title">{{ field.title }}</span>
                <el-tag size="small" :type="field.required ? 'danger' : 'info'">
                  {{ getFieldTypeLabel(field.type) }}{{ field.required ? ' (必填)' : '' }}
                </el-tag>
              </div>
            </div>

            <!-- 字段内容编辑 -->
            <div class="field-content">
              <label class="content-label">字段内容：</label>
              <div class="content-input">
                <!-- 单行文本 -->
                <el-input 
                  v-if="field.type === FieldTypeEnum.TEXT"
                  v-model="field.value"
                  :placeholder="field.placeholder || '请输入内容'" />
                
                <!-- 多行文本 -->
                <el-input 
                  v-else-if="field.type === FieldTypeEnum.TEXTAREA"
                  v-model="field.value"
                  type="textarea"
                  :rows="3"
                  :placeholder="field.placeholder || '请输入内容'" />
                
                <!-- 日期选择 -->
                <el-date-picker
                  v-else-if="field.type === FieldTypeEnum.DATE"
                  v-model="field.value"
                  type="date"
                  :placeholder="field.placeholder || '选择日期'"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />
                
                <!-- 金额输入 -->
                <el-input-number
                  v-else-if="field.type === FieldTypeEnum.AMOUNT"
                  v-model="field.value"
                  :min="0"
                  :precision="2"
                  :placeholder="field.placeholder || '请输入金额'"
                  style="width: 200px;" />
                
                <!-- 文件上传 -->
                <div v-else-if="field.type === FieldTypeEnum.FILE" class="file-upload-area">
                  <el-upload
                    :auto-upload="false"
                    :on-change="(file: any) => handleFileChange(file.raw, field)"
                    :show-file-list="false"
                    multiple>
                    <CustomButton :height="32">
                      <i class="jt-20-upload"></i>选择文件
                    </CustomButton>
                  </el-upload>
                  
                  <!-- 已选文件列表 -->
                  <div v-if="field.fileList && field.fileList.length > 0" class="file-list">
                    <div 
                      v-for="(file, fileIndex) in field.fileList" 
                      :key="file.id"
                      class="file-item">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-size" v-if="file.size">({{ (file.size / 1024).toFixed(1) }}KB)</span>
                      <i 
                        class="jt-20-delete file-remove" 
                        @click="removeFile(field, fileIndex)"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-fields">
          <p>该方案暂无配置字段</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认保存
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-plan-content {
  .dynamic-fields-section {
    margin-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
      }
    }
    
    .empty-fields {
      text-align: center;
      padding: 40px 20px;
      color: #999;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px dashed #ddd;
    }
  }
  
  .fields-list {
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
      background-color: #fafafa;
      
      .field-header {
        margin-bottom: 12px;
        
        .field-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .field-title {
            font-weight: bold;
            color: #333;
            font-size: 16px;
          }
        }
      }
      
      .field-content {
        .content-label {
          display: block;
          margin-bottom: 8px;
          font-weight: bold;
          color: #666;
          font-size: 14px;
        }
        
        .content-input {
          .file-upload-area {
            .file-list {
              margin-top: 12px;
              
              .file-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px;
                background-color: #fff;
                border: 1px solid #e6e6e6;
                border-radius: 4px;
                margin-bottom: 8px;
                
                .file-name {
                  flex: 1;
                  color: #333;
                }
                
                .file-size {
                  color: #999;
                  font-size: 12px;
                }
                
                .file-remove {
                  color: #f56c6c;
                  cursor: pointer;
                  
                  &:hover {
                    color: #f78989;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding-top: 20px;
    border-top: 1px solid #e6e6e6;
  }
}
</style>