import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUserInfo } from "@/axios/login"
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 用户权限列表
  const userPermissions = ref<string[]>([])
  // 用户信息
  const userInfo = ref<any>({})
  // 是否已加载权限
  const permissionsLoaded = ref(false)

  /**
   * 获取用户信息和权限
   */
  async function fetchUserInfo() {
    try {
      const { data } = await getUserInfo({})
      const { state, msg, data: responseData } = data
      if (state === 'success') {
        // 存储用户基础信息
        userInfo.value = responseData
        
        // 检查并存储token信息
        if (responseData.access_token) {
          sessionStorage.setItem('access_token', responseData.access_token)
        }
        if (responseData.refresh_token) {
          sessionStorage.setItem('refresh_token', responseData.refresh_token)
        }
        if (responseData.token_type) {
          sessionStorage.setItem('token_type', responseData.token_type)
        }
        if (responseData.username) {
          sessionStorage.setItem('username', responseData.username)
        }
        if (responseData.group_name) {
          sessionStorage.setItem('group_name', responseData.group_name)
        }
        if (responseData.role_name) {
          sessionStorage.setItem('role_name', responseData.role_name)
        }
        
        // 详细的权限数据分析和提取
        if (responseData.user_all_permissions) {
          // 存储完整权限数据到sessionStorage供调试
          sessionStorage.setItem('user_all_permissions', JSON.stringify(responseData.user_all_permissions))
          
          if (Array.isArray(responseData.user_all_permissions)) {
            // 递归提取所有权限名称，处理嵌套结构
            const extractPermissionNames = (permissions: any[]): string[] => {
              const names: string[] = []
              permissions.forEach(p => {
                // 尝试多种可能的权限名称字段
                const permName = p.name || p.codename || p.permission_name || p.code || String(p)
                if (permName && typeof permName === 'string') {
                  names.push(permName)
                }
                
                // 如果有children属性，递归处理
                if (p.children && Array.isArray(p.children)) {
                  names.push(...extractPermissionNames(p.children))
                }
                
                // 如果有permissions属性，递归处理
                if (p.permissions && Array.isArray(p.permissions)) {
                  names.push(...extractPermissionNames(p.permissions))
                }
              })
              return names
            }
            
            userPermissions.value = extractPermissionNames(responseData.user_all_permissions)
          }
        }
        
        permissionsLoaded.value = true
        console.log('=== 权限加载完成 ===', userPermissions.value.length, '个权限')
        return true
      } else {
        ElMessage.error(msg || '获取用户信息失败')
        return false
      }
    } catch (error) {
      console.error('获取用户信息异常:', error)
      return false
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  function hasPermission(permission: string): boolean {
    return userPermissions.value.includes(permission)
  }

  /**
   * 检查用户是否有任意一个权限
   */
  function hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => userPermissions.value.includes(permission))
  }

  /**
   * 重置用户状态
   */
  function resetUserState() {
    userPermissions.value = []
    userInfo.value = {}
    permissionsLoaded.value = false
  }

  return {
    userPermissions,
    userInfo,
    permissionsLoaded,
    fetchUserInfo,
    hasPermission,
    hasAnyPermission,
    resetUserState
  }
}) 