<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Close } from '@element-plus/icons-vue'

// 组件属性定义
interface Props {
  modelValue?: File | null           // v-model 绑定的文件
  accept?: string                    // 接受的文件类型
  maxSize?: number                   // 最大文件大小（MB）
  placeholder?: string               // 占位符文本
  disabled?: boolean                 // 是否禁用
  multiple?: boolean                 // 是否支持多文件（暂不实现，为将来扩展预留）
  validateType?: (file: File) => boolean  // 自定义文件类型验证
}

// 组件事件定义
interface Emits {
  (e: 'update:modelValue', value: File | null): void
  (e: 'change', file: File | null): void
  (e: 'error', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  accept: '*',
  maxSize: 100,
  placeholder: '选择文件',
  disabled: false,
  multiple: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const uploadRef = ref()
const selectedFile = ref<File | null>(props.modelValue || null)

// 计算属性
const fileInfo = computed(() => {
  if (!selectedFile.value) return null
  return {
    name: selectedFile.value.name,
    size: selectedFile.value.size,
    type: selectedFile.value.type
  }
})

/**
 * 文件选择变化处理
 * @param uploadFile 上传文件对象
 */
function handleFileChange(uploadFile: any) {
  const file = uploadFile.raw
  
  // 自定义文件类型验证
  if (props.validateType && !props.validateType(file)) {
    clearFile()
    return
  }
  
  // 默认文件类型验证（如果没有自定义验证）
  if (!props.validateType && props.accept !== '*') {
    const acceptTypes = props.accept.split(',').map(type => type.trim())
    const isValidType = acceptTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase())
      } else {
        return file.type === type
      }
    })
    
    if (!isValidType) {
      const message = `只能上传 ${props.accept} 格式的文件！`
      ElMessage.error(message)
      emit('error', message)
      clearFile()
      return
    }
  }

  // 检查文件大小
  const isValidSize = file.size / 1024 / 1024 <= props.maxSize
  if (!isValidSize) {
    const message = `上传文件大小不能超过 ${props.maxSize}MB！`
    ElMessage.error(message)
    emit('error', message)
    clearFile()
    return
  }

  // 文件验证通过，保存文件信息
  selectedFile.value = file
  emit('update:modelValue', file)
  emit('change', file)
  console.log('文件选择成功:', file.name)
}

/**
 * 文件移除处理
 */
function handleRemove() {
  selectedFile.value = null
  emit('update:modelValue', null)
  emit('change', null)
}

/**
 * 清除文件
 */
function clearFile() {
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  handleRemove()
}

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 */
function formatFileSize(size: number): string {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}

// 暴露方法给父组件
defineExpose({
  clearFile
})
</script>

<template>
  <div class="file-upload-component">
    <!-- 文件选择器 -->
    <div class="upload-container">
      <el-upload
        ref="uploadRef"
        class="file-upload"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleRemove"
        :show-file-list="false"
        :limit="1"
        :accept="accept"
        :disabled="disabled">
        
        <template #trigger>
          <button class="upload-trigger-btn" :disabled="disabled">
            <i class="jt-20-upload"></i>{{ placeholder }}
          </button>
        </template>
      </el-upload>
      
      <div class="upload-tip" v-if="accept !== '*' || maxSize < 100">
        <span v-if="accept !== '*'">只能上传 {{ accept }} 文件</span>
        <span v-if="accept !== '*' && maxSize < 100">，</span>
        <span v-if="maxSize < 100">且不超过 {{ maxSize }}MB</span>
      </div>
    </div>
    
    <!-- 显示已选择的文件信息 -->
    <div v-if="fileInfo" class="file-info">
      <!-- <el-icon><Document /></el-icon> -->
      <span class="file-name" :title="fileInfo.name">{{ fileInfo.name }}</span>
      <!-- <span class="file-size">({{ formatFileSize(fileInfo.size) }})</span> -->
      <el-icon class="remove-icon" @click="handleRemove" v-if="!disabled">
        <Close />
      </el-icon>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.file-upload-component {
  .upload-container {
    .file-upload {
      margin-bottom: 8px;
    }
    
    .upload-trigger-btn {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      background-color: #fff;
      border: 1px solid #c0c4cc;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s;
      
      // &:hover:not(:disabled) {
      //   background: #66b1ff;
      // }
      
      // &:active:not(:disabled) {
      //   background: #3a8ee6;
      // }
      
      &:disabled {
        background-color: #c0c4cc;
        cursor: not-allowed;
      }
      
      i {
        font-size: 14px;
      }
    }
    
    .upload-tip {
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
      margin-top: 4px;
    }
  }
  
  .file-info {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 175px;
    height: 22px;
    
    /* .el-icon {
      color: #409eff;
      font-size: 16px;
      flex-shrink: 0;
    } */
    
    .file-name {
      color: #303133;
      font-size: 14px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 200px;
    }
    
    .file-size {
      color: #909399;
      font-size: 12px;
      flex-shrink: 0;
    }
    
    .remove-icon {
      color: #f56c6c;
      cursor: pointer;
      flex-shrink: 0;
      
      &:hover {
        color: #f78989;
      }
    }
  }
}
</style>
