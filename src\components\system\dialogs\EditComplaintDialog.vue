<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'

/**
 * 投诉反馈数据接口
 */
interface complaintFeedback {
  id: number
  case_number: string        // 案件编号
  feedback_type_cn: string // 反馈类型
  category_cn: string   // 具体类别
  description: string // 详细描述
  phone_number: string       // 联系方式
  process_status: string // 处理状态
  handler: string          // 处理人
  handle_time: string       // 处理时间
  handle_result: string     // 处理结果
  created_at: string         // 创建时间
  updated_at: string         // 更新时间
}

/**
 * 组件Props接口
 */
interface Props {
  visible: boolean
  complaintData: complaintFeedback | null
}

/**
 * 组件Emits接口
 */
interface Emits {
  (e: 'update:visible'): void
  (e: 'confirm', data: { handler: string; handle_time: string; process_status: string; handle_result: string }): void
}

// Props和Emits定义
const props = withDefaults(defineProps<Props>(), {
  visible: false,
  complaintData: null
})

const emit = defineEmits<Emits>() 

// 获取登录用户名称赋值给editForm.handler
const userName = localStorage.getItem('userName')

// 响应式数据
const loading = ref(false)
const editForm = ref({
  handler: userName || '',
  handle_time: '',
  process_status: '',
  handle_result: ''
})

/**
 * 处理状态选项
 */
const processStatusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已解决', value: 'resolved' },
  { label: '已关闭', value: 'closed' }
]

/**
 * 反馈类型选项
 */
const feedbackTypeOptions = [
  { label: '意见建议', value: 'suggestion' },
  { label: '服务投诉', value: 'complaint '},
]

/**
 * 获取反馈类型显示文本
 */
const feedbackTypeLabel = computed(() => {
  if (!props.complaintData) return ''
  const option = feedbackTypeOptions.find(opt => opt.value === props.complaintData!.feedback_type_cn)
  return option?.label || props.complaintData.feedback_type_cn
})

/**
 * 格式化日期显示
 */
function formatDate(dateString: string): string {
  if (!dateString) return '-'
  return dateString.split('T')[0]
}

/**
 * 监听弹框显示状态，初始化表单数据
 */
watch(() => [props.visible, props.complaintData], ([visible, data]) => {
  if (visible && data && typeof data === 'object') {
    // 获取详细信息（模拟API调用）
    fetchComplaintDetail(data.id)
  }
}, { immediate: true })

/**
 * 获取投诉反馈详细信息
 */
async function fetchComplaintDetail(id: number) {
  if (!props.complaintData) return
  
  try {
    loading.value = true
    
    // 模拟API调用：PATCH /feedback/feedback/{id}/
    console.log('获取投诉反馈详细信息:', id)
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 初始化编辑表单
    editForm.value = {
      handler: props.complaintData.handler || '',
      handle_time: props.complaintData.handle_time || '',
      process_status: props.complaintData.process_status,
      handle_result: props.complaintData.handle_result || ''
    }
    
    console.log('获取详细信息成功:', editForm.value)
  } catch (error) {
    console.error('获取详细信息失败:', error)
    ElMessage.error('获取详细信息失败')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭弹框
 */
function closeDialog() {
  emit('update:visible')
}

/**
 * 确认提交
 */
async function handleConfirm() {
  if (!props.complaintData) {
    ElMessage.warning('数据异常，请重试')
    return
  }
  
  // 验证必填项
  if (!editForm.value.process_status) {
    ElMessage.warning('请选择处理状态')
    return
  }
  
  if (editForm.value.process_status === '已解决' && !editForm.value.handle_result.trim()) {
    ElMessage.warning('处理状态为已解决时，处理结果不能为空')
    return
  }
  
  try {
    loading.value = true
    
    // 模拟API调用：POST /feedback/feedback/{id}/handle/
    console.log('提交处理结果:', {
      id: props.complaintData.id,
      ...editForm.value
    })
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 发射确认事件
    emit('confirm', {
      handler: editForm.value.handler,
      handle_time: editForm.value.handle_time,
      process_status: editForm.value.process_status,
      handle_result: editForm.value.handle_result.trim()
    })
    
    console.log('处理完成')
  } catch (error) {
    console.error('处理失败:', error)
    ElMessage.error('处理失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 重置表单
 */
function resetForm() {
  editForm.value = {
    handler: '',
    handle_time: '',
    process_status: '',
    handle_result: ''
  }
}

/**
 * 组件卸载时重置表单
 */
watch(() => props.visible, (visible) => {
  if (!visible) {
    resetForm()
  }
})
</script>

<template>
  <CustomDialog
    :visible="props.visible"
    title="编辑投诉反馈"
    width="600px"
    @update:visible="closeDialog">
    
    <div v-loading="loading" class="edit-complaint-form">
      <el-form :model="editForm" label-width="120px" label-position="right">
        
        <!-- 基本信息（只读） -->
        <div class="readonly-section">          
          <el-form-item label="案件编号：">
            <el-input 
              :value="complaintData?.case_number" 
              disabled 
              class="readonly-input" />
          </el-form-item>
          
          <el-form-item label="反馈类型：">
            <el-input 
              :value="feedbackTypeLabel" 
              disabled 
              class="readonly-input" />
          </el-form-item>
          
          <el-form-item label="具体类别：">
            <el-input 
              :value="complaintData?.category_cn" 
              disabled 
              class="readonly-input" />
          </el-form-item>
          
          <el-form-item label="详细描述：">
            <el-input 
              type="textarea" 
              :value="complaintData?.description" 
              disabled 
              :rows="3"
              class="readonly-textarea" />
          </el-form-item>
          
          <el-form-item label="联系方式：">
            <el-input 
              :value="complaintData?.phone_number" 
              disabled 
              class="readonly-input" />
          </el-form-item>
        </div>
        
        <!-- 可编辑部分 -->
        <div class="editable-section">
          <el-form-item label="处理人：">
            <el-input 
              v-model="editForm.handler" 
              placeholder="请输入处理人" />
          </el-form-item>
          
          <el-form-item label="处理时间：">
              <el-date-picker
                v-model="editForm.handle_time"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择处理时间"
                style="width: 100%" />
          </el-form-item>
          <el-form-item label="处理状态：">
            <el-select 
              v-model="editForm.process_status" 
              placeholder="请选择处理状态"
              style="width: 100%">
              <el-option
                v-for="option in processStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="处理结果：">
            <el-input 
              v-model="editForm.handle_result"
              type="textarea"
              placeholder="请输入处理结果"
              :rows="4"
              maxlength="500"
              show-word-limit />
          </el-form-item>
        </div>
      </el-form>

      <!-- 操作按钮 -->
      <div class="dialog-footer">
        <CustomButton @click="handleConfirm" :height="34" :loading="loading" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="closeDialog" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-complaint-form {
  .readonly-section {
    margin-bottom: 30px;
    
    :deep(.readonly-input) {
      .el-input__inner {
        background-color: #f5f7fa;
        color: #606266;
        cursor: not-allowed;
      }
    }
    
    :deep(.readonly-textarea) {
      .el-textarea__inner {
        background-color: #f5f7fa;
        color: #606266;
        cursor: not-allowed;
        resize: none;
      }
    }
  }
  
  .editable-section {
    :deep(.el-form-item__label) {
      color: #333;
      font-weight: 500;
    }
    
    :deep(.el-select) {
      width: 100%;
    }
    
    :deep(.el-textarea__inner) {
      resize: none;
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 20px 24px;
  margin: 20px -24px -24px -24px;
  
  .el-button {
    min-width: 80px;
  }
}

// 响应式适配
@media (max-width: 640px) {
  .edit-complaint-form {
    :deep(.el-form) {
      .el-form-item {
        .el-form-item__label {
          width: 100px !important;
          font-size: 14px;
        }
      }
    }
  }
  
  .dialog-footer {
    padding: 16px 20px;
    
    .el-button {
      flex: 1;
      margin: 0;
    }
  }
}
</style> 