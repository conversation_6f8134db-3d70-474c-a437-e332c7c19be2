import type { Ref, ComponentPublicInstance } from "vue"
import { isFunction } from "@vue/shared"

export const composeRefs = (...refs: (Ref<HTMLElement | undefined>)[]) => {
  return (el: Element | ComponentPublicInstance | null) => {
    refs.forEach((ref) => {
      if(isFunction(ref)) {
        ref(el as Element | ComponentPublicInstance)
      } else {
        ref.value = el as HTMLElement | undefined
      }
    })
  }
}