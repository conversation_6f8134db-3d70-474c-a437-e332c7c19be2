<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import CustomInput from '@/components/common/CustomInput.vue'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import { getVoiceCallRecords } from "@/axios/system"
import { ElMessage } from 'element-plus'

const loading = ref(false)
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const recordList = ref([])

function setPage1() {
  page.value = 1
  searchRecordList()
}

async function searchRecordList() {
	loading.value = true
	const params = {
		search:search.value,
		page:page.value,
		page_size,
	}
	const { data } = await getVoiceCallRecords(params)
	const { state, msg } = data
	if(state == 'success'){
		recordList.value = data.data.results
		total.value = data.data.count
	}else{
		ElMessage.error(msg)
	}
	loading.value = false
}

function pageChanged(p: number) {
  page.value = p
  searchRecordList()
}

onMounted(() => {
  searchRecordList()
})
</script>

<template>
  <div class="voice-call-record">
    <div class="search-header">
      <CustomInput v-model="search" placeholder="搜索被叫号码、主叫号码、外呼结果备注" @click="setPage1"></CustomInput>
    </div>
    <div>
      <el-table
        :data="recordList"
        border
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle"
        class="record-table"
        v-loading="loading">
        <el-table-column type="index" label="序号" width="60" align="center">
          <template v-slot="{$index}">
            {{page_size * (page - 1) + $index + 1}}
          </template>
        </el-table-column>
        <el-table-column prop="call_start_time" label="呼叫开始时间" align="center" width="190"></el-table-column>
        <el-table-column prop="called_number" label="被叫号码" align="center" width="130"></el-table-column>
        <el-table-column prop="caller_number" label="主叫号码" align="center" width="130"></el-table-column>
        <el-table-column prop="call_status_cn" label="呼叫状态" align="center" width="90">
			<!-- call_status = not_connected(未接通), connected(已接通), busy(忙线), power_off(关机), no_answer(无人接听), invalid_number(无效号码), rejected(拒接), failed(呼叫失败) -->
          <template v-slot="{row}">
            <span
              :class="{
                'text-success': row.call_status_cn === '已接通',
                'text-warning': row.call_status_cn === '未接通',
                'text-danger': row.call_status_cn !== '已接通' && row.call_status_cn !== '未接通'
              }">
              {{ row.call_status_cn }}
            </span>
            <!-- <el-tag
              :type="row.call_status_cn === '已接通' ? 'success' : row.call_status_cn === '未接通' ? 'warning' : 'danger'"
              size="small">
              {{ row.call_status_cn }}
            </el-tag> -->
          </template>
        </el-table-column>
        <el-table-column prop="creditor_name" label="债权人名称" align="center" min-width="120"></el-table-column>
        <el-table-column prop="debtor_name" label="债务人名称" align="center" width="120"></el-table-column>
        <el-table-column prop="call_duration_display" label="通话时长" align="center" width="120"></el-table-column>
        <el-table-column prop="call_end_time" label="呼叫结束时间" align="center" width="190"></el-table-column>
        <el-table-column prop="task_batch_id" label="外呼任务批次号" align="center" width="150"></el-table-column>
        <el-table-column prop="recording_file_name" label="录音文件名" align="center" width="150"></el-table-column>
        <el-table-column prop="call_result_notes" label="外呼结果备注" align="center" width="140" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
	<div class="pagination-wrapper" >
		<el-pagination
		background
		layout="prev, pager, next"
		:total="total"
		:current-page="page"
		:page-size="page_size"
		@current-change="pageChanged"
		></el-pagination>
	</div>
  </div>
</template>

<style lang="scss" scoped>
.voice-call-record {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  .search-header {
    display: grid;
    grid-template-columns: 400px;
    gap: 20px;
    margin-bottom: 20px;
  }
}

.record-table {
  border-radius: 8px;
  overflow: hidden;
}
.text-success{
  color:#1377C4;
}
.text-warning{
  color:#D94223;
}
.text-danger{
  color:#e6a23c;
}
.pagination-wrapper {
  	margin-top: 20px;
    padding-bottom: 20px;
	display: flex;
	justify-content: center; 
}

// 响应式适配
@media (max-width: 1200px) {
  .voice-call-record {
    .search-header {
      grid-template-columns: 1fr;
    }
  }

  .record-table {
    .text-ellipsis {
      max-width: 120px;
    }
  }
}
</style>