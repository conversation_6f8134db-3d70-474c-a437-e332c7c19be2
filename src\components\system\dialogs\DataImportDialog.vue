<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import CustomInput from '@/components/common/CustomInput.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import { getCreditor, addDataImport } from '@/axios/system'

// 债权人选项接口定义
interface CreditorOption {
  id: number
  name: string
}

// 组件属性定义
interface Props {
  visible: boolean              // 弹框显示状态
}

// 组件事件定义
interface Emits {
  (e: 'close'): void           // 关闭弹框
  (e: 'success'): void         // 导入成功
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)

// 表单数据
const formData = reactive({
  package_name: '',          // 资产包名称
  file: null as File | null, // 上传文件
  creditor: ''           // 债权人ID
})

// 债权人选项数据
const creditorOptions = ref<CreditorOption[]>([])
const creditorLoading = ref(false)

/**
 * 监听弹框显示状态，初始化表单数据
 */
watch(() => props.visible, (visible) => {
  if (visible) {
    loadCreditorOptions()
  }
})

/**
 * 加载债权人选项
 */
async function loadCreditorOptions() {
  creditorLoading.value = true
  // 调用实际的债权人列表接口
  const { data } = await getCreditor({ page_size: 1000 })
  const { state, msg } = data
  if(state == 'success'){
    // 转换数据格式
    creditorOptions.value = data.data.results.map((item: any) => ({
      id: item.id,
      name: item.creditor_name
    }))
  }else{
    ElMessage.error(msg)
  }
  creditorLoading.value = false
}

/**
 * 手动触发文件选择
 */
/* function handleFileSelect() {
	uploadRef.value?.clearFiles()
  const input = uploadRef.value?.$el?.querySelector('input[type="file"]')
  if (input) {
    input.click()
  }
} */

/**
 * Excel文件类型验证
 * @param file 文件对象
 */
function validateExcelFile(file: File): boolean {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls')

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return false
  }
  return true
}

/**
 * 文件选择变化处理
 * @param file 选择的文件
 */
function handleFileChange(file: File | null) {
  formData.file = file
}

/**
 * 提交数据导入
 */
async function handleSubmit() {
  // 表单验证
  if (!formData.package_name.trim()) {
    ElMessage.error('请输入资产包名称')
    return
  }

  if (!formData.file) {
    ElMessage.error('请选择要上传的文件')
    return
  }

  // 债权人必填
  if (!formData.creditor) {
    ElMessage.error('请选择债权人')
    return
  }

    loading.value = true
    // 构造FormData
    const uploadData = new FormData()
    uploadData.append('package_name', formData.package_name)
    uploadData.append('file', formData.file)
    // formData.creditor 需要是整型不能是字符串格式
    console.log(formData.creditor,'-----')
    uploadData.append('creditor',formData.creditor)

    // 调用数据导入接口
    const {data} = await addDataImport(uploadData)
    const { state, msg } = data
    if(state === 'success') {
      ElMessage.success(msg)
      emit('success')
      handleCancel()
    }else{
      ElMessage.error(msg)
    }
    loading.value = false
}

/**
 * 取消操作
 */
function handleCancel() {
  // 重置表单
  formData.package_name = ''
  formData.file = null
  formData.creditor = ''

  emit('close')
}
</script>

<template>
  <CustomDialog
    :visible="visible"
    title="数据导入"
    width="600px"
    @update:visible="handleCancel"
    class="data-import-dialog">
    
    <div class="form-container">
      <div class="form-item">
        <label class="form-label"><span class="required">*</span>资产包名称</label>
        <div class="form-input">
          <el-input 
            v-model="formData.package_name"
            placeholder="请输入资产包名称"
            clearable />
        </div>
      </div>
      <div class="form-item">
        <label class="form-label"><span class="required">*</span>上传文件</label>
        <div class="form-input">
          <FileUpload
            v-model="formData.file"
            accept=".xlsx,.xls"
            :max-size="100"
            placeholder="选择文件"
            :validate-type="validateExcelFile"
            @change="handleFileChange" />
        </div>
      </div>
      <div class="form-item">
        <label class="form-label"><span class="required">*</span>债权人</label>
        <div class="form-input">
          <el-select 
            v-model="formData.creditor"
            placeholder="请选择债权人"
            filterable
            clearable
            :loading="creditorLoading"
            style="width: 100%;">
            <el-option
              v-for="option in creditorOptions"
              :key="option.id"
              :label="option.name"
              :value="option.id" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="dialog-footer">
      <CustomButton @click="handleSubmit" :loading="loading" :height="34" btn-type="blue">
        <i class="jt-20-ensure"></i>确认
      </CustomButton>
      <CustomButton @click="handleCancel" :height="34">
        <i class="jt-20-delete"></i>取消
      </CustomButton>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.data-import-dialog {
  .form-container {
    padding: 20px 0;
    
    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24px;
      
      .form-label {
        width: 120px;
        padding-top: 8px;
        color: #303133;
        font-weight: 500;
        font-size: 14px;
        text-align: right;
        margin-right: 16px;
        flex-shrink: 0;
        
        .required {
          color: #f56c6c;
          margin-left: 2px;
        }
      }
      
      .form-input {
        flex: 1;
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    padding-top: 30px;
  }
}

// Element Plus 组件样式覆盖
:deep(.el-select) {
  .el-select__wrapper {
    border-radius: 4px;
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}
</style> 