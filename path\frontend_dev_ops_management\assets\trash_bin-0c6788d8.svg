<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="80" height="80" viewBox="0 0 80 80">
  <defs>
    <style>
      .cls-1, .cls-10, .cls-11, .cls-3, .cls-5, .cls-7, .cls-8 {
        mix-blend-mode: multiply;
      }

      .cls-1, .cls-10, .cls-11, .cls-12, .cls-3, .cls-5, .cls-7, .cls-8 {
        isolation: isolate;
      }

      .cls-1 {
        fill: url(#radial-gradient);
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }

      .cls-3 {
        fill: url(#radial-gradient-2);
      }

      .cls-4 {
        fill: url(#linear-gradient-2);
      }

      .cls-6 {
        fill: url(#linear-gradient-3);
      }

      .cls-7 {
        opacity: 0.74;
        fill: url(#linear-gradient-4);
      }

      .cls-8 {
        fill: url(#linear-gradient-5);
      }

      .cls-9 {
        fill: url(#linear-gradient-6);
      }

      .cls-10 {
        fill: url(#linear-gradient-7);
      }

      .cls-11 {
        fill: url(#linear-gradient-8);
      }

      .cls-12 {
        mix-blend-mode: screen;
        fill: url(#radial-gradient-3);
      }

      .cls-13 {
        fill: url(#linear-gradient-9);
      }

      .cls-14 {
        fill: url(#linear-gradient-10);
      }

      .cls-15 {
        fill: url(#linear-gradient-11);
      }

      .cls-16 {
        fill: url(#linear-gradient-12);
      }

      .cls-17 {
        fill: none;
      }
    </style>
    <radialGradient id="radial-gradient" cx="0.5" cy="0.5" r="4.167" gradientTransform="translate(1.222 -9.722) scale(0.12 0.12)" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a8a8a8"/>
      <stop offset="0.176" stop-color="#afafaf"/>
      <stop offset="0.45" stop-color="#c5c5c5"/>
      <stop offset="0.784" stop-color="#e9e9e9"/>
      <stop offset="0.962" stop-color="#fff"/>
    </radialGradient>
    <linearGradient id="linear-gradient" x1="0.5" y1="0.818" x2="0.5" y2="-0.523" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#878787"/>
      <stop offset="0.264" stop-color="#d1d1d1"/>
      <stop offset="0.687" stop-color="#d6d6d6"/>
      <stop offset="1" stop-color="#a6a6a6"/>
    </linearGradient>
    <radialGradient id="radial-gradient-2" cx="0.527" cy="1.785" r="4.175" gradientTransform="translate(0.382) scale(0.236 1)" gradientUnits="objectBoundingBox">
      <stop offset="0.231" stop-color="#fff"/>
      <stop offset="0.306" stop-color="#eee"/>
      <stop offset="0.452" stop-color="#c4c4c4"/>
      <stop offset="0.656" stop-color="gray"/>
      <stop offset="0.908" stop-color="#232323"/>
      <stop offset="1"/>
    </radialGradient>
    <linearGradient id="linear-gradient-2" x1="0.946" y1="0.5" x2="0.041" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d1d1d1"/>
      <stop offset="0.247" stop-color="#878787"/>
      <stop offset="0.555" stop-color="#d6d6d6"/>
      <stop offset="0.786" stop-color="#fff"/>
      <stop offset="1" stop-color="#a6a6a6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.426" y1="0.5" x2="0.99" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.179" stop-color="#f8f8f8"/>
      <stop offset="0.423" stop-color="#e7e7e7"/>
      <stop offset="0.555" stop-color="#dbdbdb"/>
      <stop offset="1" stop-color="#a4a4a4"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" y1="0.772" x2="0.5" y2="0.219" gradientUnits="objectBoundingBox">
      <stop offset="0.225" stop-color="#fff"/>
      <stop offset="0.291" stop-color="#f5f5f5"/>
      <stop offset="0.4" stop-color="#dcdcdc"/>
      <stop offset="0.54" stop-color="#b2b2b2"/>
      <stop offset="0.704" stop-color="#797979"/>
      <stop offset="0.887" stop-color="#303030"/>
      <stop offset="1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.5" y1="0.501" x2="0.5" y2="0.951" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.555" stop-color="#dbdbdb"/>
      <stop offset="1" stop-color="#a4a4a4"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.923" y1="0.5" x2="0.015" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-7" x1="0.358" y1="0.517" x2="0.985" y2="0.366" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-8" x1="0.5" y1="0.405" x2="0.5" y2="1.081" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.489" stop-color="#dbdbdb"/>
      <stop offset="1" stop-color="#a4a4a4"/>
    </linearGradient>
    <radialGradient id="radial-gradient-3" cx="0.5" cy="0.5" r="25.129" gradientTransform="translate(0.49) scale(0.02 1)" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#474747"/>
      <stop offset="1"/>
    </radialGradient>
    <linearGradient id="linear-gradient-9" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-8"/>
    <linearGradient id="linear-gradient-10" x1="0.5" y1="0.152" x2="0.5" y2="1.062" xlink:href="#linear-gradient-8"/>
    <linearGradient id="linear-gradient-11" x1="0.5" y1="1.414" x2="0.5" y2="-0.511" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#787063"/>
      <stop offset="0.107" stop-color="#665949"/>
      <stop offset="0.284" stop-color="#4c3926"/>
      <stop offset="0.429" stop-color="#3c2610"/>
      <stop offset="0.522" stop-color="#371f08"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="-0.909" y1="0.5" x2="1.214" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="0.489" stop-color="#dbdbdb"/>
      <stop offset="1" stop-color="#7a7a7a"/>
    </linearGradient>
  </defs>
  <g id="icon_delete_tan_01" transform="translate(-3157 -3702)">
    <g id="组_3742" data-name="组 3742" transform="translate(2441.228 819.683)">
      <path id="路径_16119" data-name="路径 16119" class="cls-1" d="M789.76,2982.307c0,2.236-15.109,4.05-33.751,4.05s-33.751-1.814-33.751-4.05,15.11-4.05,33.751-4.05S789.76,2980.069,789.76,2982.307Z" transform="translate(-0.141 -27.121)"/>
      <g id="组_3741" data-name="组 3741" transform="translate(721.772 2885.317)">
        <g id="组_3727" data-name="组 3727" transform="translate(22.345)">
          <path id="路径_16120" data-name="路径 16120" class="cls-2" d="M775.885,2891.341l-1.378-4.381a2.5,2.5,0,0,0-2.237-1.643H756.988a2.5,2.5,0,0,0-2.237,1.643l-1.379,4.381c-.284.9.751,1.642,1.7,1.642h2.282l.858-2.729a2.178,2.178,0,0,1,1.95-1.431H769.1a2.18,2.18,0,0,1,1.951,1.431l.858,2.729h2.281C775.133,2892.984,776.169,2892.244,775.885,2891.341Z" transform="translate(-753.325 -2885.317)"/>
          <path id="路径_16121" data-name="路径 16121" class="cls-3" d="M775.615,2893.646h-2.569l-.967-3.073a2.452,2.452,0,0,0-2.2-1.612H759.82a2.452,2.452,0,0,0-2.2,1.612l-.967,3.073h-2.569a2.077,2.077,0,0,0,1.207.4h2.282l.858-2.729a2.178,2.178,0,0,1,1.95-1.431h8.934a2.18,2.18,0,0,1,1.951,1.431l.858,2.729h2.281a2.079,2.079,0,0,0,1.207-.4Z" transform="translate(-753.547 -2886.381)"/>
        </g>
        <g id="组_3728" data-name="组 3728" transform="translate(4.381 14.095)">
          <path id="路径_16122" data-name="路径 16122" class="cls-4" d="M782.616,2958.723a2.683,2.683,0,0,1-2.583,2.271H734.419a2.684,2.684,0,0,1-2.583-2.271l-3.858-51.234a1.965,1.965,0,0,1,1.995-2.27H784.48a1.965,1.965,0,0,1,1.994,2.27Z" transform="translate(-727.958 -2905.22)"/>
        </g>
        <g id="组_3729" data-name="组 3729" class="cls-5" transform="translate(43.484 14.095)">
          <path id="路径_16123" data-name="路径 16123" class="cls-6" d="M800.594,2905.22H789.615c-3.382,18.808-2.912,37.015-6.439,55.774h12.972a2.683,2.683,0,0,0,2.583-2.271l3.858-51.234A1.965,1.965,0,0,0,800.594,2905.22Z" transform="translate(-783.175 -2905.22)"/>
        </g>
        <path id="路径_16124" data-name="路径 16124" class="cls-7" d="M786.475,2907.49a1.965,1.965,0,0,0-1.994-2.27H729.972a1.965,1.965,0,0,0-1.995,2.27l.746,5.746h57.006Z" transform="translate(-723.578 -2891.125)"/>
        <path id="路径_16125" data-name="路径 16125" class="cls-8" d="M733.094,2975.365l.24,3.828a2.684,2.684,0,0,0,2.583,2.271h45.615a2.684,2.684,0,0,0,2.583-2.271l.288-3.828Z" transform="translate(-725.076 -2911.594)"/>
        <g id="组_3730" data-name="组 3730" transform="translate(0 5.867)">
          <path id="路径_16126" data-name="路径 16126" class="cls-9" d="M788.962,2902.24a1.71,1.71,0,0,1-1.727,2.356H723.608a1.71,1.71,0,0,1-1.727-2.356l1.976-6.284a3.586,3.586,0,0,1,3.208-2.354h56.712a3.588,3.588,0,0,1,3.209,2.354Z" transform="translate(-721.772 -2893.601)"/>
          <path id="路径_16127" data-name="路径 16127" class="cls-10" d="M812.093,2902.24l-1.976-6.284a3.588,3.588,0,0,0-3.209-2.354h-5.87a83.981,83.981,0,0,1,2.715,10.994h6.613A1.71,1.71,0,0,0,812.093,2902.24Z" transform="translate(-744.903 -2893.601)"/>
          <path id="路径_16128" data-name="路径 16128" class="cls-11" d="M788.524,2903.83H722.318l-.438,1.395a1.71,1.71,0,0,0,1.727,2.356h63.627a1.71,1.71,0,0,0,1.727-2.356Z" transform="translate(-721.772 -2896.586)"/>
          <rect id="矩形_2514" data-name="矩形 2514" class="cls-12" width="57.308" height="1.142" transform="translate(5.176 0.689)"/>
        </g>
        <g id="组_3740" data-name="组 3740" transform="translate(14.956 22.033)">
          <g id="组_3733" data-name="组 3733" transform="translate(15.125)">
            <g id="组_3731" data-name="组 3731">
              <path id="路径_16129" data-name="路径 16129" class="cls-13" d="M764.249,2953.589v-33.574a3.585,3.585,0,0,1,3.586-3.586h0a3.585,3.585,0,0,1,3.586,3.586h0v33.574a3.586,3.586,0,0,1-3.586,3.586h0a3.586,3.586,0,0,1-3.586-3.586Zm1.372-33.574v33.574a2.216,2.216,0,0,0,2.214,2.212h0a2.214,2.214,0,0,0,2.212-2.212h0v-33.574a2.215,2.215,0,0,0-2.212-2.213h0a2.216,2.216,0,0,0-2.214,2.213Z" transform="translate(-764.249 -2916.429)"/>
            </g>
            <g id="组_3732" data-name="组 3732" class="cls-5" transform="translate(0 35.92)">
              <path id="路径_16130" data-name="路径 16130" class="cls-14" d="M770.047,2967.992v.4a2.213,2.213,0,0,1-4.426,0V2967.3c-.457-.06-.915-.107-1.372-.148v1.24a3.586,3.586,0,1,0,7.171,0v-.2C770.962,2968.129,770.505,2968.064,770.047,2967.992Z" transform="translate(-764.249 -2967.15)"/>
            </g>
            <path id="路径_16131" data-name="路径 16131" class="cls-15" d="M771.017,2953.872a2.9,2.9,0,0,1-2.9,2.9h0a2.9,2.9,0,0,1-2.9-2.9V2920.3a2.9,2.9,0,0,1,2.9-2.9h0a2.9,2.9,0,0,1,2.9,2.9Z" transform="translate(-764.532 -2916.712)"/>
          </g>
          <g id="组_3736" data-name="组 3736" transform="translate(30.251)">
            <g id="组_3734" data-name="组 3734">
              <path id="路径_16132" data-name="路径 16132" class="cls-16" d="M785.607,2953.589v-33.574a3.585,3.585,0,0,1,3.586-3.586h0a3.584,3.584,0,0,1,3.585,3.586h0v33.574a3.585,3.585,0,0,1-3.585,3.586h0a3.586,3.586,0,0,1-3.586-3.586Zm1.372-33.574v33.574a2.215,2.215,0,0,0,2.214,2.212h0a2.215,2.215,0,0,0,2.212-2.212h0v-33.574a2.216,2.216,0,0,0-2.212-2.213h0a2.216,2.216,0,0,0-2.214,2.213Z" transform="translate(-785.607 -2916.429)"/>
            </g>
            <g id="组_3735" data-name="组 3735" class="cls-5" transform="translate(0 35.92)">
              <path id="路径_16133" data-name="路径 16133" class="cls-14" d="M791.406,2967.992v.4a2.213,2.213,0,0,1-4.426,0V2967.3c-.457-.06-.915-.107-1.372-.148v1.24a3.586,3.586,0,1,0,7.171,0v-.2C792.321,2968.129,791.863,2968.064,791.406,2967.992Z" transform="translate(-785.607 -2967.15)"/>
            </g>
            <path id="路径_16134" data-name="路径 16134" class="cls-15" d="M792.375,2953.872a2.9,2.9,0,0,1-2.9,2.9h0a2.9,2.9,0,0,1-2.9-2.9V2920.3a2.9,2.9,0,0,1,2.9-2.9h0a2.9,2.9,0,0,1,2.9,2.9Z" transform="translate(-785.89 -2916.712)"/>
          </g>
          <g id="组_3739" data-name="组 3739">
            <g id="组_3737" data-name="组 3737">
              <path id="路径_16135" data-name="路径 16135" class="cls-13" d="M742.891,2953.589v-33.574a3.584,3.584,0,0,1,3.586-3.586h0a3.585,3.585,0,0,1,3.586,3.586h0v33.574a3.586,3.586,0,0,1-3.586,3.586h0a3.586,3.586,0,0,1-3.586-3.586Zm1.372-33.574v33.574a2.215,2.215,0,0,0,2.214,2.212h0a2.214,2.214,0,0,0,2.212-2.212h0v-33.574a2.216,2.216,0,0,0-2.212-2.213h0a2.216,2.216,0,0,0-2.214,2.213Z" transform="translate(-742.891 -2916.429)"/>
            </g>
            <g id="组_3738" data-name="组 3738" class="cls-5" transform="translate(0 35.92)">
              <path id="路径_16136" data-name="路径 16136" class="cls-14" d="M748.689,2967.992v.4a2.213,2.213,0,0,1-4.426,0V2967.3c-.457-.06-.915-.107-1.372-.148v1.24a3.586,3.586,0,1,0,7.171,0v-.2C749.6,2968.129,749.147,2968.064,748.689,2967.992Z" transform="translate(-742.891 -2967.15)"/>
            </g>
            <path id="路径_16137" data-name="路径 16137" class="cls-15" d="M749.658,2953.872a2.9,2.9,0,0,1-2.9,2.9h0a2.9,2.9,0,0,1-2.9-2.9V2920.3a2.9,2.9,0,0,1,2.9-2.9h0a2.9,2.9,0,0,1,2.9,2.9Z" transform="translate(-743.173 -2916.712)"/>
          </g>
        </g>
      </g>
    </g>
    <rect id="矩形_2515" data-name="矩形 2515" class="cls-17" width="80" height="80" transform="translate(3157 3702)"/>
  </g>
</svg>
