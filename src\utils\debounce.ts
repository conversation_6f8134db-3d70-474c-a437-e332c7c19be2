export function debounce <F extends (...args: any[]) => any>(fn: F, delay: number): (...args: Parameters<F>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | undefined
  return function (this:ThisParameterType<F>, ...args: Parameters<F>) {
    if(timeoutId !== undefined) {
      clearTimeout(timeoutId)
      timeoutId = undefined
    }
    timeoutId = setTimeout(() => {
      fn.apply(this, args)
      clearTimeout(timeoutId)
    }, delay)
  }
}