import axios from 'axios'
import { ElMessage } from 'element-plus'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import router from '@/router'

type Result<T> = {
  state: string,
  msg: string,
  data: T
}

class Request {
  private instance: AxiosInstance
  private baseConfig: AxiosRequestConfig = {baseURL: '/ops_management/api', timeout: 60000}

  constructor(config: AxiosRequestConfig) {
    this.instance = axios.create(Object.assign(this.baseConfig, config))
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        /**
         * 请求拦截器：为所有API请求添加认证头
         * 注意：URL参数中的认证信息已经在App.vue中处理并存储到sessionStorage中
         * 这里只需要从sessionStorage获取即可，不再从URL参数获取
         */
        
        // 从sessionStorage获取认证信息
        const token = sessionStorage.getItem("access_token") as string;
        const token_type = sessionStorage.getItem('token_type') as string;
        // 如果有认证信息，则添加到请求头
        if(token && token_type) {
          config.headers!.Authorization = token_type + ' ' + token;
        }
        
        return config;
      },
      (err: any) => {
        // 请求错误，这里可以用全局提示框进行提示
        console.error('请求配置错误:', err)
        return Promise.reject(err);
      }
    );
    
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (err:any) => {
        const { status, data } = err.response
        
        if(status === 500) {
          ElMessage.error('连接不到服务器')
        } else if(status === 401) {
          // 401错误处理：清除本地认证信息
          sessionStorage.removeItem('access_token')
          sessionStorage.removeItem('token_type')
          sessionStorage.removeItem('refresh_token')
          sessionStorage.removeItem('expires_in')
          sessionStorage.removeItem('token_expires_at')
          
          // 检查是否有跳转地址
          if (data && data.data) {
            // 如果401且有跳转地址，则跳转到其他系统的登录页面
            window.location.href = data.data;
          } else {
            // 没有跳转地址的401错误，可能是直接访问的情况
            ElMessage.error('请重新登录')
          }
        } else if(status === 400) {
          const { msg } = data
          // msg是对象，需要遍历msg，如果msg是字符串，则直接显示
          if(typeof msg === 'string') {
            ElMessage.error(msg)
          } else if(typeof msg === 'object') {
            for(const key in msg) {
              ElMessage.error(msg[key])
            }
          }else{
            ElMessage.error(msg)
          }
        } else {
          console.log('其他HTTP错误:', status, data)
          const { msg } = data
          ElMessage.error(msg)
        }
        return err.response
      }
    )
  }

  public request(config: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.instance.request(config)
  }
  public get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<Result<T>>> {
    return this.instance.get(url, config)
  }
  public post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<Result<T>>> {
    console.log(data)
    return this.instance.post(url, data, config)
  }
  public put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<Result<T>>> {
    return this.instance.put(url, data, config)
  }
  public patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<Result<T>>> {
    return this.instance.patch(url, data, config)
  }
  public del<T = any>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<Result<T>>> {
    return this.instance.delete(url, config)
  }
}

export const defaultInstance = new Request({})