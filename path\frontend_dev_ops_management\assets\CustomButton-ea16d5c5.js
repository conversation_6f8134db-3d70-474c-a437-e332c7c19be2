import{a as f,c as g,o as h,f as C,g as n,C as l,aI as _,B,aK as x,a9 as y}from"./index-8a4876d8.js";import"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{_ as k}from"./_plugin-vue_export-helper-c27b6911.js";const v=["onClick"],N={class:"btn__prefix__inner"},S={class:"btn__default"},T=f({__name:"CustomButton",props:{height:{},btnType:{},disabled:{type:Boolean},logName:{},menuName:{},disableLog:{type:Boolean}},emits:["click"],setup(u,{emit:d}){const e=u,s={red:"custom__button__bg__red",default:"custom__button__bg",blue:"custom__button__bg__blue"},m=g(()=>e.disabled?"custom__button__disabled":e.btnType===void 0?s.default:s[e.btnType]);function p(t){if(!e.disabled){if(!e.disableLog){const o=e.logName||b(t.target);y(o,e.menuName)}d("click")}}function b(t){var i,r,c;const o=t.closest(".btn-container"),a=(r=(i=o==null?void 0:o.querySelector(".btn__default"))==null?void 0:i.textContent)==null?void 0:r.trim();return a?a.replace(/\s+/g," ").trim():((c=t.textContent)==null?void 0:c.trim())||"未知按钮"}return(t,o)=>(h(),C("div",{class:B(["btn-container",m.value]),style:_({height:t.height+"px"}),onClick:x(p,["stop"]),tabindex:"0"},[n("span",{class:"btn__prefix",style:_({height:t.height+"px"})},[n("div",N,[l(t.$slots,"prefix-icon",{},void 0,!0)])],4),n("span",S,[l(t.$slots,"default",{},void 0,!0)])],14,v))}}),$=k(T,[["__scopeId","data-v-f26f0523"]]);export{$ as C};
