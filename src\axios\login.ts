import { defaultInstance } from ".";

// const { get, put, patch, post, del } = defaultInstance

// export const getCaptcha = () => defaultInstance.get('/captcha/')

/* export const login = (data:any) => {console.log(data); return defaultInstance.post('/login/', data) }

export const H4ALogin = () => defaultInstance.get('/h4a/login/')

export const logout = (data: any) => defaultInstance.post('/logout/', data)
 */
// 获取用户信息
export const getUserInfo = (data:any) => defaultInstance.get('/user/user_info/', { params: data })

// 退出登录
export const getLogout = () => defaultInstance.post('/user/logout/')

// 修改密码
export const setPassword = (data:any) => defaultInstance.post('/user/change_password/', data)