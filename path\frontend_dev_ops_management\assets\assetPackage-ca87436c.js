/* empty css             */import{C as E,c as T,h as N}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{a as V,r as o,z,H as S,E as B,o as b,f as K,g as i,h as a,I as L,J as O,q as M,w as g,n as y,K as R,L as U,M as A,i as q,t as G,N as H}from"./index-8a4876d8.js";import"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{D as J}from"./DataPreviewDialog-ee378a4a.js";import{P as C}from"./types-d67a131c.js";import{_ as $}from"./_plugin-vue_export-helper-c27b6911.js";import"./construct-a2f67563.js";const j={class:"data-import"},F={class:"search-header"},Q={class:"table-container"},W={class:"operation-buttons"},X=["onClick"],Y={class:"pagination-container"},r=10,Z=V({__name:"assetPackage",setup(ee){const c=o(""),d=o(!1),m=o([]),p=o(!1),f=o(C.OPERATION),u=o(null),h=o(0),n=o(1);z(()=>{v()});async function v(){d.value=!0;const s={page:n.value,page_size:r,search:c.value},{data:e}=await S(s),{state:t,msg:_}=e;t==="success"?(m.value=e.data.results,h.value=e.data.count):B.error(_),d.value=!1}function w(){n.value=1,v()}function k(s){n.value=s,v()}function P(s){f.value=C.ORIGINAL,u.value=s.id||null,p.value=!0}function D(){p.value=!1,u.value=null}return(s,e)=>{const t=H,_=R,x=U,I=A;return b(),K("div",j,[i("div",F,[a(E,{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=l=>c.value=l),placeholder:"搜索资产包名称",onKeyup:L(w,["enter"]),onClick:w},null,8,["modelValue","onKeyup"])]),i("div",Q,[O((b(),M(_,{data:m.value,border:"",style:{width:"100%"},"cell-style":y(T),"header-cell-style":y(N)},{default:g(()=>[a(t,{type:"index",label:"序号",width:"80",align:"center"},{default:g(({$index:l})=>[q(G((n.value-1)*r+l+1),1)]),_:1}),a(t,{prop:"package_name",label:"资产包名称","min-width":"200",align:"center","show-overflow-tooltip":""}),a(t,{prop:"source_file_name",label:"原文件","min-width":"180",align:"center","show-overflow-tooltip":""}),a(t,{prop:"file_size_display",label:"文件大小","min-width":"100",align:"center"}),a(t,{prop:"uploader_name",label:"上传人","min-width":"100",align:"center"}),a(t,{prop:"upload_time",label:"上传时间","min-width":"160",align:"center"}),a(t,{label:"操作",width:"150",fixed:"right",align:"center"},{default:g(({row:l})=>[i("div",W,[i("div",{onClick:ae=>P(l),class:"operation-btn edit-btn"},"预览",8,X)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[I,d.value]]),i("div",Y,[a(x,{"current-page":n.value,"onUpdate:currentPage":e[1]||(e[1]=l=>n.value=l),"page-size":r,"onUpdate:pageSize":e[2]||(e[2]=l=>r=l),total:h.value,layout:"prev, pager, next",onCurrentChange:k,background:""},null,8,["current-page","total"])])]),a(J,{visible:p.value,"preview-type":f.value,"package-id":u.value,onClose:D},null,8,["visible","preview-type","package-id"])])}}});const ue=$(Z,[["__scopeId","data-v-d70d91af"]]);export{ue as default};
