<script lang="ts" setup>
import { computed } from 'vue'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'

interface Props {
  visible: boolean
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
  width?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '删除确认',
  message: '确认删除？此操作不可撤销。',
  confirmText: '确认',
  cancelText: '取消',
  width: '400px',
  loading: false
})

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()

// 计算属性：控制弹框显示状态
const dialogVisible = computed({
  get() {
    return props.visible
  },
  set(value: boolean) {
    emit('update:visible', value)
  }
})

// 处理确认删除
function handleConfirm() {
  emit('confirm')
}

// 处理取消操作
function handleCancel() {
  emit('cancel')
  emit('update:visible', false)
}

// 处理弹框关闭
function handleClose() {
  emit('update:visible', false)
}
</script>

<template>
  <CustomDialog 
    :title="title"
    :visible="dialogVisible" 
    :width="width"
    :markclose="true"
    @update:visible="handleClose">
    
    <!-- 删除确认内容 -->
    <div class="delete-confirm-content">
      <div class="icon-section">
        <i class="jt-60-delete delete-icon"></i>
      </div>
      
      <div class="message-section">
        <p class="delete-message">{{ message }}</p>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="btns-group">
      <CustomButton 
        @click="handleConfirm" 
        :loading="loading"
        :height="34" 
        btn-type="red">
        <i class="jt-20-ensure"></i>{{ confirmText }}
      </CustomButton>
      <CustomButton 
        @click="handleCancel" 
        :height="34">
        <i class="jt-20-delete"></i>{{ cancelText }}
      </CustomButton>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.delete-confirm-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  
  .icon-section {
    margin-bottom: 16px;
    
    .delete-icon {
      color: #f56c6c;
      font-size: 60px;
    }
  }
  
  .message-section {
    text-align: center;
    
    .delete-message {
      margin: 0;
      font-size: 16px;
      color: #333;
      line-height: 1.5;
      word-break: break-word;
    }
  }
}

.btns-group {
  display: flex;
  justify-content: center;
  gap: 24px;
}

// 响应式适配
@media (max-width: 768px) {
  .delete-confirm-content {
    padding: 16px 0;
    
    .icon-section {
      margin-bottom: 12px;
      
      .delete-icon {
        font-size: 48px;
      }
    }
    
    .message-section {
      .delete-message {
        font-size: 14px;
      }
    }
  }
  
  .btns-group {
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
  }
}
</style>
