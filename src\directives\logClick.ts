import type { App, DirectiveBinding } from 'vue'
import { operationLogger } from '@/utils/operationLogger'

export interface LogClickBinding {
  buttonName?: string;
  menuName?: string;
}

export default {
  install(app: App) {
    app.directive('log-click', {
      mounted(el: HTMLElement, binding: DirectiveBinding<LogClickBinding | string>) {
        const clickHandler = () => {
          let buttonName = ''
          let menuName = ''
          
          if (typeof binding.value === 'string') {
            // 如果传入的是字符串，直接作为按钮名称
            buttonName = binding.value
          } else if (binding.value && typeof binding.value === 'object') {
            // 如果传入的是对象，解构获取参数
            buttonName = binding.value.buttonName || ''
            menuName = binding.value.menuName || ''
          }
          
          // 如果没有指定按钮名称，自动从DOM提取
          if (!buttonName) {
            buttonName = operationLogger.extractButtonName(el)
          }
          
          // 记录操作日志
          operationLogger.logOperation(buttonName, menuName)
        }
        
        el.addEventListener('click', clickHandler)
        
        // 保存处理器引用用于清理
        el._logClickHandler = clickHandler
      },
      
      beforeUnmount(el: HTMLElement) {
        if (el._logClickHandler) {
          el.removeEventListener('click', el._logClickHandler)
          delete el._logClickHandler
        }
      }
    })
  }
}

// 扩展HTMLElement类型以支持_logClickHandler属性
declare global {
  interface HTMLElement {
    _logClickHandler?: () => void
  }
} 