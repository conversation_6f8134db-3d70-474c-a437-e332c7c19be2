<script lang="ts" setup>
import { ref, watch } from 'vue'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  showDialog: boolean
  planData?: {
    id: string
    plan_name?: string
    mediation_case_number?: string
    [key: string]: any
  }
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'ensure', params: { id: number, approval_status: string, approval_comment?: string }): void
}>()

// 表单引用
const approvalFormRef = ref()
const loading = ref(false)

// 审批表单数据
const approvalForm = ref({
  approval_status: '', // 审批状态：approved（通过）、rejected（不通过）
  approval_comment: '' // 审批意见
})

// 表单验证规则
const rules = {
  approval_status: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ]
}

// 监听弹框显示状态
watch(() => props.showDialog, (newVal: boolean) => {
  if (newVal) {
    resetForm()
  }
})

// 重置表单数据
function resetForm() {
  approvalForm.value = {
    approval_status: '',
    approval_comment: ''
  }
  // 清除表单验证状态
  if (approvalFormRef.value) {
    approvalFormRef.value.clearValidate()
  }
}

// 关闭弹框
function close() {
  emit('close')
}

// 确认审批
async function ensureApproval() {
  if (!approvalFormRef.value) return
  
  loading.value = true
  try {
    // 验证表单
    const isValid = await new Promise((resolve) => {
      approvalFormRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValid) return
    
    if (!props.planData?.id) {
      ElMessage.error('未找到方案信息')
      return
    }
    
    // 构造提交数据
    const params = {
      id: Number(props.planData.id),
      approval_status: approvalForm.value.approval_status,
      approval_comment: approvalForm.value.approval_comment || undefined
    }
    
    emit('ensure', params)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog 
    :visible="showDialog" 
    @update:visible="close" 
    width="600px" 
    title="审批方案">
    <div class="approval-content">
      <div class="plan-info">
        <div class="info-row">
          <span class="info-label">调解案件号</span>
          <span class="info-value">{{ planData?.mediation_case_number }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">方案名称</span>
          <span class="info-value">{{ planData?.plan_name }}</span>
        </div>
      </div>
      <el-form 
        ref="approvalFormRef" 
        :model="approvalForm" 
        :rules="rules" 
        label-width="100px"
        label-position="left">
        <el-form-item label="审批结果" prop="approval_status">
          <el-radio-group v-model="approvalForm.approval_status">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见" prop="approval_comment">
          <el-input
            v-model="approvalForm.approval_comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见"
            maxlength="500"
            show-word-limit
            resize="none" />
        </el-form-item>
      </el-form>
      <div class="btns-group">
        <CustomButton 
          @click="ensureApproval" 
          :loading="loading" 
          :height="34" 
          btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.approval-content {
  padding: 0 8px;
  
  .plan-info {
    padding: 24px 0;
    
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      color: #333;
      font-weight: 500;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        min-width: 100px;
      }
    }
  }
  
  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 24px;
      
      .el-form-item__label {
        font-weight: 500;
        color: #333;
      }
      
      .el-radio-group {
        .el-radio {
          margin-right: 24px;
          
          .el-radio__label {
            font-size: 16px;
            font-weight: 500;
          }
          
          &.is-checked .el-radio__label {
            color: #409eff;
          }
        }
      }
      
      .el-textarea {
        .el-textarea__inner {
          font-family: inherit;
          line-height: 1.5;
          font-size: 16px;
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    padding: 24px 0 8px 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .approval-content {
    .btns-group {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style> 