<script lang="ts" setup>
import { onMounted, ref, type Ref } from 'vue'
import CustomButton from '@/components/common/CustomButton.vue';
import CustomInput from '@/components/common/CustomInput.vue';
import AddMediationInformation from '../../dialogs/AddMediationInformation.vue';
import EditMediationInformation from '../../dialogs/EditMediationInformation(废弃）.vue';
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue';
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import type { Mediation, MediationParams, AddMediationParams, EditMediationParams, MediationField } from '../../auth/type';
import { FieldType, CaseStatus } from '../../auth/type';
import { getPlan, editPlan, addPlan, deletePlan } from '@/axios/system'
import { ElMessage } from 'element-plus';

// 选中的方案行数据
const selectRow: Ref<Mediation> = ref({
  id: '',
  title: '',
  fields: [],
  jsonData: '',
  caseStatus: CaseStatus.PENDING
})

// 分页和搜索
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const planList = ref<Mediation[]>([])

// 对话框控制
const showAdd = ref(false)
const showEdit = ref(false)
const showDelete = ref(false)

// 生成模拟数据
function generateMockData(): Mediation[] {
  const mockPlans: Mediation[] = [
    {
      id: 'GZTJ202506250001',
      title: '债务人基本信息收集方案',
      updateTime: '2024-01-15T10:30:00Z',
      caseStatus: CaseStatus.PENDING,
      fileList: [
        {
          id: 'file1',
          name: '还款合同.pdf',
          size: 1024000,
          type: 'application/pdf'
        },
      ],
      remark: '',
      fields: [
        {
          id: 'GZTJ1',
          title: '债务人姓名',
          type: FieldType.TEXTAREA,
          value: '张三',
          required: true,
          placeholder: '请输入债务人姓名'
        },
        {
          id: 'GZTJ2',
          title: '身份证号',
          type: FieldType.TEXTAREA,
          value: '110101199001011234',
          required: true,
          placeholder: '请输入身份证号'
        },
        {
          id: 'GZTJ3',
          title: '债务金额',
          type: FieldType.AMOUNT,
          value: 150000,
          required: true,
          placeholder: '请输入债务金额'
        },
        {
          id: 'GZTJ4',
          title: '债务产生日期',
          type: FieldType.DATE,
          value: '2023-06-15',
          required: true,
          placeholder: '选择债务产生日期'
        },
      ],
      jsonData: JSON.stringify({
        planTitle: '债务人基本信息收集方案',
        sections: [
          { title: '债务人姓名', content: '张三', type: 'text' },
          { title: '身份证号', content: '110101199001011234', type: 'text' },
          { title: '债务金额', content: '¥150,000.00', type: 'amount' },
          { title: '债务产生日期', content: '2023-06-15', type: 'date' },
          { title: '相关证明文件', content: '2个文件', type: 'file', fileList: ['借款合同.pdf', '银行流水.pdf'] }
        ]
      })
    },
    {
      id: 'GZTJ202506200002',
      title: '资产评估调查方案',
      updateTime: '2024-01-20T09:15:00Z',
      caseStatus: CaseStatus.CLOSED,
      fileList: [],
      remark: '',
      fields: [
        {
          id: 'GZTJ6',
          title: '资产类型',
          type: FieldType.TEXTAREA,
          value: '房产',
          required: true,
          placeholder: '请输入资产类型'
        },
        {
          id: 'GZTJ7',
          title: '资产地址',
          type: FieldType.TEXTAREA,
          value: '北京市朝阳区某某小区某某号楼某某单元某某室',
          required: true,
          placeholder: '请详细描述资产地址'
        },
        {
          id: 'GZTJ8',
          title: '估值金额',
          type: FieldType.AMOUNT,
          value: 3500000,
          required: true,
          placeholder: '请输入估值金额'
        },
        {
          id: 'GZTJ9',
          title: '评估日期',
          type: FieldType.DATE,
          value: '2024-01-18',
          required: true,
          placeholder: '选择评估日期'
        }
      ],
      jsonData: JSON.stringify({
        planTitle: '资产评估调查方案',
        sections: [
          { title: '资产类型', content: '房产', type: 'text' },
          { title: '资产地址', content: '北京市朝阳区某某小区某某号楼某某单元某某室', type: 'textarea' },
          { title: '估值金额', content: '¥3,500,000.00', type: 'amount' },
          { title: '评估日期', content: '2024-01-18', type: 'date' }
        ]
      })
    },
    {
      id: 'GZTJ202506150003',
      title: '调解谈判记录方案',
      updateTime: '2024-01-22T11:30:00Z',
      caseStatus: CaseStatus.MEDIATING,
      fileList: [],
      remark: '',
      fields: [
        {
          id: 'GZTJ10',
          title: '调解员姓名',
          type: FieldType.TEXTAREA,
          value: '李调解',
          required: true,
          placeholder: '请输入调解员姓名'
        },
        {
          id: 'GZTJ11',
          title: '调解时间',
          type: FieldType.DATE,
          value: '2024-01-20',
          required: true,
          placeholder: '选择调解时间'
        },
        {
          id: 'GZTJ12',
          title: '谈判过程记录',
          type: FieldType.TEXTAREA,
          value: '双方就债务金额和还款方式进行了深入讨论，债务人表示愿意分期偿还，债权人要求提供担保。经过协商，初步达成了分12期还款的意向。',
          required: true,
          placeholder: '请详细记录谈判过程'
        },
        {
          id: 'GZTJ13',
          title: '达成金额',
          type: FieldType.AMOUNT,
          value: 120000,
          required: false,
          placeholder: '请输入达成的金额'
        },
      ],
      jsonData: JSON.stringify({
        planTitle: '调解谈判记录方案',
        sections: [
          { title: '调解员姓名', content: '李调解', type: 'text' },
          { title: '调解时间', content: '2024-01-20', type: 'date' },
          { title: '谈判过程记录', content: '双方就债务金额和还款方式进行了深入讨论...', type: 'textarea' },
          { title: '达成金额', content: '¥120,000.00', type: 'amount' },
          { title: '录音文件', content: '1个文件', type: 'file', fileList: ['调解录音_20240120.mp3'] }
        ]
      })
    },
    {
      id: 'GZTJ202506100004',
      title: '执行进度跟踪方案',
      updateTime: '2024-01-25T15:20:00Z',
      caseStatus: CaseStatus.PROCESSING,
      fileList: [],
      remark: '',
      fields: [
        {
          id: 'GZTJ15',
          title: '执行案号',
          type: FieldType.TEXTAREA,
          value: '(2024)京执123号',
          required: true,
          placeholder: '请输入执行案号'
        },
        {
          id: 'GZTJ16',
          title: '执行开始日期',
          type: FieldType.DATE,
          value: '2024-01-22',
          required: true,
          placeholder: '选择执行开始日期'
        },
        {
          id: 'GZTJ17',
          title: '预计执行金额',
          type: FieldType.AMOUNT,
          value: 180000,
          required: true,
          placeholder: '请输入预计执行金额'
        },
        {
          id: 'GZTJ18',
          title: '执行措施说明',
          type: FieldType.TEXTAREA,
          value: '已查封债务人名下房产一处，银行账户冻结，正在进行财产调查和评估工作。',
          required: false,
          placeholder: '请描述具体的执行措施'
        }
      ],
      jsonData: JSON.stringify({
        planTitle: '执行进度跟踪方案',
        sections: [
          { title: '执行案号', content: '(2024)京执123号', type: 'text' },
          { title: '执行开始日期', content: '2024-01-22', type: 'date' },
          { title: '预计执行金额', content: '¥180,000.00', type: 'amount' },
          { title: '执行措施说明', content: '已查封债务人名下房产一处，银行账户冻结...', type: 'textarea' }
        ]
      })
    },
    {
      id: 'GZTJ202506050005',
      title: '债权人信息登记方案',
      updateTime: '2024-01-25T13:30:00Z',
      caseStatus: CaseStatus.PROCESSING,
      fileList: [],
      remark: '',
      fields: [
        {
          id: 'GZTJ19',
          title: '债权人名称',
          type: FieldType.TEXTAREA,
          value: '某某银行股份有限公司',
          required: true,
          placeholder: '请输入债权人名称'
        },
        {
          id: 'GZTJ20',
          title: '联系电话',
          type: FieldType.TEXTAREA,
          value: '010-12345678',
          required: true,
          placeholder: '请输入联系电话'
        },
        {
          id: 'GZTJ21',
          title: '债权金额',
          type: FieldType.AMOUNT,
          value: 500000,
          required: true,
          placeholder: '请输入债权金额'
        },
        {
          id: 'GZTJ22',
          title: '债权产生日期',
          type: FieldType.DATE,
          value: '2023-03-10',
          required: true,
          placeholder: '选择债权产生日期'
        },
      ],
      jsonData: JSON.stringify({
        planTitle: '债权人信息登记方案',
        sections: [
          { title: '债权人名称', content: '某某银行股份有限公司', type: 'text' },
          { title: '联系电话', content: '010-12345678', type: 'text' },
          { title: '债权金额', content: '¥500,000.00', type: 'amount' },
          { title: '债权产生日期', content: '2023-03-10', type: 'date' },
          { title: '授权委托书', content: '3个文件', type: 'file', fileList: ['授权委托书.pdf', '营业执照.pdf', '法人身份证.pdf'] }
        ]
      })
    }
  ]
  
  return mockPlans
}



// 重置到第一页并搜索
function setPage1() {
  page.value = 1
  searchPlanList()
}

// 搜索方案列表（使用模拟数据）
async function searchPlanList() {
  try {
    // 使用模拟数据替代API调用
    const mockData = generateMockData()

    // 模拟搜索过滤
    let filteredData = mockData
    if (search.value.trim()) {
      filteredData = mockData.filter(plan =>
        plan.title.toLowerCase().includes(search.value.toLowerCase()) ||
        plan.id!.toString().includes(search.value)
      )
    }

    // 模拟分页
    const startIndex = (page.value - 1) * page_size
    const endIndex = startIndex + page_size
    planList.value = filteredData.slice(startIndex, endIndex)
    total.value = filteredData.length

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))

  } catch (error) {
    ElMessage.error('获取方案列表失败')
  }
}

// 分页改变
function pageChanged(p: number) {
  page.value = p
  searchPlanList()
}



// 打开新增方案对话框
function openAddPlanDialog() {
  showAdd.value = true 
}

// 提交新增方案（模拟）
async function submitAddPlan(params: AddMediationParams) {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟新增成功
    ElMessage.success('新增成功')
    showAdd.value = false
    setPage1()
  } catch (error) {
    ElMessage.error('新增失败')
  }
}

// 打开编辑方案对话框
function openEditPlanDialog(row: Mediation, index: number) {
  selectRow.value = { ...row }
  showEdit.value = true
}

// 提交编辑方案（模拟）
async function submitEditPlan(params: EditMediationParams) {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟编辑成功
    ElMessage.success('编辑成功')
    showEdit.value = false
    searchPlanList()
  } catch (error) {
    ElMessage.error('编辑失败')
  }
}

// 打开删除确认对话框
function openEnsureDeleteDialog(row: Mediation, index: number) {
  selectRow.value = { ...row }
  showDelete.value = true
}

// 删除方案（模拟）
async function deletePlanRow() {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟删除成功
    ElMessage.success('删除成功')
    showDelete.value = false
    searchPlanList()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 关闭编辑对话框
function closeEditDialog() {
  showEdit.value = false
  selectRow.value = {
    id: '',
    title: '',
    fields: [],
    jsonData: '',
    caseStatus: CaseStatus.PENDING
  }
}

// 格式化显示动态字段概览
function formatFieldsPreview(fields: MediationField[]): string {
  if (!fields || fields.length === 0) return '无字段'
  return fields.map(field => field.title).slice(0, 3).join('、') + 
         (fields.length > 3 ? '...' : '')
}

// 格式化字段类型显示
/* function getFieldTypeIcon(type: FieldType): string {
  const iconMap = {
    [FieldType.TEXTAREA]: '📝',
    [FieldType.TEXTAREA]: '📄',
    [FieldType.DATE]: '📅',
    [FieldType.AMOUNT]: '💰',
    [FieldType.FILE]: '📎'
  }
  return iconMap[type] || '📝'
} */

// 预览JSON数据
/* function previewJsonData(row: Mediation) {
  try {
    const jsonData = JSON.parse(row.jsonData)
    ElMessage({
      message: `方案JSON预览：\n${JSON.stringify(jsonData, null, 2)}`,
      type: 'info',
      duration: 8000,
      showClose: true
    })
  } catch (error) {
    ElMessage.error('JSON数据格式错误')
  }
} */

// 组件挂载时获取方案列表
onMounted(() => {
  searchPlanList()
})
</script>

<template>
  <div class="plan-management">
    <div class="search-header">
      <CustomInput v-model="search" placeholder="搜索方案名称或案件号" @click="setPage1"></CustomInput>
      <CustomButton @click="openAddPlanDialog" :height="34"><i class="jt-20-add"></i>新增调解</CustomButton>
    </div>



    <div>
      <el-table
        :data="planList"
        border
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle"
        class="plan-table">
        
        <!-- 序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center">
          <template v-slot="{$index}">
            {{page_size * (page - 1) + $index + 1}}
          </template>
        </el-table-column>
        
        <!-- 方案ID -->
        <el-table-column prop="id" label="调解案件号" align="center" width="180"></el-table-column>

        <el-table-column prop="caseStatus" label="案件状态" align="center" min-width="100">
          <template v-slot="{row}">
            <div class="plan-title">
              <span class="title-text">{{ row.caseStatus }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="调解信息配置" align="center" min-width="220">
          <template v-slot="{row}">
            <div class="fields-preview">
              <div class="field-types">
                <span 
                  v-for="field in row.fields.slice(0, 4)" 
                  :key="field.id"
                  class="field-type-tag"
                  :title="field.title">
				   {{ field.title }}
                </span>
                  <!-- {{ getFieldTypeIcon(field.type) }} -->
                <span v-if="row.fields.length > 4" class="more-fields">
                  +{{ row.fields.length - 4 }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <!-- 字段数量统计 -->
        <!-- <el-table-column label="字段统计" align="center" width="120">
          <template v-slot="{row}">
            <div class="field-stats">
              <el-tag size="small" type="info">{{ row.fields?.length || 0 }}个字段</el-tag>
              <div class="required-count">
                <el-tag size="small" type="warning">
                  {{ row.fields?.filter(f => f.required).length || 0 }}必填
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column> -->
        
        <!-- 相关文件 -->
        <el-table-column prop="fileList" label="相关文件" align="center" min-width="100">
          <template v-slot="{row}">
            <div class="file-list" v-for="file in row.fileList" :key="file.id">
              <span class="file-name">{{ file.name }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 备注 -->
        <el-table-column prop="remark" label="备注" align="center" min-width="100">
          <template v-slot="{row}">
            <div class="remark">
              <span class="remark-text">{{ row.remark }}</span>
            </div>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column label="操作" align="center" width="250">
          <template v-slot="{row, $index}">
            <div class="operation-buttons">
              <!-- <div @click="previewJsonData(row)" class="operation-btn preview-btn">预览</div> -->
              <div @click="openEditPlanDialog(row, $index)" class="operation-btn edit-btn">编辑</div>
              <div @click="openEnsureDeleteDialog(row, $index)" class="operation-btn delete-btn">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <el-pagination
        class="pagi"
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="page"
        :page-size="page_size"
        @current-change="pageChanged"
      ></el-pagination>
    </div>
  </div>

  <!-- 新增方案对话框 -->
  <AddMediationInformation
    :show-dialog="showAdd"
    @close="showAdd = false"
    @ensure="submitAddPlan">
  </AddMediationInformation>

  <!-- 编辑方案对话框 -->
  <EditMediationInformation
    :show-dialog="showEdit"
    :plan-data="selectRow"
    @close="closeEditDialog"
    @ensure="submitEditPlan">
  </EditMediationInformation>
  
  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDelete"
    title="删除调解信息"
    :message="`确认删除选中的调解信息吗？此操作不可撤销。`"
    confirm-text="确认"
    cancel-text="取消"
    @update:visible="showDelete = $event"
    @confirm="deletePlanRow"
    @cancel="showDelete = false" />
</template>

<style lang="scss" scoped>
.plan-management {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    display: grid;
    grid-template-columns: 300px 125px;
    gap: 20px;
    margin-bottom: 20px;
  }


}

.plan-table {
  border-radius: 8px;
  overflow: hidden;
  
  .plan-title {
    .title-text {
      color: #333;
      line-height: 1.4;
    }
  }
  
  .fields-preview {
    .field-types {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      justify-content: center;
      
      .field-type-tag {
        display: inline-block;
        padding: 2px 6px;
        background-color: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 12px;
        font-size: 12px;
        color: #0369a1;
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .more-fields {
        padding: 2px 6px;
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
  
  .field-stats {
    .required-count {
      margin-top: 4px;
    }
  }
  
  .create-time {
    font-size: 13px;
    color: #666;
  }
}

.pagi {
  margin-top: 20px;
  text-align: center;
  
  :deep(.el-pagination) {
    justify-content: center;
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .plan-management {
    .search-header {
      grid-template-columns: 1fr auto;
    }
  }
  
  .plan-table {
    .fields-preview {
      .field-types {
        .field-type-tag {
          max-width: 60px;
        }
      }
    }
  }
}
</style>