<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import { reprocessExcel } from '@/axios/system'

// 组件属性定义
interface Props {
  visible: boolean              // 弹框显示状态
  recordId: number | null       // 记录ID
  fileName?: string             // 已上传的文件名称（用于回显）
}

// 组件事件定义
interface Emits {
  (e: 'close'): void           // 关闭弹框
  (e: 'success'): void         // 重新上传成功
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)

// 表单数据
const formData = reactive({
  file: null as File | null,    // 上传文件
  resetFieldMapping: false      // 是否重置字段映射配置，默认选中"否"
})

// 重置字段映射选项
const resetFieldMappingOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
]

// 计算属性：格式化显示的文件名列表
const displayFileNames = computed(() => {
  if (!props.fileName) return []
  // 支持单个文件名或逗号分隔的多个文件名
  return props.fileName.split(',').map(name => name.trim()).filter(name => name)
})


/**
 * Excel文件类型验证
 * @param file 文件对象
 */
function validateExcelFile(file: File): boolean {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls')

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return false
  }
  return true
}

/**
 * 文件选择变化处理
 * @param file 选择的文件
 */
function handleFileChange(file: File | null) {
  formData.file = file
}

/**
 * 文件移除处理
 */
function handleRemove() {
  formData.file = null
}

/**
 * 提交重新上传
 */
async function handleSubmit() {
  // 表单验证
  if (!formData.file) {
    ElMessage.error('请选择要上传的文件')
    return
  }

  if (!props.recordId) {
    ElMessage.error('记录ID不存在')
    return
  }

  loading.value = true
  // 构造FormData
  const uploadData = new FormData()
  uploadData.append('file', formData.file)
  uploadData.append('reset_mappings', formData.resetFieldMapping.toString())

  // 调用重新上传接口
  const {data} = await reprocessExcel(props.recordId,uploadData)
  const { state, msg } = data
  if(state === 'success') {
    ElMessage.success(msg)
    emit('success')
    handleCancel()
  }else{
    ElMessage.error(msg)
  }
  loading.value = false
}

/**
 * 取消操作
 */
function handleCancel() {
  // 重置表单
  formData.file = null
  formData.resetFieldMapping = false

  emit('close')
}
</script>

<template>
  <CustomDialog
    :visible="visible"
    title="重新上传"
    width="600px"
    @update:visible="handleCancel"
    class="reupload-dialog">
    
    <div class="form-container">
      <div class="form-item" v-if="displayFileNames.length > 0">
        <label class="form-label">已上传文件</label>
        <div class="form-input">
          <div class="uploaded-files-list">
            <div
              v-for="(fileName, index) in displayFileNames"
              :key="index"
              class="uploaded-file-item">
              <span class="file-name">{{ fileName }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="form-item">
        <label class="form-label"><span class="required">*</span>上传文件</label>
        <div class="form-input">
          <FileUpload
            v-model="formData.file"
            accept=".xlsx,.xls"
            :max-size="100"
            limit="1"
            placeholder="选择文件"
            :validate-type="validateExcelFile"
            @change="handleFileChange" />
        </div>
      </div>
      <div class="form-item">
        <label class="form-label">是否重置字段映射配置</label>
        <div class="form-input">
          <el-select 
            v-model="formData.resetFieldMapping" 
            placeholder="请选择"
            style="width: 100%;">
            <el-option
              v-for="option in resetFieldMappingOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="dialog-footer">
      <CustomButton 
        @click="handleSubmit" 
        :loading="loading"
        :height="34" 
        btn-type="blue">
        <i class="jt-20-ensure"></i>确认
      </CustomButton>
      <CustomButton 
        @click="handleCancel" 
        :height="34">
        <i class="jt-20-delete"></i>取消
      </CustomButton>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.reupload-dialog {
  .form-container {
    padding: 20px 0;
    
    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24px;
      
      .form-label {
        width: 140px;
        padding-top: 8px;
        color: #303133;
        font-weight: 500;
        font-size: 14px;
        text-align: right;
        margin-right: 16px;
        flex-shrink: 0;
        
        .required {
          color: #f56c6c;
          margin-left: 2px;
        }
      }
      
      .form-input {
        flex: 1;
      }
    }
  }

  // 已上传文件列表样式
  .uploaded-files-list {
    .uploaded-file-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      i {
        color: #67c23a;
        margin-right: 8px;
        font-size: 16px;
      }

      .file-name {
        color: #606266;
        font-size: 14px;
        word-break: break-all;
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    padding-top: 30px;
  }
}

// Element Plus 组件样式覆盖
:deep(.el-select) {
  .el-select__wrapper {
    border-radius: 4px;
  }
}
</style> 