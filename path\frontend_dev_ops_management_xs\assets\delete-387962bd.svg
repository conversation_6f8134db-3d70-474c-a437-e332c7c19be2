<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" viewBox="0 0 16 16">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-4);
      }

      .cls-5 {
        fill: url(#linear-gradient-5);
      }

      .cls-6 {
        fill: url(#linear-gradient-6);
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: #ff9b96;
      }

      .cls-9 {
        fill: none;
      }
    </style>
    <linearGradient id="linear-gradient" x1="-5.899" y1="5.12" x2="-5.192" y2="4.413" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#7d0d07"/>
      <stop offset="1" stop-color="#be2119"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-5.998" y1="5.191" x2="-5.291" y2="4.484" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#820c06"/>
      <stop offset="1" stop-color="#c9251d"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-6.102" y1="5.263" x2="-5.395" y2="4.556" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#870b05"/>
      <stop offset="1" stop-color="#d42921"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-6.208" y1="5.339" x2="-5.501" y2="4.632" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#8d0a04"/>
      <stop offset="1" stop-color="#df2e25"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="-6.319" y1="5.415" x2="-5.612" y2="4.708" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#920902"/>
      <stop offset="1" stop-color="#e93228"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="-6.433" y1="5.497" x2="-5.726" y2="4.79" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#970801"/>
      <stop offset="1" stop-color="#f4362c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="0.573" y1="1" x2="0.54" y2="0.1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#9c0700"/>
      <stop offset="1" stop-color="#ff3a30"/>
    </linearGradient>
  </defs>
  <g id="icon_lieb_del" transform="translate(-1774 -327)">
    <g id="组_1814" data-name="组 1814" transform="translate(1228.852 -221.178)">
      <g id="组_1813" data-name="组 1813" transform="translate(548.148 551.179)">
        <path id="路径_4595" data-name="路径 4595" class="cls-1" d="M556.29,551.338a.553.553,0,0,0-.778,0l-1.65,1.65a.553.553,0,0,1-.78,0l-1.649-1.65a.554.554,0,0,0-.779,0l-2.344,2.346a.551.551,0,0,0,0,.781l1.648,1.648a.551.551,0,0,1,0,.779l-1.648,1.65a.549.549,0,0,0,0,.779l2.344,2.345a.555.555,0,0,0,.78,0l1.65-1.65a.554.554,0,0,1,.779,0l1.649,1.65a.553.553,0,0,0,.778,0l2.346-2.345a.554.554,0,0,0,0-.779l-1.65-1.648a.555.555,0,0,1,0-.78l1.65-1.648a.555.555,0,0,0,0-.781Z" transform="translate(-548.148 -551.178)"/>
        <path id="路径_4596" data-name="路径 4596" class="cls-2" d="M556.531,561.825a.549.549,0,0,1-.778,0l-1.649-1.648a.552.552,0,0,0-.779,0l-1.65,1.648a.552.552,0,0,1-.78,0l-2.259-2.258a.552.552,0,0,1,0-.779l1.651-1.65a.556.556,0,0,0,0-.779l-1.651-1.648a.554.554,0,0,1,0-.781l2.259-2.257a.551.551,0,0,1,.779,0l1.649,1.648a.556.556,0,0,0,.78,0l1.65-1.648a.55.55,0,0,1,.778,0l2.26,2.257a.554.554,0,0,1,0,.781l-1.648,1.648a.553.553,0,0,0,0,.781l1.646,1.648a.552.552,0,0,1,0,.779Z" transform="translate(-548.389 -551.425)"/>
        <path id="路径_4597" data-name="路径 4597" class="cls-3" d="M556.776,561.978a.553.553,0,0,1-.778,0l-1.649-1.651a.552.552,0,0,0-.779,0l-1.65,1.651a.555.555,0,0,1-.78,0l-2.172-2.173a.553.553,0,0,1,0-.779l1.649-1.65a.552.552,0,0,0,0-.779l-1.649-1.648a.554.554,0,0,1,0-.781L551.141,552a.551.551,0,0,1,.779,0l1.649,1.65a.555.555,0,0,0,.78,0L556,552a.55.55,0,0,1,.778,0l2.173,2.172a.556.556,0,0,1,0,.781L557.3,556.6a.553.553,0,0,0,0,.78l1.649,1.648a.554.554,0,0,1,0,.779Z" transform="translate(-548.635 -551.663)"/>
        <path id="路径_4598" data-name="路径 4598" class="cls-4" d="M557.015,562.132a.55.55,0,0,1-.778,0l-1.649-1.649a.554.554,0,0,0-.779,0l-1.65,1.649a.552.552,0,0,1-.78,0l-2.085-2.084a.549.549,0,0,1,0-.779l1.648-1.65a.552.552,0,0,0,0-.779l-1.648-1.648a.551.551,0,0,1,0-.781l2.085-2.085a.552.552,0,0,1,.779,0l1.649,1.65a.553.553,0,0,0,.78,0l1.65-1.65a.551.551,0,0,1,.778,0l2.086,2.085a.555.555,0,0,1,0,.781l-1.65,1.648a.553.553,0,0,0,0,.78l1.65,1.648a.553.553,0,0,1,0,.779Z" transform="translate(-548.873 -551.905)"/>
        <path id="路径_4599" data-name="路径 4599" class="cls-5" d="M557.259,562.285a.55.55,0,0,1-.778,0l-1.65-1.649a.549.549,0,0,0-.778,0l-1.65,1.649a.553.553,0,0,1-.78,0l-2-2a.552.552,0,0,1,0-.779l1.65-1.648a.556.556,0,0,0,0-.78l-1.65-1.648a.554.554,0,0,1,0-.781l2-2a.556.556,0,0,1,.779,0l1.649,1.649a.551.551,0,0,0,.78,0l1.65-1.649a.55.55,0,0,1,.778,0l2,2a.559.559,0,0,1,0,.781l-1.651,1.648a.554.554,0,0,0,0,.78l1.65,1.648a.554.554,0,0,1,0,.779Z" transform="translate(-549.117 -552.144)"/>
        <path id="路径_4600" data-name="路径 4600" class="cls-6" d="M557.5,562.444a.55.55,0,0,1-.778,0l-1.65-1.65a.552.552,0,0,0-.778,0l-1.65,1.65a.553.553,0,0,1-.78,0l-1.912-1.912a.552.552,0,0,1,0-.779L551.6,558.1a.553.553,0,0,0,0-.78l-1.648-1.648a.554.554,0,0,1,0-.781l1.912-1.911a.548.548,0,0,1,.779,0l1.649,1.647a.55.55,0,0,0,.78,0l1.65-1.647a.547.547,0,0,1,.778,0l1.913,1.911a.548.548,0,0,1,0,.777l-1.65,1.652a.557.557,0,0,0,0,.78l1.65,1.648a.552.552,0,0,1,0,.779Z" transform="translate(-549.36 -552.39)"/>
        <path id="路径_4601" data-name="路径 4601" class="cls-7" d="M557.746,562.6a.55.55,0,0,1-.778,0l-1.65-1.649a.556.556,0,0,0-.78,0l-1.648,1.648a.55.55,0,0,1-.78,0l-1.825-1.824a.553.553,0,0,1,0-.779l1.65-1.648a.555.555,0,0,0,0-.78l-1.65-1.648a.555.555,0,0,1,0-.781l1.825-1.825a.551.551,0,0,1,.779,0l1.649,1.649a.555.555,0,0,0,.78,0l1.65-1.649a.55.55,0,0,1,.778,0l1.826,1.825a.549.549,0,0,1,0,.777l-1.649,1.651a.556.556,0,0,0,0,.78L559.572,560a.557.557,0,0,1,0,.779Z" transform="translate(-549.605 -552.633)"/>
      </g>
      <path id="路径_4602" data-name="路径 4602" class="cls-8" d="M552.279,552.705c.071.069-.048.3-.263.516l-1.825,1.825c-.215.215-.447.334-.514.265s.05-.3.264-.516l1.825-1.825C551.979,552.755,552.212,552.637,552.279,552.705Z" transform="translate(-1.111 -1.11)"/>
      <path id="路径_4603" data-name="路径 4603" class="cls-8" d="M551.824,573.28c.068.068-.05.3-.264.514l-1.285,1.287c-.215.213-.447.332-.516.263s.05-.3.265-.514l1.287-1.287C551.524,573.328,551.756,573.21,551.824,573.28Z" transform="translate(-1.171 -16.267)"/>
      <path id="路径_4604" data-name="路径 4604" class="cls-8" d="M572.221,552.782c.067.07-.05.3-.265.517l-1.322,1.322c-.215.214-.446.333-.515.264s.048-.3.263-.516l1.322-1.32C571.919,552.834,572.152,552.715,572.221,552.782Z" transform="translate(-16.172 -1.167)"/>
    </g>
    <rect id="矩形_1113" data-name="矩形 1113" class="cls-9" width="16" height="16" transform="translate(1774 327)"/>
  </g>
</svg>
