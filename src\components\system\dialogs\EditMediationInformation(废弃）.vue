<script lang="ts" setup>
import { ref, type Ref, watch, computed, onMounted } from "vue";
import CustomButton from "@/components/common/CustomButton.vue";
import CustomInput from "@/components/common/CustomInput.vue";
import AssetPackageSelector from "./AssetPackageSelector.vue";
import EditMediationInformationDialog from "./EditMediationInformationDialog.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle";
import type { FieldType, MediationField as BaseMediationField, EditMediationParams, Mediation, UploadFile, FieldTypeOption, CaseStatusOption } from '../auth/type';
import { FieldType as FieldTypeEnum, CaseStatus } from '../auth/type';
import { getDataImportList, getMediationCase, editMediationCase, getDebtor } from '@/axios/system';

/**
 * 扩展的调解字段接口，增加值锁定属性
 */
interface MediationField extends BaseMediationField {
  isValueLocked?: boolean
}

/**
 * 扩展的上传文件接口，增加服务器文件标识
 */
interface ExtendedUploadFile extends UploadFile {
  isServerFile?: boolean
}

/**
 * 资产包选择结果接口
 */
interface AssetSelectionResult {
  fileName: string
  selectedRow: Record<string, any>
  headers: Array<{ prop: string; label: string; width?: number }>
  // 新增字段
  asset_package: number | null
  creditor_name: string
  creditor: number | null
  asset_package_row_number: number
}

// Tab标签页类型
type TabType = 'asset' | 'mediation'

// 当前激活的Tab
const activeTab = ref<TabType>('asset')

// 分页和搜索
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const loading = ref(false)

// 列表数据
const assetList = ref<any[]>([])
const mediationList = ref<any[]>([])

// 编辑弹窗控制
const showEditDialog = ref(false)
const editingRow = ref<any>(null)
const editMode = ref<'asset' | 'mediation'>('asset')

// 债务人选项
const debtorOptions = ref([])

// 监听Tab切换，重新加载数据
watch(activeTab, (newTab) => {
  page.value = 1
  if (newTab === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
})

// 监听搜索变化
watch(search, () => {
  page.value = 1
  if (activeTab.value === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
})

// 计算属性：搜索框提示文字
const searchPlaceholder = computed(() => {
  return activeTab.value === 'asset' ? '资产包名称' : '调解案件号'
})

// 计算属性：当前列表数据
const currentList = computed(() => {
  return activeTab.value === 'asset' ? assetList.value : mediationList.value
})

// 页面初始化
onMounted(() => {
  loadAssetList()
  getDebtorOptions()
})

// 加载资产包列表
async function loadAssetList() {
  loading.value = true
  try {
    const params = {
      page: page.value,
      page_size: page_size,
      search: search.value || undefined
    }
    const response = await getDataImportList(params)
    const { state, msg, data } = response.data
    if (state === 'success') {
      assetList.value = data.results || []
      total.value = data.count || 0
    } else {
      ElMessage.error(msg || '获取资产包列表失败')
    }
  } catch (error) {
    console.error('加载资产包列表失败:', error)
    ElMessage.error('获取资产包列表失败')
  } finally {
    loading.value = false
  }
}

// 加载调解案件列表
async function loadMediationList() {
  loading.value = true
  try {
    const params = {
      page: page.value,
      page_size: page_size,
      search: search.value || undefined
    }
    const response = await getMediationCase(params)
    const { state, msg, data } = response.data
    if (state === 'success') {
      mediationList.value = data.results || []
      total.value = data.count || 0
    } else {
      ElMessage.error(msg || '获取调解案件列表失败')
    }
  } catch (error) {
    console.error('加载调解案件列表失败:', error)
    ElMessage.error('获取调解案件列表失败')
  } finally {
    loading.value = false
  }
}

// 获取债务人选项
function getDebtorOptions() {
  getDebtor({ page: 1, page_size: 1000 }).then(res => {
    const { state, msg } = res.data
    if (state === 'success') {
      debtorOptions.value = res.data.data.results.map((item: any) => ({
        label: item.debtor_name,
        value: item.id
      }))
    } else {
      ElMessage.error(msg)
    }
  })
}

// 分页改变
function handlePageChange(newPage: number) {
  page.value = newPage
  if (activeTab.value === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
}

// 搜索重置页码
function setPage1() {
  page.value = 1
}

// 打开编辑弹窗
function openEditDialog(row: any, mode: 'asset' | 'mediation') {
  editingRow.value = row
  editMode.value = mode
  showEditDialog.value = true
}

// 关闭编辑弹窗
function closeEditDialog() {
  showEditDialog.value = false
  editingRow.value = null
}

// 处理编辑确认
function handleEditConfirm(formData: any) {
  // 这里处理编辑提交逻辑
  console.log('编辑确认:', formData)
  closeEditDialog()
  // 重新加载当前列表
  if (activeTab.value === 'asset') {
    loadAssetList()
  } else {
    loadMediationList()
  }
}








</script>

<template>
  <div class="mediation-management">
    <!-- 搜索头部 -->
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput
            v-model="search"
            :placeholder="searchPlaceholder"
            @click="setPage1">
          </CustomInput>
        </div>
      </div>
    </div>

    <!-- Tab标签页 -->
    <el-tabs v-model="activeTab" class="mediation-tabs">
      <!-- 资产包Tab -->
      <el-tab-pane label="资产包" name="asset">
        <el-table
          :data="currentList"
          v-loading="loading"
          border
          :cell-style="cellStyle"
          :header-cell-style="headerCellStyle">

          <!-- 序号 -->
          <el-table-column label="序号" type="index" width="80" align="center" />

          <!-- 资产包名称 -->
          <el-table-column label="资产包名称" prop="asset_package_name" min-width="150" align="center" />

          <!-- 债权人 -->
          <el-table-column label="债权人" prop="creditor_name" min-width="120" align="center" />

          <!-- 调解信息配置 -->
          <el-table-column label="调解信息配置" align="center" min-width="180">
            <template v-slot="{ row }">
              <div class="config-preview">
                <span v-if="row.mediation_config && row.mediation_config.length > 0">
                  {{ row.mediation_config.length }}个字段
                </span>
                <span v-else>未配置</span>
              </div>
            </template>
          </el-table-column>

          <!-- 相关文件 -->
          <el-table-column label="相关文件" align="center" min-width="120">
            <template v-slot="{ row }">
              <div class="files-preview">
                <span v-if="row.attachments && row.attachments.length > 0">
                  {{ row.attachments.length }}个文件
                </span>
                <span v-else>无文件</span>
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="120" align="center">
            <template v-slot="{ row }">
              <CustomButton
                @click="openEditDialog(row, 'asset')"
                :height="32"
                btn-type="blue">
                <i class="jt-20-edit"></i>编辑
              </CustomButton>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
                  <div class="file-info">
                    <span class="file-name" :title="file.file_name || file.name">{{ file.file_name || file.name}}</span>
                  </div>
                  <div class="file-actions">
                    <!-- <i
                      v-if="file.isServerFile && file.url"
                      class="jt-20-download file-action"
                      @click="downloadRelatedFile(file)"
                      title="下载文件"></i>
                    <i
                      v-if="canPreviewFile(file)"
                      class="jt-20-preview file-action"
                      @click="previewRelatedFile(file)"
                      title="预览文件"></i> -->
                    <i
                      class="jt-20-delete file-action file-remove"
                      @click="removeRelatedFile(fileIndex)"
                      title="删除文件"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 固定字段区域 -->
      <!-- <div class="fixed-fields-section">
        <div class="fixed-fields-grid">=
          <div class="field-group">
            <label class="field-label">案件状态：</label>
            <el-select v-model="caseStatus" placeholder="请选择案件状态" style="width: 200px;">
              <el-option
                v-for="option in caseStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value">
                {{ option.label }}
              </el-option>
            </el-select>
          </div>
          
        </div>
        
        <div class="field-group full-width">
          <label class="field-label">备注：</label>
          <el-input 
            v-model="remark" 
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="500"
            show-word-limit />
        </div>
      </div> -->

      <!-- 动态字段配置区域 -->
      <div class="dynamic-fields-section">
        <!-- 区域头部：标题和添加按钮 -->
        <div class="section-header">
          <h3>调解信息配置</h3>
          <CustomButton @click="addField" :height="34" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>

        <!-- 字段列表区域 -->
        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div 
            v-for="(field, index) in dynamicFields" 
            :key="field.id"
            class="field-item">
            
            <!-- 字段头部：显示序号和删除按钮 -->
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton 
                @click="removeField(index)" 
                :height="32" 
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>

            <!-- 字段基础配置区域 -->
            <div class="field-config">
              <!-- 字段类型选择 -->
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select 
                  v-model="field.type" 
                  @change="onFieldTypeChange(field)"
                  style="width: 280px;">
                  <el-option
                    v-for="option in fieldTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                    <span style="display: flex; align-items: center;">
                      <i :class="option.icon" style="margin-right: 8px;"></i>
                      {{ option.label }}
                    </span>
                  </el-option>
                </el-select>
              </div>

              <!-- 字段标题配置 -->
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <!-- 多行文本类型且有可选项时显示选择器 -->
                <el-select 
                  v-if="shouldShowTitleSelector(field)"
                  v-model="field.title"
                  @change="onFieldTitleSelect(field, $event)"
                  allow-create
                  filterable
                  placeholder="选择或输入字段标题"
                  style="width: 280px;">
                  <el-option
                    v-for="option in availableFieldOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value" />
                </el-select>
                <!-- 其他情况显示普通输入框 -->
                <el-input 
                  v-else
                  v-model="field.title" 
                  placeholder="请输入字段标题"
                  style="width: 280px;"/>
              </div>

            </div>

            <div class="field-preview">
              <label class="config-label">
                内容预览：
                <!-- <el-tag size="small" :type="field.required ? 'danger' : 'info'">
                  {{ getFieldTypeLabel(field.type) }}{{ field.required ? ' (必填)' : '' }}
                </el-tag> -->
              </label>
              <div class="preview-content">
                <el-input 
                  v-if="field.type === FieldTypeEnum.TEXTAREA"
                  v-model="field.value"
                  type="textarea"
                  :disabled="isFieldValueDisabled(field)"
                  :rows="3"
                  :placeholder="'请输入内容'" />
                
                <el-date-picker
                  v-else-if="field.type === FieldTypeEnum.DATE"
                  v-model="field.value"
                  type="date"
                  :disabled="isFieldValueDisabled(field)"
                  :placeholder="'选择日期'"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />
                <el-input
                  v-else-if="field.type === FieldTypeEnum.AMOUNT"
                  v-model="field.value"
                  :placeholder="'请输入金额'"
                  :disabled="isFieldValueDisabled(field)"
                  @input="field.value = field.value.replace(/[^0-9.]/g, '')"
                  style="width: 200px;" />
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-fields">
          <p>还没有添加任何字段，点击"添加字段"开始配置调解信息内容</p>
        </div>
      </div>

      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>

  <!-- 资产包选择器 -->
  <AssetPackageSelector 
    :visible="showAssetSelector"
    @update:visible="showAssetSelector = false"
    @confirm="handleAssetSelectionConfirm" />
</template>

<style lang="scss" scoped>
.edit-plan-content {
  .dynamic-fields-section {
    margin-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .empty-fields {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      border-radius: 8px;
      border: 1px dashed #ddd;
    }
  }
  
  // 固定字段区域样式
  .fixed-fields-section {
    margin: 20px 0;
    
    .section-header {
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #0369a1;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .fixed-fields-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }
  .field-group{
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 36px;
  }
  .fields-list {
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }
      
      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 10px;
        
        .config-row {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        
        .config-label {
          min-width: 90px;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
      
      .field-preview {
        .config-label {
          display: block;
          margin-bottom: 12px;
          font-weight: bold;
          color: #333;
          font-size: 15px;
        }
      }
    }
  }
  .file-upload-section{
    display: inline-block;
    width: 93%;
  }
  // 通用文件列表样式
  .file-list {
    max-height: 300px;
    overflow-y: auto;
    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      margin-top: 10px;
      border-radius: 6px;
      background-color: #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        gap: 8px;
      }
      
      .file-name {
        flex: 1;
        color: #333;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-actions {
        display: flex;
        align-items: center;
        gap: 6px;

        .file-action {
          cursor: pointer;
          font-size: 16px;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;
          flex-shrink: 0;

          &.file-remove {
            color: #f56c6c;
            border-radius: 50%;
          }
        }
      }

      .file-remove {
        color: #f56c6c;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;
        flex-shrink: 0;

        &:hover {
          color: #fff;
          background-color: #f56c6c;
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .edit-plan-content {
    .dynamic-fields-section {
      .fields-list {
        .field-item {
          .field-config {
            grid-template-columns: 1fr;
          }
        }
      }
    }
    
    .btns-group {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>