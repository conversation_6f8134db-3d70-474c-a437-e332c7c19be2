<template>
  <slot :handle-keydown="onKeydown"></slot>
</template>

<script setup lang="ts">
import { watch, ref, provide } from "vue";
import type { Ref } from "vue"
import { getEdges, createFocusoutPreventedEvent, tryFocus } from "../utils"
import { useFocusReason } from "../utils";
import { FOCUS_TRAP_INJECTION_KEY } from "../tokens"
console.log('CustomFocusTrap: ', FOCUS_TRAP_INJECTION_KEY)

const emit = defineEmits<{
  (e: 'focusout-prevented', event: CustomEvent<any>): void
}>()

const props = defineProps<{
  loop: boolean,
  trapped: boolean,
}>()


const { focusReason }  = useFocusReason()

const forwardRef:Ref<HTMLElement | undefined> = ref()


const onKeydown = (e: KeyboardEvent) => {
  const { loop, trapped } = props
  if(!loop && !trapped) return
  const { altKey, ctrlKey, shiftKey, metaKey, currentTarget, key } = e
  const currentFocusingEl = document.activeElement as HTMLElement | null
  const isTabbing = !ctrlKey && !altKey && !metaKey && key === 'Tab'
  if(isTabbing && currentFocusingEl) {
    const container = currentTarget as HTMLElement
    const focusable = getEdges(container)
    const [first, last] = focusable
    const isTabbable = first && last
    if(!isTabbable) {
      if(currentFocusingEl === container) {
        const focusoutPreventedEvent = createFocusoutPreventedEvent({
          focusReason: focusReason.value
        })
        emit('focusout-prevented', focusoutPreventedEvent)
        if(!focusoutPreventedEvent.defaultPrevented) {
          e.preventDefault()
        }
      }
    } else {
      if(!shiftKey && currentFocusingEl === last) {
        const focusoutPreventedEvent = createFocusoutPreventedEvent({
          focusReason: focusReason.value
        })
        emit('focusout-prevented', focusoutPreventedEvent)
        console.log('!shiftKey: last')
        if(!focusoutPreventedEvent.defaultPrevented) {
          console.log('!shiftKey: last--inside')
          e.preventDefault()
          if(loop) tryFocus(first, true)
        }
      } else if(shiftKey && [first, container].includes(currentFocusingEl)) {
          const focusoutPreventedEvent = createFocusoutPreventedEvent({
            focusReason: focusReason.value
          })
          emit('focusout-prevented', focusoutPreventedEvent)
          if(!focusoutPreventedEvent.defaultPrevented) {
            e.preventDefault()
            if(loop) tryFocus(last, true)
          }
      }
    }
  }
}
provide(FOCUS_TRAP_INJECTION_KEY, {
  focusTrapRef: forwardRef,
  onKeydown
})
watch([forwardRef], ([forwardRef], [oldForwardRef]) => {
  if(forwardRef) {
    forwardRef.addEventListener('keydown', onKeydown)
  }
  if(oldForwardRef) {
    oldForwardRef.removeEventListener('keydown', onKeydown)
  }
})

</script>

