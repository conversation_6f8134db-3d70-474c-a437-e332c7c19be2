/* empty css             */import{C as z,h as k,c as x}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{a as E,r as c,z as D,o as v,f as V,g as n,h as o,I as B,J as K,q as M,w as h,n as m,O as P,E as f,K as S,L as I,M as L,N}from"./index-8a4876d8.js";import{_ as T}from"./_plugin-vue_export-helper-c27b6911.js";const U={class:"case-tracking"},q={class:"search-area"},J={class:"search-form"},O={class:"table-container"},$={class:"operation-buttons"},j=["onClick"],A={class:"pagination-container"},F=E({__name:"personnelDispatch",setup(G){const e=c({search:"",page:1,page_size:10}),r=c(!1),_=c([]),g=c(0);function b(){e.value.page=1,i()}async function i(){r.value=!0;const l={page:e.value.page,page_size:e.value.page_size,search:e.value.search},{data:a}=await P(l),{state:s,msg:p}=a;if(s==="success"){const{results:d,count:u}=a.data;_.value=d,g.value=u}else f.error(p);r.value=!1}function y(l){e.value.page=l,i()}function C(l,a){f.info("功能建设中")}return D(()=>{i()}),(l,a)=>{const s=N,p=S,d=I,u=L;return v(),V("div",U,[n("div",q,[n("div",J,[o(z,{modelValue:e.value.search,"onUpdate:modelValue":a[0]||(a[0]=t=>e.value.search=t),placeholder:"请输入调解案件号",class:"search-input",onKeydown:B(b,["enter"])},null,8,["modelValue","onKeydown"])])]),n("div",O,[K((v(),M(p,{data:_.value,style:{width:"100%"},"header-cell-style":m(k),"cell-style":m(x),border:"",stripe:""},{default:h(()=>[o(s,{prop:"case_number",label:"调解案件号",align:"center"}),o(s,{prop:"case_status_cn",label:"案件状态",align:"center"}),o(s,{prop:"mediator_name",label:"调解员",align:"center"}),o(s,{label:"操作",align:"center",width:"180"},{default:h(({row:t,$index:w})=>[n("div",$,[n("div",{onClick:H=>C(t,w),class:"operation-btn edit-btn"},"编辑",8,j)])]),_:1})]),_:1},8,["data","header-cell-style","cell-style"])),[[u,r.value]])]),n("div",A,[o(d,{class:"pagi",background:"","current-page":e.value.page,"onUpdate:currentPage":a[1]||(a[1]=t=>e.value.page=t),"page-size":e.value.page_size,"onUpdate:pageSize":a[2]||(a[2]=t=>e.value.page_size=t),total:g.value,layout:"prev, pager, next",onCurrentChange:y},null,8,["current-page","page-size","total"])])])}}});const ee=T(F,[["__scopeId","data-v-c0c011e2"]]);export{ee as default};
