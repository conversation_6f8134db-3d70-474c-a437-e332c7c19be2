<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import CustomButton from '@/components/common/CustomButton.vue'
import CustomInput from '@/components/common/CustomInput.vue'
import AddCase from '@/components/system/dialogs/AddCase.vue'
import EditCase from '@/components/system/dialogs/EditCase.vue'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import { ElMessage } from 'element-plus'
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue'
import { getCaseDisplay, addCaseDisplay, editCaseDisplay, deleteCaseDisplay } from '@/axios/system'
// 新增导入：token验证和操作日志记录
import { checkTokenExists } from '@/utils/tokenValidator'
import { logButtonClick } from '@/utils/operationLogger'

/**
 * 调解结果枚举
 */
enum MediationResult {
  SUCCESS = 'success',      // 调解成功
  FAILED = 'failed',        // 调解失败
  PENDING = 'pending',      // 调解中
  CANCELLED = 'cancelled'   // 调解取消
}

/**
 * 案例数据接口定义
 */
interface CaseInfo {
  id?: number
  case_name: string
  case_date: string
  amount: number
  mediation_result: string
  case_details: string
}

// 分页和搜索相关状态
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const case_date = ref('')
const caseList = ref<CaseInfo[]>([])

// 弹框控制状态
const showEditDialog = ref(false)
const showAddDialog = ref(false)
const loading = ref(false)
const showDeleteDialog = ref(false)

// 当前编辑的案例数据
const currentEditCase = ref<CaseInfo | undefined>(undefined)
const deleteCaseItem = ref<CaseInfo | null>(null)

// 调解结果选项配置
const mediationResultOptions = [
  { label: '调解成功', value: MediationResult.SUCCESS },
  { label: '调解失败', value: MediationResult.FAILED },
  { label: '调解中', value: MediationResult.PENDING },
  { label: '调解取消', value: MediationResult.CANCELLED }
]

/**
 * 搜索案例列表
 */
async function searchCaseList() {
  // 检查token是否存在
  if (!checkTokenExists()) {
    ElMessage.error('登录状态已失效，请重新登录')
    return
  }

  loading.value = true
  const params = {
    page: page.value,
    page_size: page_size,
    search: search.value,
    case_date: case_date.value
  }
  const {data} = await getCaseDisplay(params)    
  const { state, msg } = data
  if(state === 'success') {
    caseList.value = data.data.results
    total.value = data.data.count
  } else {
    ElMessage.error(msg)
  }
  loading.value = false
}

/**
 * 处理搜索
 */
function handleSearch() {
  logButtonClick('搜索案例', '案例展示') // 记录操作日志
  page.value = 1
  searchCaseList()
}

/**
 * 分页改变
 */
function pageChanged(p: number) {
  page.value = p
  searchCaseList()
}

/**
 * 打开新增案例弹框
 */
function openAddDialog() {
  logButtonClick('新增案例', '案例展示') // 记录操作日志
  showAddDialog.value = true
}

/**
 * 打开编辑弹框
 */
function openEditDialog(row: CaseInfo) {
  logButtonClick('编辑案例', '案例展示') // 记录操作日志
  currentEditCase.value = { ...row }
  showEditDialog.value = true
}

/**
 * 关闭新增弹框
 */
function closeAddDialog() {
  showAddDialog.value = false
}

/**
 * 关闭编辑弹框
 */
function closeEditDialog() {
  showEditDialog.value = false
  currentEditCase.value = undefined
}

/**
 * 处理新增案例确认
 */
async function handleAddCaseEnsure(caseData: CaseInfo) {
  try {
    // 检查token是否存在
    if (!checkTokenExists()) {
      ElMessage.error('登录状态已失效，请重新登录')
      return
    }
    const res = await addCaseDisplay(caseData)
    
    ElMessage.success('案例新增成功')
    showAddDialog.value = false
    searchCaseList()
  } catch (error) {
    ElMessage.error('新增案例失败，请重试')
  }
}

/**
 * 处理编辑案例确认
 */
async function handleEditCaseEnsure(caseData: CaseInfo) {
  try {
    // 检查token是否存在
    if (!checkTokenExists()) {
      ElMessage.error('登录状态已失效，请重新登录')
      return
    }

    const res = await editCaseDisplay(caseData, currentEditCase.value?.id)
    
    ElMessage.success('案例编辑成功')
    showEditDialog.value = false
    currentEditCase.value = undefined
    searchCaseList()
  } catch (error) {
    console.error('编辑案例失败:', error)
    ElMessage.error('编辑案例失败，请重试')
  }
}

/**
 * 打开删除确认弹框
 */
function deleteCase(row: CaseInfo) {
  // 检查token是否存在
  if (!checkTokenExists()) {
    ElMessage.error('登录状态已失效，请重新登录')
    return
  }

  deleteCaseItem.value = row
  showDeleteDialog.value = true
}

/**
 * 确认删除案例
 */
async function confirmDeleteCase() {
  if (!deleteCaseItem.value) return

  try {
    logButtonClick('删除案例', '案例展示') // 记录操作日志
    await deleteCaseDisplay(deleteCaseItem.value.id)

    ElMessage.success('案例删除成功')
    searchCaseList()
  } catch (error) {
    console.error('删除案例失败:', error)
    ElMessage.error('删除案例失败，请重试')
  } finally {
    showDeleteDialog.value = false
    deleteCaseItem.value = null
  }
}

/**
 * 格式化金额
 */
function formatAmount(amount: number): string {
  return `¥${amount.toLocaleString()}`
}

// 组件挂载时获取数据
onMounted(() => {
  searchCaseList()
})
</script>

<template>
  <div class="case-show-page">
    <!-- 搜索区域 -->
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput 
            v-model="search" 
            placeholder="搜索案例名称、案例详情" 
            @keyup.enter="handleSearch"
            @click="handleSearch" />
        </div>

        <div class="search-item">
          <label>案例日期：</label>
          <el-date-picker
            v-model="case_date"
            type="date"
            placeholder="请选择案例日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          />
        </div>
        <div class="search-item">
          <CustomButton @click="openAddDialog" :height="34">
            <i class="jt-20-add"></i>新增案例
          </CustomButton>
        </div>
      </div>
    </div>

    <div class="table-container">
		<el-table
			:data="caseList"
			v-loading="loading"
			border
			style="width: 100%"
			:cell-style="cellStyle"
			:header-cell-style="headerCellStyle">
		<el-table-column type="index" label="序号" width="80" align="center">
			<template #default="{ $index }">
			{{ page_size * (page - 1) + $index + 1 }}
			</template>
		</el-table-column>
		<el-table-column align="center" prop="case_name" label="案例名称" min-width="200" />
		<el-table-column align="center" prop="case_date" label="案例日期" width="120" />
		<el-table-column label="涉及金额" width="140" align="center">
			<template #default="{ row }">
				{{ formatAmount(row.amount) }}
			</template>
		</el-table-column>
		
		<!-- 调解结果 -->
		<el-table-column label="调解结果" width="320" align="center" prop="mediation_result">
			<template #default="{ row }">
				{{ row.mediation_result }}
			</template>
		</el-table-column>
		
		<!-- 案例详情 -->
		<el-table-column align="center" prop="case_details" label="案例详情" min-width="500">
			<template #default="{ row }">
        <div class="case-details-text">{{ row.case_details }}</div>
			<!-- <el-tooltip :content="row.case_details" placement="top">
				<div class="case-details-text">{{ row.case_details }}</div>
			</el-tooltip> -->
			</template>
		</el-table-column>
		
		<!-- 操作 -->
		<el-table-column label="操作" width="180" align="center" fixed="right">
			<template #default="{ row,$index }">
				<div class="operation-buttons">
					<div @click="openEditDialog(row,$index)" class="operation-btn edit-btn">编辑</div>
					<div @click="deleteCase(row,$index)" class="operation-btn delete-btn">删除</div>
				</div>
			</template>
		</el-table-column>
		</el-table>
		
		<!-- 分页 -->
		<div class="pagination-wrapper">
		<el-pagination
			background
			layout="prev, pager, next"
			:total="total"
			:current-page="page"
			:page-size="page_size"
			@current-change="pageChanged"
		/>
		</div>
	</div>
  </div>

  <!-- 新增案例弹框 -->
  <AddCase 
    :show-dialog="showAddDialog"
    @close="closeAddDialog"
    @ensure="handleAddCaseEnsure" />

  <!-- 编辑案例弹框 -->
  <EditCase
    :show-dialog="showEditDialog"
    :case-data="currentEditCase"
    @close="closeEditDialog"
    @ensure="handleEditCaseEnsure" />

  <!-- 删除确认弹框 -->
  <DeleteConfirmDialog
    :visible="showDeleteDialog"
    title="删除案例"
    :message="`确定要删除案例「${deleteCaseItem?.case_name}」吗？此操作不可撤销。`"
    confirm-text="确认"
    cancel-text="取消"
    @update:visible="showDeleteDialog = $event"
    @confirm="confirmDeleteCase"
    @cancel="showDeleteDialog = false" />
</template>

<style lang="scss" scoped>
.case-show-page {
  margin: 24px;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    /* display: grid;
    grid-template-columns: 300px 220px 125px;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
    :deep(.el-date-editor) {
      height: 36px;
    } */
     margin-bottom: 20px;
    
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          min-width: 80px;
          font-weight: 500;
          color: #333;
        }
        
        :deep(.el-select) {
          width: 160px;
        }
        
        :deep(.el-date-editor) {
          height: 36px;
        }
      }
    }
  }
  
  .case-details-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 540px;
  }
  
  .pagination-wrapper {
    margin-top: 20px;
	display: flex;
	justify-content: center;
    text-align: center;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .case-show-page {
    margin: 12px;
    padding: 16px;
  }
}
</style>