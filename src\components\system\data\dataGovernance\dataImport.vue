<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomButton from '@/components/common/CustomButton.vue'
import CustomInput from '@/components/common/CustomInput.vue'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import DataPreviewDialog from '@/components/system/dialogs/DataPreviewDialog.vue'
import DataImportDialog from '@/components/system/dialogs/DataImportDialog.vue'
import ReuploadDialog from '@/components/system/dialogs/ReuploadDialog.vue'
import EditDialog from '@/components/system/dialogs/EditDialog.vue'
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue'
import { logButtonClick } from '@/utils/operationLogger'
import type { 
  DataImportRecord, 
  PreviewDataType,
  Pagination,
  PageQuery,
  PageResult
} from '../../auth/type'
import { PreviewDataType as PreviewTypeEnum } from '../../auth/type'
import { getDataImportList, getDataImportDetail, addDataImport, editDataImport, deleteDataImport, reprocessExcel } from '@/axios/system'

// 搜索关键字
const search = ref('')
// 响应式数据
const loading = ref(false)
const tableData = ref<DataImportRecord[]>([])

// 预览弹框控制
const showPreviewDialog = ref(false)
const previewType = ref<PreviewDataType>(PreviewTypeEnum.OPERATION)
const previewRecordId = ref<number | null>(null)

// 数据导入弹框控制
const showImportDialog = ref(false)

// 重新上传弹框控制
const showReuploadDialog = ref(false)
const reuploadRecordId = ref<number | null>(null)
const fileName = ref('')

// 编辑弹框控制
const showEditDialog = ref(false)
const editRecordId = ref<number | null>(null)

// 删除确认弹框控制
const showDeleteDialog = ref(false)
const deleteRecord = ref<DataImportRecord | null>(null)

// 分页数据
const total = ref(0)
const page = ref(1)
const page_size = 10

// 页面加载时获取数据
onMounted(() => {
  loadTableData()
})

/**
 * 加载表格数据
 */
async function loadTableData() {
  loading.value = true
  const params = {
    page: page.value,
    page_size,
    search: search.value
  }
  const { data } = await getDataImportList(params)
  const { state, msg } = data
  if(state === 'success') {
    tableData.value = data.data.results
    total.value = data.data.count
  }else{
    ElMessage.error(msg)
  }
  loading.value = false
}

/**
 * 搜索数据
 */
function handleSearch() {
logButtonClick('搜索', '数据导入') // 记录操作日志  
 page.value = 1
  loadTableData()
}

/**
 * 分页变化处理
 */
function handlePageChange(newPage: number) {
  page.value = newPage
  loadTableData()
}

/**
 * 打开数据导入弹框
 */
function openImportDialog() {
  showImportDialog.value = true
}

/**
 * 关闭数据导入弹框
 */
function closeImportDialog() {
  showImportDialog.value = false
}

/**
 * 数据导入成功处理
 */
function handleImportSuccess() {
  loadTableData() // 刷新表格数据
}

/**
 * 关闭重新上传弹框
 */
function closeReuploadDialog() {
  showReuploadDialog.value = false
  reuploadRecordId.value = null
  fileName.value = ''
}

/**
 * 重新上传成功处理
 */
function handleReuploadSuccess() {
  loadTableData() // 刷新表格数据
}

/**
 * 关闭编辑弹框
 */
function closeEditDialog() {
  showEditDialog.value = false
  editRecordId.value = null
}

/**
 * 编辑成功处理
 */
function handleEditSuccess() {
  loadTableData() // 刷新表格数据
}

/**
 * 预览运营数据
 * @param row 当前行数据
 */
function previewOperationData(row: DataImportRecord) {
  previewType.value = PreviewTypeEnum.OPERATION
  previewRecordId.value = row.id || null
  showPreviewDialog.value = true
}

/**
 * 预览原始数据
 * @param row 当前行数据
 */
function previewOriginalData(row: DataImportRecord) {
  previewType.value = PreviewTypeEnum.ORIGINAL
  previewRecordId.value = row.id || null
  showPreviewDialog.value = true
}

/**
 * 编辑记录
 * @param row 当前行数据
 */
function handleEdit(row: DataImportRecord) {
  editRecordId.value = row.id || null
  showEditDialog.value = true
}

/**
 * 打开删除确认弹框
 * @param row 当前行数据
 */
function handleDelete(row: DataImportRecord) {
  deleteRecord.value = row
  showDeleteDialog.value = true
}

/**
 * 确认删除记录
 */
async function confirmDelete() {
  if (!deleteRecord.value) return

  try {
    // 调用删除接口
    const { data } = await deleteDataImport(deleteRecord.value.id!)
    const {state,msg} = data
    if(state == "success"){
      ElMessage.success(msg)
      loadTableData() // 刷新表格数据
    } else {
      ElMessage.error(msg || '删除失败')
    }
  } catch (error) {
    ElMessage.error('删除失败，请重试')
  } finally {
    showDeleteDialog.value = false
    deleteRecord.value = null
  }
}

/**
 * 重新上传文件
 * @param row 当前行数据
 */
function handleReupload(row: DataImportRecord) {
  reuploadRecordId.value = row.id || null
  fileName.value = row.source_file_name || ''
  showReuploadDialog.value = true
}

/**
 * 关闭预览弹框
 */
function handlePreviewClose() {
  showPreviewDialog.value = false
  previewRecordId.value = null
}

/**
 * 格式化文件大小显示
 * @param size 文件大小字符串
 * @returns 格式化后的大小
 */
/* function formatFileSize(size: string): string {
  return size
} */

// 截取文件名称
/* function formatFileName(source_file:string){
  return source_file.split('/').pop()  
} */

</script>

<template>
  <div class="data-import">
    <!-- 搜索区域 -->
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput 
            v-model="search" 
            placeholder="搜索资产包名称" 
            @keyup.enter="handleSearch"
            @click="handleSearch" />
        </div>
        <div class="search-item">
          <CustomButton 
            @click="openImportDialog" 
            type="primary"
            :height="34">
            <i class="jt-20-import"></i>数据导入
          </CustomButton>
        </div>
      </div>
    </div>

    <!-- 主表格 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column type="index" label="序号" width="80" align="center">
          <template #default="{ $index }">
            {{ (page - 1) *page_size + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="package_name" label="资产包名称" min-width="150" align="center"/>
        <el-table-column prop="source_file_name" label="原文件" min-width="150" align="center"/>
        <el-table-column prop="file_size_display" label="文件大小" min-width="80" align="center"/>
        <el-table-column prop="uploader_name" label="上传人" min-width="80" align="center" />
        <el-table-column prop="upload_time" label="上传时间" min-width="150" align="center"/>
        <el-table-column label="状态" min-width="80" align="center">
          <template #default="{ row }">
            <span
              :class="{
                'text-success': row.package_status_cn === '可用',
                'text-warning': row.package_status_cn == '不可用'
              }">
              {{ row.package_status_cn }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="unavailable_reason" label="不可用原因" min-width="290" align="center" show-overflow-tooltip>
          <template #default="{ row }">
             <div class="reason-text">{{ row.unavailable_reason }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <div class="operation-buttons">
              <div @click="previewOperationData(row)" class="operation-btn preview-btn">运营预览</div>
              <div @click="handleEdit(row)" class="operation-btn edit-btn">编辑</div>
              <div @click="previewOriginalData(row)" class="operation-btn preview-btn">原始预览</div>
              <div @click="handleDelete(row)" class="operation-btn delete-btn">删除</div>
              <div @click="handleReupload(row)" class="operation-btn preview-btn">重新上传</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="page_size"
          :total="total"
          layout="prev, pager, next"
          @current-change="handlePageChange"
          background />
      </div>
    </div>

    <!-- 数据预览弹框组件 -->
    <DataPreviewDialog
      :visible="showPreviewDialog"
      :preview-type="previewType"
      :package-id="previewRecordId"
      @close="handlePreviewClose" />

    <!-- 数据导入弹框组件 -->
    <DataImportDialog 
      :visible="showImportDialog"
      @close="closeImportDialog"
      @success="handleImportSuccess" />

    <!-- 重新上传弹框组件 -->
    <ReuploadDialog 
      :visible="showReuploadDialog"
      :record-id="reuploadRecordId"
      :file-name="fileName"
      @close="closeReuploadDialog"
      @success="handleReuploadSuccess" />

    <!-- 编辑弹框组件 -->
    <EditDialog
      :visible="showEditDialog"
      :record-id="editRecordId"
      @close="closeEditDialog"
      @success="handleEditSuccess" />

    <!-- 删除确认弹框 -->
    <DeleteConfirmDialog
      :visible="showDeleteDialog"
      title="删除资产包"
      :message="`确认删除资产包「${deleteRecord?.package_name}」吗？此操作不可撤销。`"
      confirm-text="确认"
      cancel-text="取消"
      @update:visible="showDeleteDialog = $event"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false" />
  </div>
</template>

<style lang="scss" scoped>
.data-import {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  // 搜索区域样式
  .search-header {
    margin-bottom: 20px;
    
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          min-width: 80px;
          font-weight: 500;
          color: #333;
        }
        
        :deep(.el-select) {
          width: 160px;
        }
        
        :deep(.el-date-editor) {
          height: 36px;
        }
      }
    }
  }

  // 表格容器样式
  .table-container {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    :deep(.el-table) {
      border-radius: 8px;
      
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #f5f7fa;
            color: #606266;
            font-weight: 600;
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__row {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
      .text-success{
        color:#1377C4;
      }
      .text-warning{
        color:#D94223;
      }
    }
    .reason-text{
      cursor: pointer;
      white-space: normal;
      // white-space: pre-line;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4; /* 显示的行数 */
      -webkit-box-orient: vertical;
    }
    // 分页样式
    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: center;
    }
  }

  // Element-Plus 组件样式覆盖
  :deep(.el-input) {
    .el-input__wrapper {
      border-radius: 4px;
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      border-radius: 4px;
    }
  }
}

// 操作按钮样式
.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
  
  // .operation-btn {
  //   padding: 4px 8px;
  //   border-radius: 4px;
  //   font-size: 12px;
  //   cursor: pointer;
  //   border: 1px solid;
  //   transition: all 0.3s;
  //   text-align: center;
  //   min-width: 60px;
    
  //   &.preview-btn {
  //     color: #409eff;
  //     border-color: #409eff;
  //     background-color: rgba(64, 158, 255, 0.1);
      
  //     &:hover {
  //       background-color: #409eff;
  //       color: #fff;
  //     }
  //   }
    
  //   &.edit-btn {
  //     color: #e6a23c;
  //     border-color: #e6a23c;
  //     background-color: rgba(230, 162, 60, 0.1);
      
  //     &:hover {
  //       background-color: #e6a23c;
  //       color: #fff;
  //     }
  //   }
    
  //   &.delete-btn {
  //     color: #f56c6c;
  //     border-color: #f56c6c;
  //     background-color: rgba(245, 108, 108, 0.1);
      
  //     &:hover {
  //       background-color: #f56c6c;
  //       color: #fff;
  //     }
  //   }
    
  //   &.reupload-btn {
  //     color: #67c23a;
  //     border-color: #67c23a;
  //     background-color: rgba(103, 194, 58, 0.1);
      
  //     &:hover {
  //       background-color: #67c23a;
  //       color: #fff;
  //     }
  //   }
  // }
}

// 响应式适配
@media (max-width: 1200px) {
  .data-import {
    .search-header {
      .search-row {
        .search-item {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .data-import {
    padding: 10px;

    .table-container {
      .pagination-container {
        :deep(.el-pagination) {
          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
}
</style>