<template>
  <div class="go-back-container">
    <div class="go-back-header">
      <div class="back-icon-box" @click="goback">
        <i class="jt-24-back "></i>
        <span>返回</span>
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "GoBack",
  data() {
    return {
      path: "",
    };
  },

  created() {},
  methods: {
    goback() {
      this.$emit("go-back");
    },
  },
};
</script>

<style lang="scss" scoped>
.go-back-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f2f2f2;
  .go-back-header {
    display: flex;
    justify-content: flex-start;
    height: 5rem;
    background-color: #fff;
    .back-icon-box {
      margin-left: 30px;
      min-width: 56px;
      display: inline-flex;
      align-items: center;
      font-size: 16px;
      color: #363636;
      cursor: pointer;
    }
  }
}
</style>