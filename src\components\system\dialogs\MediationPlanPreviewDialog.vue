<template>
  <CustomDialog 
    :title="'预览调解方案内容'"
    :visible="visible" 
    width="900px"
    :markclose="true"
    @update:visible="handleClose">
    
    <div class="preview-content" v-if="data">
      <div class="plan-section">
        <!-- <h3 class="section-title">案件基本信息</h3> -->
        <div class="plan-row">
          <div class="plan-label">调解案件号</div>
          <div class="plan-value">{{ data.case_number || '-' }}</div>
        </div>
        <div class="plan-row">
          <div class="plan-label">债权人</div>
          <div class="plan-value">{{ data.creditor_name || '-' }}</div>
        </div>
        <div class="plan-row">
          <div class="plan-label">债务人</div>
          <div class="plan-value">{{ data.debtor_name || '-' }}</div>
        </div>
        <div class="plan-row">
          <div class="plan-label">调解员</div>
          <div class="plan-value">{{ data.mediator_name || '-' }}</div>
        </div>
        <div class="plan-row">
          <div class="plan-label">资产包名称</div>
          <div class="plan-value">{{ data.asset_package_name || '-' }}</div>
        </div>
      </div>

      <div class="plan-section" v-if="data.mediation_config && data.mediation_config.length > 0">
        <h3 class="section-title">调解方案详情</h3>
        
        <div class="scheme-container">
          <h4 class="scheme-title">方案一：标准调解方案</h4>
          
          <div class="plan-row">
            <div class="plan-label">调解目标</div>
            <div class="plan-value">通过协商达成债务清偿协议，维护双方合法权益</div>
          </div>
          
          <div class="plan-row">
            <div class="plan-label">调解方式</div>
            <div class="plan-value">面对面调解 + 电话调解</div>
          </div>
          
          <div class="plan-row">
            <div class="plan-label">预期时间</div>
            <div class="plan-value">30个工作日</div>
          </div>
          
          <div class="plan-row">
            <div class="plan-label">调解内容</div>
            <div class="plan-value">
              1. 核实债权债务关系<br/>
              2. 分析债务人还款能力<br/>
              3. 制定合理还款计划<br/>
              4. 签署调解协议
            </div>
          </div>
           
          <div v-for="(config, index) in data.mediation_config.slice(0, 3)" :key="index">
            <div class="plan-row">
              <div class="plan-label">{{ config.title || `配置项${index + 1}` }}</div>
              <div class="plan-value">{{ config.value || '-' }}</div>
            </div>
          </div>
        </div>
 
        <div class="scheme-container" v-if="data.mediation_config.length > 3">
          <h4 class="scheme-title">方案二：快速调解方案</h4>
          
          <div class="plan-row">
            <div class="plan-label">调解目标</div>
            <div class="plan-value">快速解决争议，降低调解成本</div>
          </div>
          
          <div class="plan-row">
            <div class="plan-label">调解方式</div>
            <div class="plan-value">在线调解 + 书面确认</div>
          </div>
          
          <div class="plan-row">
            <div class="plan-label">预期时间</div>
            <div class="plan-value">15个工作日</div>
          </div>
           
          <div v-for="(config, index) in data.mediation_config.slice(3)" :key="index + 3">
            <div class="plan-row">
              <div class="plan-label">{{ config.title || `配置项${index + 4}` }}</div>
              <div class="plan-value">{{ config.value || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
 
      <!-- <div class="plan-section">
        <h3 class="section-title">风险评估</h3>
        <div class="plan-row">
          <div class="plan-label">风险等级</div>
          <div class="plan-value">中等风险</div>
        </div>
        <div class="plan-row">
          <div class="plan-label">主要风险点</div>
          <div class="plan-value">
            1. 债务人还款意愿不明确<br/>
            2. 资产状况需进一步核实<br/>
            3. 可能存在其他债权人
          </div>
        </div>
        <div class="plan-row">
          <div class="plan-label">风险控制措施</div>
          <div class="plan-value">
            1. 详细调查债务人资产状况<br/>
            2. 建立分期还款保障机制<br/>
            3. 设置违约责任条款
          </div>
        </div>
      </div> -->
    </div>

    <div class="empty-state" v-else>
      <p>暂无调解方案数据</p>
    </div>

    <!-- 底部按钮 -->
    <!-- <div class="dialog-footer">
      <CustomButton @click="handleClose" :height="34">
        <i class="jt-20-delete"></i>关闭
      </CustomButton>
    </div> -->
  </CustomDialog>
</template>

<script lang="ts" setup>
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'

interface Props {
  visible: boolean
  data: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: null
})

const emit = defineEmits<Emits>()

function handleClose() {
  emit('update:visible', false)
  emit('close')
}
</script>

<style lang="scss" scoped>
.preview-content {
  max-height: 700px;
  overflow-y: auto;
  
  .plan-section {
    margin-bottom: 32px;
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #3b82f6;
    }
    
    .scheme-container {
      margin-bottom: 24px;
      padding: 20px;
      background-color: #f8fafc;
      border-radius: 8px;
      border-left: 4px solid #10b981;
      
      .scheme-title {
        font-size: 16px;
        font-weight: 600;
        color: #059669;
        margin-bottom: 16px;
      }
    }
    
    .plan-row {
      display: flex;
      margin-bottom: 16px;
      min-height: 24px;
      
      .plan-label {
        min-width: 140px;
        font-weight: 500;
        color: #374151;
        background-color: #f3f4f6;
        padding: 8px 12px;
        border-radius: 4px 0 0 4px;
        border: 1px solid #d1d5db;
        border-right: none;
        display: flex;
        align-items: center;
      }
      
      .plan-value {
        flex: 1;
        color: #1f2937;
        padding: 8px 12px;
        background-color: #ffffff;
        border: 1px solid #d1d5db;
        border-radius: 0 4px 4px 0;
        word-break: break-all;
        line-height: 1.5;
        display: flex;
        align-items: center;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px;
  color: #9ca3af;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}
</style>
