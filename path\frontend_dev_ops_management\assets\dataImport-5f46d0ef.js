/* empty css             */import{C as ye,c as $e,h as ke}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{a as A,r as u,c as ie,o as p,f as C,g as e,h as i,w as g,i as z,t as T,D as N,q as R,n as ae,aA as Ce,E as v,aB as we,aC as Ie,p as K,m as G,e as le,s as ne,A as P,F as j,a8 as de,aD as De,j as ce,Q as te,S as se,aE as xe,J as re,aF as Ve,av as Se,aG as Ee,M as ue,z as Fe,H as ze,I as Ue,a9 as Be,K as Te,L as Re,B as Oe,ad as Ne,aH as Me,N as Le}from"./index-8a4876d8.js";import{C as M}from"./CustomButton-ea16d5c5.js";import{D as Pe}from"./DataPreviewDialog-ee378a4a.js";import{_ as oe}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";import{P as ee}from"./types-d67a131c.js";import"./construct-a2f67563.js";const je=d=>(K("data-v-41ce6549"),d=d(),G(),d),We={class:"file-upload-component"},qe={class:"upload-container"},Ae=["disabled"],Ke=je(()=>e("i",{class:"jt-20-upload"},null,-1)),Ge={key:0,class:"upload-tip"},He={key:0},Je={key:1},Qe={key:2},Xe={key:0,class:"file-info"},Ye=["title"],Ze=A({__name:"FileUpload",props:{modelValue:{},accept:{default:"*"},maxSize:{default:100},placeholder:{default:"选择文件"},disabled:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},validateType:{}},emits:["update:modelValue","change","error"],setup(d,{expose:x,emit:f}){const _=d,a=u(),l=u(_.modelValue||null),y=ie(()=>l.value?{name:l.value.name,size:l.value.size,type:l.value.type}:null);function S(c){const t=c.raw;if(_.validateType&&!_.validateType(t)){w();return}if(!_.validateType&&_.accept!=="*"&&!_.accept.split(",").map(s=>s.trim()).some(s=>s.startsWith(".")?t.name.toLowerCase().endsWith(s.toLowerCase()):t.type===s)){const s=`只能上传 ${_.accept} 格式的文件！`;v.error(s),f("error",s),w();return}if(!(t.size/1024/1024<=_.maxSize)){const r=`上传文件大小不能超过 ${_.maxSize}MB！`;v.error(r),f("error",r),w();return}l.value=t,f("update:modelValue",t),f("change",t),console.log("文件选择成功:",t.name)}function D(){l.value=null,f("update:modelValue",null),f("change",null)}function w(){a.value&&a.value.clearFiles(),D()}return x({clearFile:w}),(c,t)=>{const o=we,r=Ie;return p(),C("div",We,[e("div",qe,[i(o,{ref_key:"uploadRef",ref:a,class:"file-upload","auto-upload":!1,"on-change":S,"on-remove":D,"show-file-list":!1,limit:1,accept:c.accept,disabled:c.disabled},{trigger:g(()=>[e("button",{class:"upload-trigger-btn",disabled:c.disabled},[Ke,z(T(c.placeholder),1)],8,Ae)]),_:1},8,["accept","disabled"]),c.accept!=="*"||c.maxSize<100?(p(),C("div",Ge,[c.accept!=="*"?(p(),C("span",He,"只能上传 "+T(c.accept)+" 文件",1)):N("",!0),c.accept!=="*"&&c.maxSize<100?(p(),C("span",Je,"，")):N("",!0),c.maxSize<100?(p(),C("span",Qe,"且不超过 "+T(c.maxSize)+"MB",1)):N("",!0)])):N("",!0)]),y.value?(p(),C("div",Xe,[e("span",{class:"file-name",title:y.value.name},T(y.value.name),9,Ye),c.disabled?N("",!0):(p(),R(r,{key:0,class:"remove-icon",onClick:D},{default:g(()=>[i(ae(Ce))]),_:1}))])):N("",!0)])}}});const pe=H(Ze,[["__scopeId","data-v-41ce6549"]]),J=d=>(K("data-v-40d7bad3"),d=d(),G(),d),ea={class:"form-container"},aa={class:"form-item"},la=J(()=>e("label",{class:"form-label"},[e("span",{class:"required"},"*"),z("资产包名称")],-1)),ta={class:"form-input"},sa={class:"form-item"},oa=J(()=>e("label",{class:"form-label"},[e("span",{class:"required"},"*"),z("上传文件")],-1)),ia={class:"form-input"},na={class:"form-item"},da=J(()=>e("label",{class:"form-label"},"债权人",-1)),ca={class:"form-input"},ra={class:"dialog-footer"},ua=J(()=>e("i",{class:"jt-20-ensure"},null,-1)),pa=J(()=>e("i",{class:"jt-20-delete"},null,-1)),_a=A({__name:"DataImportDialog",props:{visible:{type:Boolean}},emits:["close","success"],setup(d,{emit:x}){const f=d,_=u(!1),a=le({package_name:"",file:null,creditor:""}),l=u([]),y=u(!1);ne(()=>f.visible,o=>{o&&S()});async function S(){y.value=!0;const{data:o}=await de({page_size:1e3}),{state:r,msg:h}=o;r=="success"?l.value=o.data.results.map(s=>({id:s.id,name:s.creditor_name})):v.error(h),y.value=!1}function D(o){return o.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||o.type==="application/vnd.ms-excel"||o.name.endsWith(".xlsx")||o.name.endsWith(".xls")?!0:(v.error("只能上传 Excel 文件！"),!1)}function w(o){a.file=o}async function c(){if(!a.package_name.trim()){v.error("请输入资产包名称");return}if(!a.file){v.error("请选择要上传的文件");return}_.value=!0;const o=new FormData;o.append("package_name",a.package_name),o.append("file",a.file),console.log(a.creditor,"-----"),o.append("creditor",a.creditor);const{data:r}=await De(o),{state:h,msg:s}=r;h==="success"?(v.success(s),x("success"),t()):v.error(s),_.value=!1}function t(){a.package_name="",a.file=null,a.creditor="",x("close")}return(o,r)=>{const h=ce,s=te,U=se;return p(),R(oe,{visible:o.visible,title:"数据导入",width:"600px","onUpdate:visible":t,class:"data-import-dialog"},{default:g(()=>[e("div",ea,[e("div",aa,[la,e("div",ta,[i(h,{modelValue:a.package_name,"onUpdate:modelValue":r[0]||(r[0]=n=>a.package_name=n),placeholder:"请输入资产包名称",clearable:""},null,8,["modelValue"])])]),e("div",sa,[oa,e("div",ia,[i(pe,{modelValue:a.file,"onUpdate:modelValue":r[1]||(r[1]=n=>a.file=n),accept:".xlsx,.xls","max-size":100,placeholder:"选择文件","validate-type":D,onChange:w},null,8,["modelValue"])])]),e("div",na,[da,e("div",ca,[i(U,{modelValue:a.creditor,"onUpdate:modelValue":r[2]||(r[2]=n=>a.creditor=n),placeholder:"请选择债权人",filterable:"",clearable:"",loading:y.value,style:{width:"100%"}},{default:g(()=>[(p(!0),C(j,null,P(l.value,n=>(p(),R(s,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])])])]),e("div",ra,[i(M,{onClick:c,loading:_.value,height:34,"btn-type":"blue"},{default:g(()=>[ua,z("确认 ")]),_:1},8,["loading"]),i(M,{onClick:t,height:34},{default:g(()=>[pa,z("取消 ")]),_:1})])]),_:1},8,["visible"])}}});const ma=H(_a,[["__scopeId","data-v-40d7bad3"]]),Q=d=>(K("data-v-6d713a8f"),d=d(),G(),d),fa={class:"form-container"},va={key:0,class:"form-item"},ga=Q(()=>e("label",{class:"form-label"},"已上传文件",-1)),ha={class:"form-input"},ba={class:"uploaded-files-list"},ya={class:"file-name"},$a={class:"form-item"},ka=Q(()=>e("label",{class:"form-label"},[e("span",{class:"required"},"*"),z("上传文件")],-1)),Ca={class:"form-input"},wa={class:"form-item"},Ia=Q(()=>e("label",{class:"form-label"},"是否重置字段映射配置",-1)),Da={class:"form-input"},xa={class:"dialog-footer"},Va=Q(()=>e("i",{class:"jt-20-ensure"},null,-1)),Sa=Q(()=>e("i",{class:"jt-20-delete"},null,-1)),Ea=A({__name:"ReuploadDialog",props:{visible:{type:Boolean},recordId:{},fileName:{}},emits:["close","success"],setup(d,{emit:x}){const f=d,_=u(!1),a=le({file:null,resetFieldMapping:!1}),l=[{label:"是",value:!0},{label:"否",value:!1}],y=ie(()=>f.fileName?f.fileName.split(",").map(t=>t.trim()).filter(t=>t):[]);function S(t){return t.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||t.type==="application/vnd.ms-excel"||t.name.endsWith(".xlsx")||t.name.endsWith(".xls")?!0:(v.error("只能上传 Excel 文件！"),!1)}function D(t){a.file=t}async function w(){if(!a.file){v.error("请选择要上传的文件");return}if(!f.recordId){v.error("记录ID不存在");return}_.value=!0;const t=new FormData;t.append("file",a.file),t.append("reset_mappings",a.resetFieldMapping.toString());const{data:o}=await xe(f.recordId,t),{state:r,msg:h}=o;r==="success"?(v.success(h),x("success"),c()):v.error(h),_.value=!1}function c(){a.file=null,a.resetFieldMapping=!1,x("close")}return(t,o)=>{const r=te,h=se;return p(),R(oe,{visible:t.visible,title:"重新上传",width:"600px","onUpdate:visible":c,class:"reupload-dialog"},{default:g(()=>[e("div",fa,[y.value.length>0?(p(),C("div",va,[ga,e("div",ha,[e("div",ba,[(p(!0),C(j,null,P(y.value,(s,U)=>(p(),C("div",{key:U,class:"uploaded-file-item"},[e("span",ya,T(s),1)]))),128))])])])):N("",!0),e("div",$a,[ka,e("div",Ca,[i(pe,{modelValue:a.file,"onUpdate:modelValue":o[0]||(o[0]=s=>a.file=s),accept:".xlsx,.xls","max-size":100,placeholder:"选择文件","validate-type":S,onChange:D},null,8,["modelValue"])])]),e("div",wa,[Ia,e("div",Da,[i(h,{modelValue:a.resetFieldMapping,"onUpdate:modelValue":o[1]||(o[1]=s=>a.resetFieldMapping=s),placeholder:"请选择",style:{width:"100%"}},{default:g(()=>[(p(),C(j,null,P(l,s=>i(r,{key:s.value,label:s.label,value:s.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])])])]),e("div",xa,[i(M,{onClick:w,loading:_.value,height:34,"btn-type":"blue"},{default:g(()=>[Va,z("确认 ")]),_:1},8,["loading"]),i(M,{onClick:c,height:34},{default:g(()=>[Sa,z("取消 ")]),_:1})])]),_:1},8,["visible"])}}});const Fa=H(Ea,[["__scopeId","data-v-6d713a8f"]]),W=d=>(K("data-v-e5a7921a"),d=d(),G(),d),za={class:"form-container"},Ua={class:"basic-info"},Ba={class:"form-item"},Ta=W(()=>e("label",{class:"form-label"},"资产包名称",-1)),Ra={class:"form-input"},Oa={class:"form-item"},Na=W(()=>e("label",{class:"form-label"},[e("span",{class:"required"},"*"),z("原文件名称")],-1)),Ma={class:"form-input"},La={class:"form-item"},Pa=W(()=>e("label",{class:"form-label"},"债权人",-1)),ja={class:"form-input"},Wa={class:"field-mapping"},qa={class:"mapping-container"},Aa=W(()=>e("div",{class:"mapping-header"},[e("div",{class:"field-column"},"系统字段"),e("div",{class:"file-column"},"文件列")],-1)),Ka={class:"mapping-list"},Ga={class:"field-info"},Ha={class:"field-label"},Ja={class:"column-select"},Qa={class:"dialog-footer"},Xa=W(()=>e("i",{class:"jt-20-ensure"},null,-1)),Ya=W(()=>e("i",{class:"jt-20-delete"},null,-1)),Za=A({__name:"EditDialog",props:{visible:{type:Boolean},recordId:{}},emits:["close","success"],setup(d,{emit:x}){const f=d,_=u(!1),a=u(!1),l=le({package_name:"",source_file_name:"",creditor:null,field_mappings_detail:[]}),y=u([]),S=u(!1),D=u([]),w=u(!1);ne(()=>f.visible,n=>{n&&f.recordId&&(c(),t(),o())});async function c(){if(!f.recordId)return;a.value=!0;const n=await Ve(f.recordId),{data:m}=n.data;l.package_name=m.package_name,l.source_file_name=m.source_file_name,l.creditor=m.creditor||null,l.field_mappings_detail=m.field_mappings_detail||[],a.value=!1}async function t(){S.value=!0;const{data:n}=await de({page_size:1e3}),{state:m,msg:E}=n;m=="success"?y.value=n.data.results.map($=>({id:$.id,name:$.creditor_name})):v.error(E),S.value=!1}async function o(){w.value=!0;try{const{data:n}=await Se({page_size:1e3}),{state:m,msg:E}=n;m==="success"?D.value=n.data.results.map($=>({id:$.id,field_name:$.field_name})):v.error(E)}catch{v.error("加载字段配置失败")}finally{w.value=!1}}function r(n,m){const E=m;if(l.field_mappings_detail[n])if(E){const $=D.value.find(B=>B.id===E);$&&(l.field_mappings_detail[n].mapped_field_config={id:$.id,field_name:$.field_name})}else l.field_mappings_detail[n].mapped_field_config=null}function h(n){var m;return((m=n.mapped_field_config)==null?void 0:m.id)||null}async function s(){if(!l.package_name.trim()){v.error("请输入资产包名称");return}_.value=!0;const n={package_name:l.package_name,creditor:l.creditor,field_mappings:l.field_mappings_detail.map(B=>{var L;return{id:B.id,original_field_name:B.original_field_name,mapped_field_config_id:((L=B.mapped_field_config)==null?void 0:L.id)||null}})},{data:m}=await Ee(n,f.recordId),{state:E,msg:$}=m;E==="success"?(v.success($),x("success"),U()):v.error($),_.value=!1}function U(){l.package_name="",l.source_file_name="",l.creditor=null,l.field_mappings_detail=[],x("close")}return(n,m)=>{const E=ce,$=te,B=se,L=ue;return p(),R(oe,{visible:n.visible,title:"编辑资产包",width:"800px","onUpdate:visible":U,class:"edit-dialog"},{default:g(()=>[re((p(),C("div",za,[e("div",Ua,[e("div",Ba,[Ta,e("div",Ra,[i(E,{modelValue:l.package_name,"onUpdate:modelValue":m[0]||(m[0]=k=>l.package_name=k),placeholder:"请输入资产包名称",clearable:""},null,8,["modelValue"])])]),e("div",Oa,[Na,e("div",Ma,[i(E,{modelValue:l.source_file_name,"onUpdate:modelValue":m[1]||(m[1]=k=>l.source_file_name=k),placeholder:"原文件名称",disabled:""},null,8,["modelValue"])])]),e("div",La,[Pa,e("div",ja,[i(B,{modelValue:l.creditor,"onUpdate:modelValue":m[2]||(m[2]=k=>l.creditor=k),placeholder:"请选择债权人",filterable:"",clearable:"",loading:S.value,style:{width:"100%"}},{default:g(()=>[(p(!0),C(j,null,P(y.value,k=>(p(),R($,{key:k.id,label:k.name,value:k.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])])])]),e("div",Wa,[e("div",qa,[Aa,e("div",Ka,[(p(!0),C(j,null,P(l.field_mappings_detail,(k,X)=>(p(),C("div",{key:`mapping-${k.id||X}-${k.fieldName||""}`,class:"mapping-item"},[e("div",Ga,[e("span",Ha,T(k.original_field_name||k.fieldLabel)+" ",1)]),e("div",Ja,[i(B,{"model-value":h(k),"onUpdate:modelValue":O=>r(X,O),placeholder:"请选择文件列",filterable:"",clearable:"",loading:w.value,style:{width:"100%"}},{default:g(()=>[(p(!0),C(j,null,P(D.value,O=>(p(),R($,{key:O.id,label:O.field_name,value:O.id},null,8,["label","value"]))),128))]),_:2},1032,["model-value","onUpdate:modelValue","loading"])])]))),128))])])])])),[[L,a.value]]),e("div",Qa,[i(M,{onClick:s,loading:_.value,height:34,"btn-type":"blue"},{default:g(()=>[Xa,z("确认 ")]),_:1},8,["loading"]),i(M,{onClick:U,height:34},{default:g(()=>[Ya,z("取消 ")]),_:1})])]),_:1},8,["visible"])}}});const el=H(Za,[["__scopeId","data-v-e5a7921a"]]),al=d=>(K("data-v-c309ebab"),d=d(),G(),d),ll={class:"data-import"},tl={class:"search-header"},sl={class:"search-row"},ol={class:"search-item"},il={class:"search-item"},nl=al(()=>e("i",{class:"jt-20-import"},null,-1)),dl={class:"table-container"},cl={class:"reason-text"},rl={class:"operation-buttons"},ul=["onClick"],pl=["onClick"],_l=["onClick"],ml=["onClick"],fl=["onClick"],vl={class:"pagination-container"},Z=10,gl=A({__name:"dataImport",setup(d){const x=u(""),f=u(!1),_=u([]),a=u(!1),l=u(ee.OPERATION),y=u(null),S=u(!1),D=u(!1),w=u(null),c=u(""),t=u(!1),o=u(null),r=u(0),h=u(1);Fe(()=>{s()});async function s(){f.value=!0;const I={page:h.value,page_size:Z,search:x.value},{data:V}=await ze(I),{state:F,msg:q}=V;F==="success"?(_.value=V.data.results,r.value=V.data.count):v.error(q),f.value=!1}function U(){Be("搜索","数据导入"),h.value=1,s()}function n(I){h.value=I,s()}function m(){S.value=!0}function E(){S.value=!1}function $(){s()}function B(){D.value=!1,w.value=null,c.value=""}function L(){s()}function k(){t.value=!1,o.value=null}function X(){s()}function O(I){l.value=ee.OPERATION,y.value=I.id||null,a.value=!0}function _e(I){l.value=ee.ORIGINAL,y.value=I.id||null,a.value=!0}function me(I){o.value=I.id||null,t.value=!0}async function fe(I){try{await Ne.confirm(`确认删除资产包 "${I.package_name}" 吗？`,"删除确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const{data:V}=await Me(I.id),{state:F,msg:q}=V;F=="success"&&(v.success(q),s())}catch(V){V==="cancel"&&v.info("已取消删除")}}function ve(I){w.value=I.id||null,c.value=I.source_file_name||"",D.value=!0}function ge(){a.value=!1,y.value=null}return(I,V)=>{const F=Le,q=Te,he=Re,be=ue;return p(),C("div",ll,[e("div",tl,[e("div",sl,[e("div",ol,[i(ye,{modelValue:x.value,"onUpdate:modelValue":V[0]||(V[0]=b=>x.value=b),placeholder:"搜索资产包名称",onKeyup:Ue(U,["enter"]),onClick:U},null,8,["modelValue","onKeyup"])]),e("div",il,[i(M,{onClick:m,type:"primary",height:34},{default:g(()=>[nl,z("数据导入 ")]),_:1})])])]),e("div",dl,[re((p(),R(q,{data:_.value,border:"",style:{width:"100%"},"cell-style":ae($e),"header-cell-style":ae(ke)},{default:g(()=>[i(F,{type:"index",label:"序号",width:"80",align:"center"},{default:g(({$index:b})=>[z(T((h.value-1)*Z+b+1),1)]),_:1}),i(F,{prop:"package_name",label:"资产包名称","min-width":"150",align:"center"}),i(F,{prop:"source_file_name",label:"原文件","min-width":"150",align:"center"}),i(F,{prop:"file_size_display",label:"文件大小","min-width":"80",align:"center"}),i(F,{prop:"uploader_name",label:"上传人","min-width":"80",align:"center"}),i(F,{prop:"upload_time",label:"上传时间","min-width":"150",align:"center"}),i(F,{label:"状态","min-width":"80",align:"center"},{default:g(({row:b})=>[e("span",{class:Oe({"text-success":b.package_status_cn==="可用","text-warning":b.package_status_cn=="不可用"})},T(b.package_status_cn),3)]),_:1}),i(F,{prop:"unavailable_reason",label:"不可用原因","min-width":"290",align:"center","show-overflow-tooltip":""},{default:g(({row:b})=>[e("div",cl,T(b.unavailable_reason),1)]),_:1}),i(F,{label:"操作",width:"180",fixed:"right",align:"center"},{default:g(({row:b})=>[e("div",rl,[e("div",{onClick:Y=>O(b),class:"operation-btn preview-btn"},"运营预览",8,ul),e("div",{onClick:Y=>me(b),class:"operation-btn edit-btn"},"编辑",8,pl),e("div",{onClick:Y=>_e(b),class:"operation-btn preview-btn"},"原始预览",8,_l),e("div",{onClick:Y=>fe(b),class:"operation-btn delete-btn"},"删除",8,ml),e("div",{onClick:Y=>ve(b),class:"operation-btn preview-btn"},"重新上传",8,fl)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[be,f.value]]),e("div",vl,[i(he,{"current-page":h.value,"onUpdate:currentPage":V[1]||(V[1]=b=>h.value=b),"page-size":Z,"onUpdate:pageSize":V[2]||(V[2]=b=>Z=b),total:r.value,layout:"prev, pager, next",onCurrentChange:n,background:""},null,8,["current-page","total"])])]),i(Pe,{visible:a.value,"preview-type":l.value,"package-id":y.value,onClose:ge},null,8,["visible","preview-type","package-id"]),i(ma,{visible:S.value,onClose:E,onSuccess:$},null,8,["visible"]),i(Fa,{visible:D.value,"record-id":w.value,"file-name":c.value,onClose:B,onSuccess:L},null,8,["visible","record-id","file-name"]),i(el,{visible:t.value,"record-id":o.value,onClose:k,onSuccess:X},null,8,["visible","record-id"])])}}});const Sl=H(gl,[["__scopeId","data-v-c309ebab"]]);export{Sl as default};
