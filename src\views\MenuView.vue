<script lang="ts" setup>
import { computed, ref } from "vue"
import type { Ref } from "vue"
import { RouterLink, useRoute } from 'vue-router'
import type { MenuItem } from "./type"

let menuList: Ref<MenuItem[]> = ref([
  {
    id: 0,
    name: '系统设置',
    link: '/home/<USER>',
    icon: 'jt-40-setting',
    active: true
  },
])
const route = useRoute()
</script>

<template>
  <div class="menu-view">
    <div v-for="({link, name, icon}, index) in menuList" :key="index" :class="['link-item', route.path.indexOf(link) > -1 ? '': 'bg' ]">
      <div class="link-wrap">
        <div class="light"></div>
        <RouterLink :to="link" class="link-style">
          <i :class="icon"></i>
          <span>{{ name }}</span>
        </RouterLink>
        <div class="dark"></div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.menu-view {
  display: flex;
  align-items: center;
  height: 60px;
  font-family: Microsoft YaHei;
  background-image: url('@/assets/images/bg/head_list_bg.png');
  display: flex;
  justify-content: center;
  align-items: center;
  .link-item {
    height: 5.9rem;
    margin-top: 1px;
    background-image: linear-gradient(180deg, 
    #2c7cc7 0%, 
    #88c6ff 100%);
    border-radius: 0.4rem 0.4rem 0rem 0rem;
    cursor: pointer;
    font-size: 20px;
    .link-wrap {
      display: flex;
      justify-content: center;
      align-items: center;
      .link-style {
        text-decoration: none;
        color: #fff;
        display: flex;
        align-items: center;
        width: 100%;
        height: 5.9rem;
        margin: 0 24px;
      }
    }
  }
}
.bg {
  background: transparent !important;
}

.dark {
  width: 1px;
  height: 5.9rem;
  background-image:  linear-gradient(180deg, #53aafd 0%, #44528a 100%)
}

.light {
  width: 0.2rem;
  height: 5.9rem;
  background-image: linear-gradient(0deg, 
  #61c0ff 0%, 
  #1d85fb 100%);
}
</style>