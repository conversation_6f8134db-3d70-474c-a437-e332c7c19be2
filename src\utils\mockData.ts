// 外呼线路申请和短信申请的模拟数据服务
// 用于模拟后端API响应，支持基本的增删改查操作

export interface OutboundLineApplication {
  id: number;
  companyName: string; // 公司名称
  contactPerson: string; // 联系人
  contactPhone: string; // 联系电话
  lineCount: number; // 申请线路数量
  useReason: string; // 使用原因
  status: '待审核' | '已通过' | '已拒绝'; // 申请状态
  applyDate: string; // 申请日期
  approveDate?: string; // 审批日期
  remark?: string; // 备注
}

export interface SmsApplication {
  id: number;
  companyName: string; // 公司名称
  contactPerson: string; // 联系人
  contactPhone: string; // 联系电话
  smsCount: number; // 申请短信数量
  useTemplate: string; // 使用模板
  status: '待审核' | '已通过' | '已拒绝'; // 申请状态
  applyDate: string; // 申请日期
  approveDate?: string; // 审批日期
  remark?: string; // 备注
}

// 模拟外呼线路申请数据
const outboundLineData: OutboundLineApplication[] = [
  {
    id: 1,
    companyName: '某某科技有限公司',
    contactPerson: '张三',
    contactPhone: '13800138001',
    lineCount: 10,
    useReason: '客户服务外呼',
    status: '已通过',
    applyDate: '2024-01-15',
    approveDate: '2024-01-16',
    remark: '已审核通过'
  },
  {
    id: 2,
    companyName: '某某贸易公司',
    contactPerson: '李四',
    contactPhone: '13800138002',
    lineCount: 5,
    useReason: '销售推广',
    status: '待审核',
    applyDate: '2024-01-20',
    remark: '等待审核'
  }
];

// 模拟短信申请数据
const smsData: SmsApplication[] = [
  {
    id: 1,
    companyName: '某某科技有限公司',
    contactPerson: '王五',
    contactPhone: '13800138003',
    smsCount: 1000,
    useTemplate: '【公司名】您的验证码是：{code}，请在5分钟内使用。',
    status: '已通过',
    applyDate: '2024-01-10',
    approveDate: '2024-01-11',
    remark: '验证码短信模板'
  },
  {
    id: 2,
    companyName: '某某商城',
    contactPerson: '赵六',
    contactPhone: '13800138004',
    smsCount: 500,
    useTemplate: '【商城】您的订单已发货，物流单号：{trackingNumber}',
    status: '待审核',
    applyDate: '2024-01-22',
    remark: '物流通知短信'
  }
];

// 模拟API响应延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 外呼线路申请相关API
export const outboundLineApi = {
  // 获取申请列表
  async getList(): Promise<OutboundLineApplication[]> {
    await delay(500); // 模拟网络延迟
    return [...outboundLineData];
  },

  // 添加申请
  async add(data: Omit<OutboundLineApplication, 'id' | 'applyDate' | 'status'>): Promise<OutboundLineApplication> {
    await delay(300);
    const newId = Math.max(...outboundLineData.map(item => item.id)) + 1;
    const newItem: OutboundLineApplication = {
      ...data,
      id: newId,
      status: '待审核',
      applyDate: new Date().toISOString().split('T')[0]
    };
    outboundLineData.push(newItem);
    return newItem;
  },

  // 更新申请
  async update(id: number, data: Partial<OutboundLineApplication>): Promise<OutboundLineApplication> {
    await delay(300);
    const index = outboundLineData.findIndex(item => item.id === id);
    if (index === -1) throw new Error('申请不存在');
    
    outboundLineData[index] = { ...outboundLineData[index], ...data };
    return outboundLineData[index];
  },

  // 删除申请
  async delete(id: number): Promise<void> {
    await delay(300);
    const index = outboundLineData.findIndex(item => item.id === id);
    if (index === -1) throw new Error('申请不存在');
    
    outboundLineData.splice(index, 1);
  },

  // 审核申请
  async approve(id: number, status: '已通过' | '已拒绝', remark?: string): Promise<OutboundLineApplication> {
    await delay(300);
    const index = outboundLineData.findIndex(item => item.id === id);
    if (index === -1) throw new Error('申请不存在');
    
    outboundLineData[index].status = status;
    outboundLineData[index].approveDate = new Date().toISOString().split('T')[0];
    if (remark) outboundLineData[index].remark = remark;
    
    return outboundLineData[index];
  }
};

// 短信申请相关API
export const smsApi = {
  // 获取申请列表
  async getList(): Promise<SmsApplication[]> {
    await delay(500);
    return [...smsData];
  },

  // 添加申请
  async add(data: Omit<SmsApplication, 'id' | 'applyDate' | 'status'>): Promise<SmsApplication> {
    await delay(300);
    const newId = Math.max(...smsData.map(item => item.id)) + 1;
    const newItem: SmsApplication = {
      ...data,
      id: newId,
      status: '待审核',
      applyDate: new Date().toISOString().split('T')[0]
    };
    smsData.push(newItem);
    return newItem;
  },

  // 更新申请
  async update(id: number, data: Partial<SmsApplication>): Promise<SmsApplication> {
    await delay(300);
    const index = smsData.findIndex(item => item.id === id);
    if (index === -1) throw new Error('申请不存在');
    
    smsData[index] = { ...smsData[index], ...data };
    return smsData[index];
  },

  // 删除申请
  async delete(id: number): Promise<void> {
    await delay(300);
    const index = smsData.findIndex(item => item.id === id);
    if (index === -1) throw new Error('申请不存在');
    
    smsData.splice(index, 1);
  },

  // 审核申请
  async approve(id: number, status: '已通过' | '已拒绝', remark?: string): Promise<SmsApplication> {
    await delay(300);
    const index = smsData.findIndex(item => item.id === id);
    if (index === -1) throw new Error('申请不存在');
    
    smsData[index].status = status;
    smsData[index].approveDate = new Date().toISOString().split('T')[0];
    if (remark) smsData[index].remark = remark;
    
    return smsData[index];
  }
}; 