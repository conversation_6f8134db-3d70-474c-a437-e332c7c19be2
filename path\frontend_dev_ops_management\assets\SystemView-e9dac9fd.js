import{a as c,b as d,c as m,s as h,o as f,f as u,h as o,g as p,n as g,R as v,v as l,x as C}from"./index-8a4876d8.js";import{S as k}from"./SidebarComp-c556c6c8.js";import{_}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css             */const A={style:{display:"flex"}},j={class:"router-wrap"},b=c({__name:"SystemView",setup(y){const n=d(),t=[{link:"",isActive:!0,isCollapse:!0,parent:"运营管理",children:[{isChildActive:!0,name:"资产包管理",link:"/home/<USER>/assetPackage",icon:"jt-20-asset-package"},{isChildActive:!1,isChildCollapse:!1,name:"调解管理",icon:"jt-20-mediation",children:[{isGrandChildActive:!1,name:"案件跟踪",link:"/home/<USER>/caseTracking",icon:"jt-20-disposal"},{isGrandChildActive:!1,name:"调解信息",link:"/home/<USER>/mediationInformation",icon:"jt-20-debtor-evaluation"},{isGrandChildActive:!1,name:"调解方案",link:"/home/<USER>/disposalPlan",icon:"jt-20-disposal-plan"},{isGrandChildActive:!1,name:"人员调度",link:"/home/<USER>/personnelDispatch",icon:"jt-20-personnel-dispatch"}]},{isChildActive:!1,name:"债权人管理",link:"/home/<USER>/creditor",icon:"jt-20-creditor"},{isChildActive:!1,name:"债务人管理",link:"/home/<USER>/debtor",icon:"jt-20-debtor"},{isChildActive:!1,name:"诉前保全",link:"/home/<USER>/preAction",icon:"jt-20-pre-action"},{isChildActive:!1,name:"信息修复",link:"/home/<USER>/informationRepair",icon:"jt-20-information-repair"},{isChildActive:!1,name:"案例展示",link:"/home/<USER>/caseShow",icon:"jt-20-creditor"},{isChildActive:!1,name:"投诉建议",link:"/home/<USER>/complaintFeedback",icon:"jt-20-complaint"},{isChildActive:!1,isChildCollapse:!1,name:"外呼管理",icon:"jt-20-outbound-call",children:[{isGrandChildActive:!1,name:"语音外呼记录",link:"/home/<USER>/outboundCall",icon:"jt-20-outbound-voice-call"},{isGrandChildActive:!1,name:"短信发送记录",link:"/home/<USER>/messageRecord",icon:"jt-20-message"}]}]},{link:"",isActive:!1,isCollapse:!1,parent:"数据管理",children:[{isChildActive:!1,isChildCollapse:!1,name:"数据治理",icon:"jt-20-data-governance",children:[{isGrandChildActive:!1,name:"字段配置",link:"/home/<USER>/fieldConfiguration",icon:"jt-20-outbound-voice-call"},{isGrandChildActive:!1,name:"数据导入",link:"/home/<USER>/dataImport",icon:"jt-20-message"}]},{isChildActive:!1,name:"数据分析",link:"/home/<USER>/dataAnalysis",icon:"jt-20-data-analysis"}]}],s=m(()=>n.permissionsLoaded?(console.log("权限已加载，进行菜单过滤",n.userPermissions),t.map(e=>({...e,children:e.children.filter(i=>{if(i.link)return l(n.userPermissions,i.link);if(i.children){const a=i.children.filter(r=>l(n.userPermissions,r.link));return a.length===0?!1:(i.children=a,!0)}return!0})})).filter(e=>e.children.length>0)):(console.log("权限未加载，显示完整菜单"),t));return h(s,e=>{C.setMenuList(e)},{immediate:!0}),(e,i)=>(f(),u("div",A,[o(k,{"menu-list":s.value},null,8,["menu-list"]),p("div",j,[o(g(v))])]))}});const P=_(b,[["__scopeId","data-v-59d5d184"]]);export{P as default};
