/* empty css             */import{C as be,c as ue,h as de}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{C as j}from"./CustomButton-ea16d5c5.js";/* empty css                        *//* empty css                       *//* empty css                  *//* empty css                     */import{_ as re}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{F as m,_ as ke}from"./DeleteTips.vue_vue_type_style_index_0_lang-58486e4e.js";import{a as te,r as f,c as H,s as ae,o as n,q as h,w as u,g as a,h as t,f as g,A as M,F as U,i as E,t as F,B as _e,n as x,E as y,Q as pe,S as me,k as ve,j as ge,l as fe,T as he,U as ye,p as se,m as ne,D as le,z as Ve,H as we,O as $e,Z as Ce,_ as Te,$ as Ee,a0 as Ae,a1 as De,L as Ue,J as ce,a2 as Pe,N as Oe,K as Fe,a3 as Me,M as xe}from"./index-8a4876d8.js";import{_ as oe}from"./_plugin-vue_export-helper-c27b6911.js";const N=b=>(se("data-v-53888f26"),b=b(),ne(),b),Se={class:"add-plan-content"},je={class:"dynamic-fields-section"},Ne={class:"section-header"},Ie=N(()=>a("h3",null,"方案配置",-1)),Re=N(()=>a("i",{class:"jt-20-addition"},null,-1)),Xe={key:0,class:"fields-list"},Be={class:"field-header"},qe={class:"field-index"},Ye=N(()=>a("i",{class:"jt-20-remove"},null,-1)),ze={class:"field-config"},Le={class:"config-row"},Je=N(()=>a("label",{class:"config-label"},"字段类型：",-1)),He={style:{display:"flex","align-items":"center"}},Ke={class:"config-row"},Qe=N(()=>a("label",{class:"config-label"},"字段标题：",-1)),Ze={class:"config-row"},Ge=N(()=>a("label",{class:"config-label"},"字段值：",-1)),We={key:1,class:"empty-fields"},ea=N(()=>a("p",null,'还没有添加任何字段，点击"添加字段"开始配置方案内容',-1)),aa=[ea],la={class:"btns-group"},ta=N(()=>a("i",{class:"jt-20-ensure"},null,-1)),sa=N(()=>a("i",{class:"jt-20-delete"},null,-1)),na=te({__name:"AddDisposalPlan",props:{showDialog:{type:Boolean},currentMode:{},mediationCaseOptions:{},assetPackageOptions:{}},emits:["close","ensure"],setup(b,{emit:A}){const k=b,P=f(),V=f(!1),r=f({mediation_case:"",plan_name:"",asset_package:""}),p=f([]),q=[{label:"文本输入",value:m.TEXTAREA,icon:"jt-24-edit"},{label:"日期选择",value:m.DATE,icon:"jt-24-calendar"},{label:"金额输入",value:m.AMOUNT,icon:"jt-24-money"}],w=H(()=>k.currentMode||"mediationCases"),R=H(()=>"新增方案"),X=H(()=>({plan_name:[{required:!0,message:"请输入方案名称",trigger:"blur"}]}));ae(()=>k.showDialog,Y);function Y(l){l&&B()}function B(){w.value==="mediationCases"?r.value.mediation_case="":r.value.asset_package="",r.value.plan_name="",p.value=[]}function z(){A("close")}function L(){const l=Date.now().toString(36),i=Math.random().toString(36).substring(2,11);return`field_${l}_${i}`}function K(){const l={id:L(),title:"",type:m.TEXTAREA,value:"",required:!0};p.value.push(l)}function J(l){p.value.splice(l,1)}function O(l){l.value=Q(l.type||m.TEXT)}function Q(l){switch(l){case m.TEXT:case m.TEXTAREA:return"";case m.DATE:return"";case m.AMOUNT:return 0;default:return""}}function Z(){for(let l=0;l<p.value.length;l++){const i=p.value[l];if(!i.title.trim())return y.error(`第${l+1}个字段的标题不能为空`),!1;if(i.required&&(!i.value||String(i.value).trim()===""))return y.error(`${i.title}是必填字段，请填写内容`),!1}return!0}async function G(){if(P.value){V.value=!0;try{if(!await new Promise(C=>{P.value.validate(T=>{C(T)})})||!Z())return;if(p.value.length===0){y.error("请至少添加一个字段");return}const i={plan_name:r.value.plan_name,plan_config:p.value,...w.value==="asset"&&{asset_package:r.value.asset_package},...w.value==="mediationCases"&&{mediation_case:r.value.mediation_case}};A("ensure",i)}finally{V.value=!1}}}return(l,i)=>{const C=pe,T=me,S=ve,s=ge,c=fe,d=he,v=ye;return n(),h(re,{visible:l.showDialog,"onUpdate:visible":z,width:"1200px",title:R.value},{default:u(()=>[a("div",Se,[t(c,{ref_key:"planFormRef",ref:P,model:r.value,rules:X.value,"label-width":"110px"},{default:u(()=>[w.value==="asset"?(n(),h(S,{key:0,label:"资产包",prop:"asset_package"},{default:u(()=>[t(T,{modelValue:r.value.asset_package,"onUpdate:modelValue":i[0]||(i[0]=e=>r.value.asset_package=e),filterable:"",placeholder:"请选择资产包",style:{width:"100%"}},{default:u(()=>[(n(!0),g(U,null,M(l.assetPackageOptions,e=>(n(),h(C,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(n(),h(S,{key:1,label:"调解案件",prop:"mediation_case"},{default:u(()=>[t(T,{modelValue:r.value.mediation_case,"onUpdate:modelValue":i[1]||(i[1]=e=>r.value.mediation_case=e),filterable:"",placeholder:"请选择调解案件",style:{width:"100%"}},{default:u(()=>[(n(!0),g(U,null,M(l.mediationCaseOptions,e=>(n(),h(C,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),t(S,{label:"方案名称",prop:"plan_name"},{default:u(()=>[t(s,{modelValue:r.value.plan_name,"onUpdate:modelValue":i[2]||(i[2]=e=>r.value.plan_name=e),placeholder:"例如方案一、方案二"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),a("div",je,[a("div",Ne,[Ie,t(j,{onClick:K,height:34,"btn-type":"blue"},{default:u(()=>[Re,E("添加字段 ")]),_:1})]),p.value.length>0?(n(),g("div",Xe,[(n(!0),g(U,null,M(p.value,(e,D)=>(n(),g("div",{key:e.id||`field-${D}`,class:"field-item"},[a("div",Be,[a("span",qe,"字段 "+F(D+1),1),t(j,{onClick:o=>J(D),height:32,"btn-type":"red"},{default:u(()=>[Ye,E("删除 ")]),_:2},1032,["onClick"])]),a("div",ze,[a("div",Le,[Je,t(T,{modelValue:e.type,"onUpdate:modelValue":o=>e.type=o,onChange:o=>O(e),style:{width:"280px"}},{default:u(()=>[(n(),g(U,null,M(q,o=>t(C,{key:o.value,label:o.label,value:o.value},{default:u(()=>[a("span",He,[a("i",{class:_e(o.icon),style:{"margin-right":"8px"}},null,2),E(" "+F(o.label),1)])]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),a("div",Ke,[Qe,t(s,{modelValue:e.title,"onUpdate:modelValue":o=>e.title=o,placeholder:"请输入字段标题",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"])]),a("div",Ze,[Ge,e.type===x(m).TEXTAREA?(n(),h(s,{key:0,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,type:"textarea",rows:3,placeholder:"请输入文本内容",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"])):e.type===x(m).DATE?(n(),h(d,{key:1,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,type:"date",placeholder:"选择日期",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"])):e.type===x(m).AMOUNT?(n(),h(v,{key:2,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,min:0,precision:2,placeholder:"请输入金额",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"])):(n(),h(s,{key:3,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,type:"textarea",rows:3,placeholder:"请输入文本内容",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"]))])])]))),128))])):(n(),g("div",We,aa))]),a("div",la,[t(j,{onClick:G,loading:V.value,height:34,"btn-type":"blue"},{default:u(()=>[ta,E("确认 ")]),_:1},8,["loading"]),t(j,{onClick:z,height:34},{default:u(()=>[sa,E("取消 ")]),_:1})])])]),_:1},8,["visible","title"])}}});const oa=oe(na,[["__scopeId","data-v-53888f26"]]),I=b=>(se("data-v-71b33f57"),b=b(),ne(),b),ia={class:"edit-plan-content"},ua={class:"dynamic-fields-section"},da={class:"section-header"},ca=I(()=>a("h3",null,"字段配置",-1)),ra=I(()=>a("i",{class:"jt-20-addition"},null,-1)),_a={key:0,class:"fields-list"},pa={class:"field-header"},ma={class:"field-index"},va=I(()=>a("i",{class:"jt-20-remove"},null,-1)),ga={class:"field-config"},fa={class:"config-row"},ha=I(()=>a("label",{class:"config-label"},"字段类型：",-1)),ya={style:{display:"flex","align-items":"center"}},ba={class:"config-row"},ka=I(()=>a("label",{class:"config-label"},"字段标题：",-1)),Va={class:"field-preview"},wa=I(()=>a("label",{class:"config-label"}," 内容预览： ",-1)),$a={class:"preview-content"},Ca={key:1,class:"empty-fields"},Ta=I(()=>a("p",null,'还没有添加任何字段，点击"添加字段"开始配置方案内容',-1)),Ea=[Ta],Aa={class:"btns-group"},Da=I(()=>a("i",{class:"jt-20-ensure"},null,-1)),Ua=I(()=>a("i",{class:"jt-20-delete"},null,-1)),Pa=te({__name:"EditDisposalPlan",props:{showDialog:{type:Boolean},planData:{},currentMode:{},mediationCaseOptions:{},assetPackageOptions:{}},emits:["close","ensure"],setup(b,{emit:A}){const k=b,P=f(),V=f(!1),r=f({asset_package:null,mediation_case:null,plan_name:""}),p=f([]),q=[{label:"文本输入",value:m.TEXTAREA,icon:"jt-24-edit"},{label:"日期选择",value:m.DATE,icon:"jt-24-calendar"},{label:"金额输入",value:m.AMOUNT,icon:"jt-24-money"}],w=H(()=>k.currentMode||"mediationCases"),R=H(()=>w.value==="asset"?"编辑资产包方案":"编辑调解案件方案"),X=H(()=>({plan_name:[{required:!0,message:"请输入方案名称",trigger:"blur"}]}));ae(()=>k.showDialog,Y),ae(()=>k.planData,B,{deep:!0});function Y(l){l&&B(k.planData)}function B(l){l&&(w.value==="asset"?r.value.asset_package=l.asset_package:r.value.mediation_case=l.mediation_case,r.value.plan_name=l.plan_name,p.value=l.plan_config?JSON.parse(JSON.stringify(l.plan_config)).map(i=>(i.required=!0,(!i.id||typeof i.id!="string"||i.id.trim()==="")&&(i.id=L()),i)):[])}function z(){A("close")}function L(){const l=Date.now().toString(36),i=Math.random().toString(36).substring(2,11);return`field_${l}_${i}`}function K(){const l={id:L(),title:"",type:m.TEXTAREA,value:"",required:!0};p.value.push(l)}function J(l){p.value.splice(l,1)}function O(l){l.value=Q(l.type||m.TEXTAREA)}function Q(l){switch(l){case m.TEXTAREA:return"";case m.DATE:return"";case m.AMOUNT:return 0;default:return""}}function Z(){for(let l=0;l<p.value.length;l++)if(!p.value[l].title.trim())return y.error(`第${l+1}个字段的标题不能为空`),!1;return!0}async function G(){if(P.value){V.value=!0;try{if(!await new Promise(C=>{P.value.validate(T=>{C(T)})})||!Z())return;if(p.value.length===0){y.error("请至少保留一个字段");return}const i={id:k.planData.id,plan_name:r.value.plan_name,plan_config:p.value,...w.value==="asset"&&{asset_package:r.value.asset_package},...w.value==="mediationCases"&&{mediation_case:r.value.mediation_case}};console.log(i,"===编辑弹框保存参数"),A("ensure",i)}finally{V.value=!1}}}return(l,i)=>{const C=pe,T=me,S=ve,s=ge,c=fe,d=he,v=ye;return n(),h(re,{visible:l.showDialog,"onUpdate:visible":z,width:"1200px",title:R.value},{default:u(()=>[a("div",ia,[t(c,{ref_key:"planFormRef",ref:P,model:r.value,rules:X.value,"label-width":"110px"},{default:u(()=>[w.value==="asset"?(n(),h(S,{key:0,label:"资产包",prop:"asset_package"},{default:u(()=>[t(T,{modelValue:r.value.asset_package,"onUpdate:modelValue":i[0]||(i[0]=e=>r.value.asset_package=e),filterable:"",placeholder:"请选择资产包",style:{width:"100%"}},{default:u(()=>[(n(!0),g(U,null,M(l.assetPackageOptions,e=>(n(),h(C,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(n(),h(S,{key:1,label:"调解案件",prop:"mediation_case"},{default:u(()=>[t(T,{modelValue:r.value.mediation_case,"onUpdate:modelValue":i[1]||(i[1]=e=>r.value.mediation_case=e),filterable:"",placeholder:"请选择调解案件",style:{width:"100%"}},{default:u(()=>[(n(!0),g(U,null,M(l.mediationCaseOptions,e=>(n(),h(C,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),t(S,{label:"方案名称",prop:"plan_name"},{default:u(()=>[t(s,{modelValue:r.value.plan_name,"onUpdate:modelValue":i[2]||(i[2]=e=>r.value.plan_name=e),placeholder:"例如方案一、方案二",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),a("div",ua,[a("div",da,[ca,t(j,{onClick:K,height:34,"btn-type":"blue"},{default:u(()=>[ra,E("添加字段 ")]),_:1})]),p.value.length>0?(n(),g("div",_a,[(n(!0),g(U,null,M(p.value,(e,D)=>(n(),g("div",{key:e.id||`field-${D}`,class:"field-item"},[a("div",pa,[a("span",ma,"字段 "+F(D+1),1),t(j,{onClick:o=>J(D),height:32,"btn-type":"red"},{default:u(()=>[va,E("删除 ")]),_:2},1032,["onClick"])]),a("div",ga,[a("div",fa,[ha,t(T,{modelValue:e.type,"onUpdate:modelValue":o=>e.type=o,onChange:o=>O(e),style:{width:"280px"}},{default:u(()=>[(n(),g(U,null,M(q,o=>t(C,{key:o.value,label:o.label,value:o.value},{default:u(()=>[a("span",ya,[a("i",{class:_e(o.icon),style:{"margin-right":"8px"}},null,2),E(" "+F(o.label),1)])]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),a("div",ba,[ka,t(s,{modelValue:e.title,"onUpdate:modelValue":o=>e.title=o,placeholder:"请输入字段标题",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"])])]),a("div",Va,[wa,a("div",$a,[e.type===x(m).TEXTAREA?(n(),h(s,{key:0,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,type:"textarea",rows:3,placeholder:"请输入文本内容"},null,8,["modelValue","onUpdate:modelValue"])):e.type===x(m).DATE?(n(),h(d,{key:1,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue","onUpdate:modelValue"])):e.type===x(m).AMOUNT?(n(),h(v,{key:2,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,min:0,precision:2,placeholder:"请输入金额",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"])):le("",!0)])])]))),128))])):(n(),g("div",Ca,Ea))]),a("div",Aa,[t(j,{onClick:G,loading:V.value,height:34,"btn-type":"blue"},{default:u(()=>[Da,E("确认 ")]),_:1},8,["loading"]),t(j,{onClick:z,height:34},{default:u(()=>[Ua,E("取消 ")]),_:1})])])]),_:1},8,["visible","title"])}}});const Oa=oe(Pa,[["__scopeId","data-v-71b33f57"]]),Fa=b=>(se("data-v-f042492e"),b=b(),ne(),b),Ma={class:"plan-management"},xa={class:"search-header"},Sa=Fa(()=>a("i",{class:"jt-20-add"},null,-1)),ja={class:"fields-preview"},Na={class:"field-types"},Ia=["title"],Ra={key:0,class:"more-fields"},Xa={class:"operation-buttons"},Ba=["onClick"],qa=["onClick"],Ya={class:"fields-preview"},za={class:"field-types"},La=["title"],Ja={key:0,class:"more-fields"},Ha={class:"operation-buttons"},Ka=["onClick"],Qa=["onClick"],Za={class:"pagination-wrapper"},W=10,Ga=te({__name:"disposalPlan",setup(b){const A=f("asset"),k=f({id:"",title:"",fields:[],jsonData:"",caseStatus:"",approvalStatus:"",schemeStatus:""}),P=f(0),V=f(1),r=f(""),p=f(!1),q=f([]),w=f(!1),R=f(!1),X=f(!1),Y=f([]),B=f([]);async function z(){const{data:s}=await we({page:1,page_size:100}),{state:c,msg:d}=s;c==="success"?Y.value=s.data.results.map(v=>({label:v.package_name,value:v.id})):y.error(d)}async function L(){try{const{data:s}=await $e({page:1,page_size:100}),{state:c,msg:d}=s;c==="success"?B.value=s.data.results.map(v=>({label:v.case_number,value:v.mediation_plan_name})):y.error(d)}catch(s){console.error("获取调解案件选项失败:",s),y.error("获取调解案件选项失败")}}function K(s){A.value=s,V.value=1,O()}function J(){V.value=1,O()}async function O(){p.value=!0;const s={page:V.value,page_size:W,search:r.value},{data:c}=await Ce(s),{state:d,msg:v}=c;if(d==="success"){const{results:e,count:D}=c.data;q.value=e,P.value=D}else y.error(v);p.value=!1}function Q(s){V.value=s,O()}function Z(){w.value=!0}async function G(s){try{const c={plan_name:s.plan_name,plan_config:s.plan_config};s.asset_package&&(c.asset_package=s.asset_package),s.mediation_case&&(c.mediation_case=s.mediation_case),console.log("新增调解案件参数:",c);const{data:d}=await Te(c),{state:v,msg:e}=d;v==="success"?(y.success("新增成功"),w.value=!1,J()):y.error(e||"新增失败")}catch(c){console.error("新增调解案件失败:",c),y.error("新增失败")}}async function l(s,c){const{data:d}=await Pe(Number(s.id)),{state:v,msg:e}=d;v==="success"?k.value={...d.data}:y.error(e),R.value=!0}async function i(s){const{data:c}=await Ee(s,Number(s.id)),{state:d,msg:v}=c;d==="success"?(y.success(v),R.value=!1,O()):y.error(v||"编辑失败")}function C(s,c){k.value={...s},X.value=!0}async function T(){try{console.log("删除调解案件，ID:",k.value.id);const{data:s}=await Ae(Number(k.value.id)),{state:c,msg:d}=s;c==="success"?(y.success("删除成功"),X.value=!1,O()):y.error(d||"删除失败")}catch(s){console.error("删除调解案件失败:",s),y.error("删除失败")}}function S(){R.value=!1,k.value={id:"",title:"",fields:[],jsonData:"",caseStatus:"",approvalStatus:"",schemeStatus:""}}return Ve(()=>{O(),A.value==="asset"?z():L()}),(s,c)=>{const d=Oe,v=Fe,e=Me,D=De,o=Ue,ie=xe;return n(),g(U,null,[a("div",Ma,[a("div",xa,[t(be,{modelValue:r.value,"onUpdate:modelValue":c[0]||(c[0]=_=>r.value=_),placeholder:"搜索资产包名称",onClick:J},null,8,["modelValue"]),t(j,{onClick:Z,height:34},{default:u(()=>[Sa,E("新增方案")]),_:1})]),t(D,{modelValue:A.value,"onUpdate:modelValue":c[1]||(c[1]=_=>A.value=_),onTabChange:K,class:"plan-tabs"},{default:u(()=>[t(e,{label:"资产包",name:"asset",class:"asset-tab"},{default:u(()=>[a("div",null,[ce((n(),h(v,{data:q.value,border:"","cell-style":x(ue),"header-cell-style":x(de),class:"plan-table"},{default:u(()=>[t(d,{type:"index",label:"序号",width:"60",align:"center"},{default:u(({$index:_})=>[E(F(W*(V.value-1)+_+1),1)]),_:1}),t(d,{prop:"asset_package_name",label:"资产包名称",align:"center","min-width":"200"}),t(d,{prop:"plan_name",label:"方案名称",align:"center","min-width":"200"}),t(d,{label:"方案配置",align:"center","min-width":"220"},{default:u(({row:_})=>[a("div",ja,[a("div",Na,[(n(!0),g(U,null,M(_.plan_config&&Array.isArray(_.plan_config)?_.plan_config.slice(0,4):[],$=>(n(),g("span",{key:$.id||$.title||Math.random(),class:"field-type-tag",title:$.title},F($.title),9,Ia))),128)),_.plan_config&&Array.isArray(_.plan_config)&&_.plan_config.length>4?(n(),g("span",Ra," +"+F(_.plan_config.length-4),1)):le("",!0)])])]),_:1}),t(d,{prop:"plan_status_cn",label:"方案状态",align:"center","min-width":"100"}),t(d,{label:"操作",align:"center",width:"250"},{default:u(({row:_,$index:$})=>[a("div",Xa,[a("div",{onClick:ee=>l(_,$),class:"operation-btn edit-btn"},"编辑",8,Ba),a("div",{onClick:ee=>C(_,$),class:"operation-btn delete-btn"},"删除",8,qa)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[ie,p.value]])])]),_:1}),t(e,{label:"调解案件",name:"mediationCases",class:"mediation-cases-tab"},{default:u(()=>[a("div",null,[ce((n(),h(v,{data:q.value,border:"","cell-style":x(ue),"header-cell-style":x(de),class:"plan-table"},{default:u(()=>[t(d,{type:"index",label:"序号",width:"60",align:"center"},{default:u(({$index:_})=>[E(F(W*(V.value-1)+_+1),1)]),_:1}),t(d,{prop:"mediation_case_number",label:"调解案件号",align:"center",width:"180"}),t(d,{prop:"mediation_case_status",label:"案件状态",align:"center","min-width":"100"}),t(d,{prop:"plan_name",label:"方案名称",align:"center","min-width":"180"}),t(d,{label:"方案配置",align:"center","min-width":"220"},{default:u(({row:_})=>[a("div",Ya,[a("div",za,[(n(!0),g(U,null,M(_.plan_config&&Array.isArray(_.plan_config)?_.plan_config.slice(0,4):[],$=>(n(),g("span",{key:$.id||$.title||Math.random(),class:"field-type-tag",title:$.title},F($.title),9,La))),128)),_.plan_config&&Array.isArray(_.plan_config)&&_.plan_config.length>4?(n(),g("span",Ja," +"+F(_.plan_config.length-4),1)):le("",!0)])])]),_:1}),t(d,{prop:"approval_status_cn",label:"审批状态",align:"center","min-width":"100"}),t(d,{prop:"plan_status_cn",label:"方案状态",align:"center","min-width":"100"}),t(d,{label:"操作",align:"center",width:"250"},{default:u(({row:_,$index:$})=>[a("div",Ha,[a("div",{onClick:ee=>l(_,$),class:"operation-btn edit-btn"},"编辑",8,Ka),a("div",{onClick:ee=>C(_,$),class:"operation-btn delete-btn"},"删除",8,Qa)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[ie,p.value]])])]),_:1})]),_:1},8,["modelValue"]),a("div",Za,[t(o,{background:"",layout:"prev, pager, next",total:P.value,"current-page":V.value,"page-size":W,onCurrentChange:Q},null,8,["total","current-page"])])]),t(oa,{"show-dialog":w.value,"current-mode":A.value,mediationCaseOptions:B.value,assetPackageOptions:Y.value,onClose:c[2]||(c[2]=_=>w.value=!1),onEnsure:G},null,8,["show-dialog","current-mode","mediationCaseOptions","assetPackageOptions"]),t(Oa,{"show-dialog":R.value,"plan-data":k.value,"current-mode":A.value,mediationCaseOptions:B.value,assetPackageOptions:Y.value,onClose:S,onEnsure:i},null,8,["show-dialog","plan-data","current-mode","mediationCaseOptions","assetPackageOptions"]),t(ke,{"show-dialog":X.value,onEnsure:T,onClose:c[3]||(c[3]=_=>X.value=!1)},null,8,["show-dialog"])],64)}}});const _l=oe(Ga,[["__scopeId","data-v-f042492e"]]);export{_l as default};
