<script lang="ts" setup>
import { onMounted, ref, type Ref } from 'vue'
import CustomButton from '@/components/common/CustomButton.vue';
import CustomInput from '@/components/common/CustomInput.vue';
import AddPlan from '../../dialogs/AddDisposalPlan.vue';
import EditPlan from '../../dialogs/EditDisposalPlan.vue';
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue';
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import type { Plan, PlanParams, AddPlanParams, EditPlanParams } from './type';
import { getPlan, editPlan, addPlan, deletePlan } from '@/axios/system'
import { ElMessage } from 'element-plus';

// 选中的方案行数据
const selectRow: Ref<Plan> = ref({
  id: 0,
  title: '',
  fields: [],
  jsonData: ''
})

// 分页和搜索
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const planList = ref<Plan[]>([])

// 对话框控制
const showAdd = ref(false)
const showEdit = ref(false)
const showDelete = ref(false)

// 重置到第一页并搜索
function setPage1() {
  page.value = 1
  searchPlanList()
}

// 搜索方案列表
async function searchPlanList() {
  const params: PlanParams = {
    ordering: 'id',
    search: search.value,
    page: page.value,
    page_size
  }
  
  try {
    const { data } = await getPlan(params)
    const { state, msg } = data
    if(state === 'success') {
      planList.value = data.data.results
      total.value = data.data.count
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    ElMessage.error('获取方案列表失败')
  }
}

// 分页改变
function pageChanged(p: number) {
  page.value = p
  searchPlanList()
}

// 打开新增方案对话框
function openAddPlanDialog() {
  showAdd.value = true 
}

// 提交新增方案
async function submitAddPlan(params: AddPlanParams) {
  try {
    const { data } = await addPlan(params)
    const { state, msg } = data
    if(state === 'success') {
      ElMessage.success(msg || '新增方案成功')
      showAdd.value = false
      setPage1()
    } else {
      ElMessage.error(msg || '新增方案失败')
    }
  } catch (error) {
    ElMessage.error('新增方案失败')
  }
}

// 打开编辑方案对话框
function openEditPlanDialog(row: Plan, index: number) {
  selectRow.value = { ...row }
  showEdit.value = true
}

// 提交编辑方案
async function submitEditPlan(params: EditPlanParams) {
  try {
    const { data } = await editPlan(params, params.id)
    const { state, msg } = data
    if(state === 'success') {
      ElMessage.success(msg || '编辑方案成功')
      showEdit.value = false
      searchPlanList()
    } else {
      ElMessage.error(msg || '编辑方案失败')
    }
  } catch (error) {
    ElMessage.error('编辑方案失败')
  }
}

// 打开删除确认对话框
function openEnsureDeleteDialog(row: Plan, index: number) {
  selectRow.value = { ...row }
  showDelete.value = true
}

// 删除方案
async function deletePlanRow() {
  if (!selectRow.value.id) return
  
  try {
    const { data } = await deletePlan(selectRow.value.id)
    const { state, msg } = data
    if(state === 'success') {
      ElMessage.success(msg || '删除方案成功')
      showDelete.value = false
      searchPlanList()
    } else {
      ElMessage.error(msg || '删除方案失败')
    }
  } catch (error) {
    ElMessage.error('删除方案失败')
  }
}

// 关闭编辑对话框
function closeEditDialog() {
  showEdit.value = false
  selectRow.value = {
    id: 0,
    title: '',
    fields: [],
    jsonData: ''
  }
}

// 格式化显示动态字段概览
function formatFieldsPreview(fields: any[]): string {
  if (!fields || fields.length === 0) return '无字段'
  return fields.map(field => field.title).slice(0, 3).join('、') + 
         (fields.length > 3 ? '...' : '')
}

// 组件挂载时获取方案列表
onMounted(() => {
  searchPlanList()
})
</script>

<template>
  <div class="plan-management">
    <div class="search-header">
      <CustomInput v-model="search" placeholder="搜索方案名称" @click="setPage1"></CustomInput>
      <CustomButton @click="openAddPlanDialog" :height="34">
        <i class="jt-20-add"></i>新增方案
      </CustomButton>
    </div>
    
    <div>
      <el-table
        :data="planList"
        border
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        
        <!-- 序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center">
          <template v-slot="{$index}">
            {{page_size * (page - 1) + $index + 1}}
          </template>
        </el-table-column>
		<el-table-column prop="id" label="调解案件号" align="center" min-width="80"></el-table-column>
        
        <!-- 方案标题 -->
        <el-table-column prop="title" label="方案名称" align="center" min-width="150"></el-table-column>
        
        <!-- 字段概览 -->
        <el-table-column label="包含字段" align="center" min-width="200">
          <template v-slot="{row}">
            <span class="fields-preview">{{ formatFieldsPreview(row.fields) }}</span>
          </template>
        </el-table-column>
        
        <!-- 字段数量 -->
        <el-table-column label="字段数量" align="center" width="100">
          <template v-slot="{row}">
            <el-tag size="small" type="info">{{ row.fields?.length || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <!-- 创建时间 -->
        <el-table-column prop="createTime" label="创建时间" align="center" width="150">
          <template v-slot="{row}">
            {{ row.createTime ? new Date(row.createTime).toLocaleDateString() : '-' }}
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column label="操作" align="center" width="200">
          <template v-slot="{row, $index}">
            <div style="display: flex; justify-content: center; gap: 14px;">
              <div @click="openEditPlanDialog(row, $index)" class="operation-btn">编辑</div>
              <div @click="openEnsureDeleteDialog(row, $index)" class="operation-btn">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <el-pagination
        class="pagi"
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="page"
        :page-size="page_size"
        @current-change="pageChanged"
      ></el-pagination>
    </div>
  </div>

  <!-- 新增方案对话框 -->
  <AddPlan 
    :show-dialog="showAdd" 
    @close="showAdd = false" 
    @ensure="submitAddPlan">
  </AddPlan>
  
  <!-- 编辑方案对话框 -->
  <EditPlan 
    :show-dialog="showEdit" 
    :plan-data="selectRow"
    @close="closeEditDialog" 
    @ensure="submitEditPlan">
  </EditPlan>
  
  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDelete"
    title="删除调解方案"
    :message="`确认删除选中的调解方案吗？此操作不可撤销。`"
    confirm-text="确认"
    cancel-text="取消"
    @update:visible="showDelete = $event"
    @confirm="deletePlanRow"
    @cancel="showDelete = false" />
</template>

<style lang="scss" scoped>
.plan-management {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  
  .search-header {
    display: grid;
    grid-template-columns: 300px 120px;
    gap: 20px;
    margin-bottom: 20px;
  }
}

.fields-preview {
  color: #666;
  font-size: 14px;
}

.pagi {
  margin-top: 20px;
  text-align: center;
}
</style>