import{C as u}from"./CustomButton-ea16d5c5.js";import{_ as p}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{a as d,c as m,o as f,q as g,w as s,n as h,g as t,h as l,i as c}from"./index-8a4876d8.js";var E=(e=>(e.TEXT="text",e.TEXTAREA="textarea",e.DATE="date",e.AMOUNT="amount",e.FILE="file",e))(E||{}),x=(e=>(e.PENDING="pending",e.PROCESSING="processing",e.MEDIATING="mediating",e.COMPLETED="completed",e.CLOSED="closed",e))(x||{});const v=t("div",{style:{display:"flex","flex-direction":"column",gap:"16px","align-items":"center"}},[t("i",{class:"jt-60-delete"}),t("span",null,"确认删除？")],-1),D={class:"btns-group"},b=t("i",{class:"jt-20-ensure"},null,-1),N=t("i",{class:"jt-20-delete"},null,-1),A=d({__name:"DeleteTips",props:{showDialog:{type:Boolean}},emits:["ensure","close"],setup(e,{emit:o}){const r=e;let n=m({get(){return r.showDialog},set(){o("close")}});function i(){n.value=!1}function _(){o("ensure")}return(C,T)=>{const a=u;return f(),g(p,{title:"删除",visible:h(n),width:"300px",markclose:!0,"onUpdate:visible":i},{default:s(()=>[v,t("div",D,[l(a,{onClick:_,height:34,"btn-type":"blue"},{default:s(()=>[b,c("确认")]),_:1}),l(a,{onClick:i,height:34},{default:s(()=>[N,c("取消")]),_:1})])]),_:1},8,["visible"])}}});export{x as C,E as F,A as _};
