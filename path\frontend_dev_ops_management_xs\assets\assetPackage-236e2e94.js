import{_ as r}from"./construct-a2f67563.js";import{b as _}from"./system-846db6a5.js";import{E as m}from"./index-efa25d88.js";import{d as p,k as i,o as d,c as g,p as l,g as u,b as a}from"./index-d5da4504.js";import{_ as f}from"./_plugin-vue_export-helper-c27b6911.js";const k=s=>(l("data-v-c267c865"),s=s(),u(),s),I={class:"outbound-e-seal"},h=k(()=>a("div",{class:"no_content"},[a("img",{src:r,alt:"construct"}),a("div",null,"功能建设中...")],-1)),S=[h],v=p({__name:"assetPackage",setup(s){async function n(){const{data:t}=await _(),{state:o,msg:c,data:e}=t;o==="success"?(console.log(t,"===data",o),sessionStorage.setItem("access_token",e.access_token),sessionStorage.setItem("refresh_token",e.refresh_token),sessionStorage.setItem("token_type",e.token_type),sessionStorage.setItem("username",e.username),sessionStorage.setItem("group_name",e.group_name),sessionStorage.setItem("role_name",e.role_name)):m.error(c)}return i(()=>{n()}),(t,o)=>(d(),g("div",I,S))}});const P=f(v,[["__scopeId","data-v-c267c865"]]);export{P as default};
