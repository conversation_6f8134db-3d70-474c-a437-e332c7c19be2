.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  .operation-btn {
    padding: 0px 12px;
    height: 28px;
    line-height: 28px;
    // border: 1px solid #b3d8ff;
    // border-radius:2px;
    color: #409eff;
    font-size: 16px;
    cursor: pointer;
    // background-color:#ecf5ff;
    &.edit-btn, &.move-up-btn, &.move-down-btn, &.preview-btn {
      color: #0068BE;
    }
    
    &.delete-btn {
      color: #D94223;
    }
  }
}
.el-table {
  border-radius: 4px;
  .el-table__inner-wrapper {
    &::before {
      background-color: #c6daf2 !important;
    }
  }
  .el-table--border::after {
    background-color: #c6daf2 !important;
  }
  .el-table__border-left-patch {
    background-color: #c6daf2 !important;
  }
}


// 响应式适配
@media (max-width: 1200px) {
  .operation-buttons {
    display: flex; 
    justify-content: center; 
    gap: 8px;	

    .operation-btn {
      font-size: 11px;
    }
  }
}