/* empty css             */import{c as Ve,h as $e,C as je}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{C as J}from"./CustomButton-ea16d5c5.js";/* empty css                        *//* empty css                       *//* empty css                  *//* empty css                     */import{_ as be}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{a as pe,r as d,c as ce,s as re,o as n,q as N,w as r,g as t,h as o,f as m,A as q,F as j,J as Ce,n as H,D as ie,i as U,E as V,P as Pe,Q as ye,S as ke,N as Ee,K as De,L as Se,M as Fe,p as ve,m as me,H as Ye,t as B,B as Te,j as Ae,k as Ue,l as Ne,T as Le,U as xe,V as Je,z as ze,O as Xe,W as Be,X as qe,Y as Ge}from"./index-8a4876d8.js";import{_ as fe}from"./_plugin-vue_export-helper-c27b6911.js";import{F as y,C as te,_ as Ke}from"./DeleteTips.vue_vue_type_style_index_0_lang-58486e4e.js";const he=D=>(ve("data-v-099c5b27"),D=D(),me(),D),He={class:"asset-selector-content"},We={class:"file-selection-area"},Ze={class:"selection-row"},Qe=he(()=>t("label",{class:"selection-label"},"资产包名称：",-1)),et={key:0,class:"table-area"},tt={key:0,class:"pagination-wrapper"},at={class:"dialog-footer"},lt=he(()=>t("i",{class:"jt-20-ensure"},null,-1)),st=he(()=>t("i",{class:"jt-20-delete"},null,-1)),nt={key:1,class:"empty-state"},ot=he(()=>t("p",null,"请先选择资产包名称查看数据",-1)),it=[ot],ct=pe({__name:"AssetPackageSelector",props:{visible:{type:Boolean}},emits:["update:visible","confirm"],setup(D,{emit:M}){const z=D;d(!1);const L=d(!1),F=d(""),P=d(""),s=d(null),T=d([]),v=d([]),A=d(1),k=d(10),Y=d(0),O=d([]),Z=d([]);async function oe(){try{const{data:c}=await Ye({page:1,page_size:100}),{state:S,msg:b}=c;S==="success"?(Z.value=c.data.results,O.value=c.data.results.map(f=>({id:f.id.toString(),name:f.package_name}))):V.error(b)}catch(c){console.error("获取资产包列表失败:",c),V.error("获取资产包列表失败")}}const ae=ce(()=>P.value.trim()?O.value.filter(c=>c.name.toLowerCase().includes(P.value.toLowerCase())):O.value),Q=ce(()=>{const c=(A.value-1)*k.value,S=c+k.value;return T.value.slice(c,S)});re(F,async c=>{c?await se(c):K()}),re(()=>z.visible,c=>{c?oe():le()});function le(){F.value="",P.value="",s.value=null,K()}function K(){T.value=[],v.value=[],A.value=1,Y.value=0}async function se(c){L.value=!0;const S=O.value.find(f=>f.name===c);if(!S){V.error("未找到对应的资产包");return}const b=await Pe(Number(S.id));if(b.data.code===200){const f=b.data.data;f.columns&&Array.isArray(f.columns)?v.value=f.columns.map(I=>({prop:I.key,label:I.label||I.key,width:I.width||0})):v.value=[],f.data&&Array.isArray(f.data)?(T.value=f.data,Y.value=f.data.length):(T.value=[],Y.value=0),A.value=1,s.value=null}else V.error(b.data.msg||"数据加载失败"),K();L.value=!1}function ne(c){s.value=c}function C(c){return s.value===c}function E(c){A.value=c}function $(){M("update:visible",!1)}function x(){if(!F.value){V.error("请选择资产包名称");return}if(!s.value){V.error("请选择表格中的一行数据");return}const c=Z.value.find(I=>I.package_name===F.value),S=Q.value.findIndex(I=>I===s.value),b=(A.value-1)*k.value+S+1,f={fileName:F.value,selectedRow:s.value,headers:v.value,asset_package:(c==null?void 0:c.id)||null,creditor_name:(c==null?void 0:c.creditor_name)||"",creditor:(c==null?void 0:c.creditor)||null,asset_package_row_number:b};console.log("选择结果:",f),M("confirm",f),M("update:visible",!1)}return(c,S)=>{const b=ye,f=ke,I=Ee,ue=De,a=Se,u=Fe;return n(),N(be,{visible:c.visible,"onUpdate:visible":$,width:"1200px",title:"选择资产信息"},{default:r(()=>[t("div",He,[t("div",We,[t("div",Ze,[Qe,o(f,{modelValue:F.value,"onUpdate:modelValue":S[0]||(S[0]=p=>F.value=p),filterable:"",placeholder:"请选择资产包名称",style:{width:"400px"}},{default:r(()=>[(n(!0),m(j,null,q(ae.value,p=>(n(),N(b,{key:p.id,label:p.name,value:p.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),F.value?(n(),m("div",et,[Ce((n(),N(ue,{data:Q.value,border:"","highlight-current-row":"","cell-style":H(Ve),"header-cell-style":H($e),onRowClick:ne,style:{width:"100%","margin-bottom":"20px"},"row-class-name":({row:p})=>C(p)?"selected-row":""},{default:r(()=>[o(I,{type:"index",label:"序号",index:p=>(A.value-1)*k.value+p+1,width:"85",align:"center"},null,8,["index"]),(n(!0),m(j,null,q(v.value,p=>(n(),N(I,{key:p.prop,prop:p.prop,label:p.label,width:p.width,align:"center"},null,8,["prop","label","width"]))),128))]),_:1},8,["data","cell-style","header-cell-style","row-class-name"])),[[u,L.value]]),Y.value>k.value?(n(),m("div",tt,[o(a,{background:"",layout:"prev, pager, next",total:Y.value,"current-page":A.value,"page-size":k.value,onCurrentChange:E},null,8,["total","current-page","page-size"])])):ie("",!0),t("div",at,[o(J,{onClick:x,height:34,"btn-type":"blue"},{default:r(()=>[lt,U("确认 ")]),_:1}),o(J,{onClick:$,height:34},{default:r(()=>[st,U("取消 ")]),_:1})])])):(n(),m("div",nt,it))])]),_:1},8,["visible"])}}});const Re=fe(ct,[["__scopeId","data-v-099c5b27"]]),G=D=>(ve("data-v-f1f64fad"),D=D(),me(),D),ut={class:"add-plan-content"},rt={class:"fixed-fields-section"},dt={class:"fixed-fields-content"},_t={class:"field-group"},pt=G(()=>t("label",{class:"field-label"},"相关文件",-1)),vt={class:"file-upload-section"},mt=G(()=>t("i",{class:"jt-20-upload"},null,-1)),ft={key:0,class:"file-list"},ht=["title"],gt=["onClick"],bt={class:"dynamic-fields-section"},yt={class:"section-header"},kt=G(()=>t("h3",null,"调解信息配置",-1)),wt=G(()=>t("i",{class:"jt-20-addition"},null,-1)),Vt={key:0,class:"fields-list"},$t={class:"field-header"},Ct={class:"field-index"},Et=G(()=>t("i",{class:"jt-20-remove"},null,-1)),Dt={class:"field-config"},St={class:"config-row"},Ft=G(()=>t("label",{class:"config-label"},"字段类型：",-1)),Tt={style:{display:"flex","align-items":"center"}},At={class:"config-row"},Ut=G(()=>t("label",{class:"config-label"},"字段标题：",-1)),Nt={class:"field-preview"},Lt=G(()=>t("label",{class:"config-label"},"内容预览：",-1)),xt={class:"preview-content"},Rt={key:1,class:"empty-fields"},Mt=G(()=>t("p",null,'还没有添加任何字段，点击"添加字段"开始配置方案内容',-1)),Ot=[Mt],It={class:"btns-group"},jt=G(()=>t("i",{class:"jt-20-ensure"},null,-1)),Pt=G(()=>t("i",{class:"jt-20-delete"},null,-1)),Yt=pe({__name:"AddMediationInformation",props:{showDialog:{type:Boolean},mode:{}},emits:["close","ensure"],setup(D,{emit:M}){const z=D,L=d(),F=d(!1),P=d(!1),s=d({asset_package_name:"",asset_package_id:null,asset_package_row_number:null,debtor:"",creditor:null,creditor_name:""}),T=d([]),v=d([]),A=[{label:"文本输入",value:y.TEXTAREA,icon:"jt-24-edit"},{label:"日期选择",value:y.DATE,icon:"jt-24-calendar"},{label:"金额输入",value:y.AMOUNT,icon:"jt-24-money"}],k=ce(()=>{const a=`asset_data_${s.value.asset_package_name}`,u=localStorage.getItem(a);try{return u?JSON.parse(u):{}}catch{return{}}}),Y=ce(()=>{const a=k.value;return Object.keys(a).map(u=>({label:u,value:u}))}),O={asset_package_name:[{required:!0,message:"请选择资产包名称",trigger:"change"}]};re(()=>z.showDialog,Z);function Z(a){a&&oe()}function oe(){s.value.asset_package_name="",s.value.asset_package_id=null,s.value.asset_package_row_number=null,s.value.debtor="",s.value.creditor=null,s.value.creditor_name="",T.value=[],v.value=[]}function ae(){M("close")}function Q(){P.value=!0}function le(a){console.log(a,"====result"),s.value.asset_package_name=a.fileName,s.value.asset_package_id=a.asset_package,s.value.asset_package_row_number=a.asset_package_row_number,s.value.creditor=a.creditor,s.value.creditor_name=a.creditor_name,console.log("接收到的资产包选择结果:",a);const u={};a.headers.forEach(R=>{const ee=a.selectedRow[R.prop];ee!=null&&(u[R.label]=ee)});const p=`asset_data_${a.fileName}`;localStorage.setItem(p,JSON.stringify(u)),se()}function K(){return"GZTJ"+Date.now()+"000"+Math.random().toString(36).substr(2,9)}function se(){const a={id:K(),title:"",type:y.TEXTAREA,value:"",required:!1,isValueLocked:!1};v.value.push(a)}function ne(a){v.value.splice(a,1)}function C(a){a.value=c(a.type),a.isValueLocked=!1,a.title=""}function E(a){return a.type===y.TEXTAREA&&s.value.asset_package_name!==""&&Y.value.length>0}function $(a,u){a.title=u;const p=k.value[u];p!=null?(a.value=p,a.isValueLocked=!0):(a.value="",a.isValueLocked=!1)}function x(a){return a.isValueLocked||!1}function c(a){switch(a){case y.TEXTAREA:return"";case y.DATE:return"";case y.AMOUNT:return 0;default:return""}}function S(a){const u=a.target,p=u.files;!p||p.length===0||(Array.from(p).forEach(R=>{const ee={id:K(),name:R.name,size:R.size,type:R.type,url:URL.createObjectURL(R),file:R,status:"success"};T.value.push(ee)}),u.value="")}function b(){const a=document.createElement("input");a.type="file",a.multiple=!0,a.accept="*",a.style.display="none",a.addEventListener("change",S),document.body.appendChild(a),a.click(),document.body.removeChild(a)}function f(a){const u=T.value[a];u.url&&URL.revokeObjectURL(u.url),T.value.splice(a,1)}function I(){for(let a=0;a<v.value.length;a++){const u=v.value[a];if(!u.title.trim())return V.error(`第${a+1}个字段的标题不能为空`),!1;if(u.required&&(!u.value||String(u.value).trim()===""))return V.error(`${u.title}是必填字段，请填写内容`),!1}return!0}async function ue(){if(L.value){F.value=!0;try{if(!await new Promise(p=>{L.value.validate(R=>{p(R)})})||!I())return;if(v.value.length===0){V.error("请至少添加一个字段");return}const u={asset_package_name:s.value.asset_package_name,asset_package_id:s.value.asset_package_id,asset_package_row_number:s.value.asset_package_row_number,debtor:s.value.debtor,creditor:s.value.creditor,creditor_name:s.value.creditor_name,mediation_config:v.value,file:T.value};M("ensure",u)}finally{F.value=!1}}}return(a,u)=>{const p=Ae,R=Ue,ee=Ne,de=ye,e=ke,i=Le,h=xe;return n(),m(j,null,[o(be,{visible:a.showDialog,"onUpdate:visible":ae,width:"1200px",title:"新增调解信息"},{default:r(()=>[t("div",ut,[o(ee,{ref_key:"planFormRef",ref:L,model:s.value,rules:O,"label-width":"110px"},{default:r(()=>[o(R,{label:"资产包名称",prop:"asset_package_name"},{default:r(()=>[o(p,{modelValue:s.value.asset_package_name,"onUpdate:modelValue":u[0]||(u[0]=l=>s.value.asset_package_name=l),readonly:"",placeholder:"请点击选择资产包名称",onClick:Q,style:{cursor:"pointer"}},null,8,["modelValue"])]),_:1}),o(R,{label:"债权人",prop:"creditor_name"},{default:r(()=>[o(p,{modelValue:s.value.creditor_name,"onUpdate:modelValue":u[1]||(u[1]=l=>s.value.creditor_name=l),disabled:""},null,8,["modelValue"])]),_:1}),o(R,{label:"债务人",prop:"debtor"},{default:r(()=>[o(p,{modelValue:s.value.debtor,"onUpdate:modelValue":u[2]||(u[2]=l=>s.value.debtor=l),placeholder:"请输入债务人姓名",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),t("div",rt,[t("div",dt,[t("div",_t,[pt,t("div",vt,[o(J,{onClick:b,height:32},{default:r(()=>[mt,U("选择文件 ")]),_:1}),T.value.length>0?(n(),m("div",ft,[(n(!0),m(j,null,q(T.value,(l,X)=>(n(),m("div",{key:l.id,class:"file-item"},[t("span",{class:"file-name",title:l.name},B(l.name),9,ht),t("i",{class:"jt-20-delete file-remove",onClick:g=>f(X),title:"删除文件"},null,8,gt)]))),128))])):ie("",!0)])])])]),t("div",bt,[t("div",yt,[kt,o(J,{onClick:se,height:34,"btn-type":"blue"},{default:r(()=>[wt,U("添加字段 ")]),_:1})]),v.value.length>0?(n(),m("div",Vt,[(n(!0),m(j,null,q(v.value,(l,X)=>(n(),m("div",{key:l.id,class:"field-item"},[t("div",$t,[t("span",Ct,"字段 "+B(X+1),1),o(J,{onClick:g=>ne(X),height:32,"btn-type":"red"},{default:r(()=>[Et,U("删除 ")]),_:2},1032,["onClick"])]),t("div",Dt,[t("div",St,[Ft,o(e,{modelValue:l.type,"onUpdate:modelValue":g=>l.type=g,onChange:g=>C(l),style:{width:"280px"}},{default:r(()=>[(n(),m(j,null,q(A,g=>o(de,{key:g.value,label:g.label,value:g.value},{default:r(()=>[t("span",Tt,[t("i",{class:Te(g.icon),style:{"margin-right":"8px"}},null,2),U(" "+B(g.label),1)])]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),t("div",At,[Ut,E(l)?(n(),N(e,{key:0,modelValue:l.title,"onUpdate:modelValue":g=>l.title=g,onChange:g=>$(l,g),"allow-create":"",filterable:"",placeholder:"选择或输入字段标题",style:{width:"280px"}},{default:r(()=>[(n(!0),m(j,null,q(Y.value,g=>(n(),N(de,{key:g.value,label:g.label,value:g.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):(n(),N(p,{key:1,modelValue:l.title,"onUpdate:modelValue":g=>l.title=g,placeholder:"请输入字段标题",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"]))])]),t("div",Nt,[Lt,t("div",xt,[l.type===H(y).TEXTAREA?(n(),N(p,{key:0,modelValue:l.value,"onUpdate:modelValue":g=>l.value=g,type:"textarea",rows:3,disabled:x(l),placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue","disabled"])):l.type===H(y).DATE?(n(),N(i,{key:1,modelValue:l.value,"onUpdate:modelValue":g=>l.value=g,type:"date",disabled:x(l),placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue","onUpdate:modelValue","disabled"])):l.type===H(y).AMOUNT?(n(),N(h,{key:2,modelValue:l.value,"onUpdate:modelValue":g=>l.value=g,min:0,precision:2,disabled:x(l),placeholder:"请输入金额",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue","disabled"])):ie("",!0)])])]))),128))])):(n(),m("div",Rt,Ot))]),t("div",It,[o(J,{onClick:ue,loading:F.value,height:34,"btn-type":"blue"},{default:r(()=>[jt,U("确认 ")]),_:1},8,["loading"]),o(J,{onClick:ae,height:34},{default:r(()=>[Pt,U("取消 ")]),_:1})])])]),_:1},8,["visible"]),o(Re,{visible:P.value,"onUpdate:visible":u[3]||(u[3]=l=>P.value=!1),onConfirm:le},null,8,["visible"])],64)}}});const Jt=fe(Yt,[["__scopeId","data-v-f1f64fad"]]),W=D=>(ve("data-v-af4c09ed"),D=D(),me(),D),zt={class:"edit-plan-content"},Xt={class:"fixed-fields-section"},Bt={class:"fixed-fields-content"},qt={class:"field-group"},Gt=W(()=>t("label",{class:"field-label"},"相关文件",-1)),Kt={class:"file-upload-section"},Ht=W(()=>t("i",{class:"jt-20-upload"},null,-1)),Wt={key:0,class:"file-list"},Zt=["title"],Qt=["onClick"],ea={class:"dynamic-fields-section"},ta={class:"section-header"},aa=W(()=>t("h3",null,"调解信息配置",-1)),la=W(()=>t("i",{class:"jt-20-addition"},null,-1)),sa={key:0,class:"fields-list"},na={class:"field-header"},oa={class:"field-index"},ia=W(()=>t("i",{class:"jt-20-remove"},null,-1)),ca={class:"field-config"},ua={class:"config-row"},ra=W(()=>t("label",{class:"config-label"},"字段类型：",-1)),da={style:{display:"flex","align-items":"center"}},_a={class:"config-row"},pa=W(()=>t("label",{class:"config-label"},"字段标题：",-1)),va={class:"field-preview"},ma={class:"config-label"},fa={class:"preview-content"},ha={key:1,class:"empty-fields"},ga=W(()=>t("p",null,'还没有添加任何字段，点击"添加字段"开始配置方案内容',-1)),ba=[ga],ya={class:"btns-group"},ka=W(()=>t("i",{class:"jt-20-ensure"},null,-1)),wa=W(()=>t("i",{class:"jt-20-delete"},null,-1)),Va=pe({__name:"EditMediationInformation",props:{showDialog:{type:Boolean},planData:{}},emits:["close","ensure"],setup(D,{emit:M}){const z=D,L=d(),F=d(!1),P=d(!1),s=d({title:""}),T=d(te.PENDING),v=d([]),A=d(""),k=d([]);te.PENDING,te.PROCESSING,te.MEDIATING,te.COMPLETED,te.CLOSED;const Y=[{label:"多行文本",value:y.TEXTAREA,icon:"jt-24-edit"},{label:"日期选择",value:y.DATE,icon:"jt-24-calendar"},{label:"金额输入",value:y.AMOUNT,icon:"jt-24-money"}],O=ce(()=>{const e=`asset_data_${s.value.title}`,i=localStorage.getItem(e);try{return i?JSON.parse(i):{}}catch{return{}}}),Z=ce(()=>{const e=O.value;return Object.keys(e).map(i=>({label:i,value:i}))}),oe={title:[{required:!0,message:"请选择资产包名称",trigger:"change"}]};re(()=>z.showDialog,ae),re(()=>z.planData,Q,{deep:!0});function ae(e){e&&Q(z.planData)}function Q(e){e&&(s.value.title=e.title||"",T.value=e.caseStatus||te.PENDING,v.value=e.fileList?JSON.parse(JSON.stringify(e.fileList)).map(i=>({...i,status:"success",isServerFile:!0})):[],A.value=e.remark||"",k.value=e.fields?JSON.parse(JSON.stringify(e.fields)).map(i=>(i.required=!0,i)):[])}function le(){M("close")}function K(){P.value=!0}function se(e){s.value.title=e.fileName;const i={};e.headers.forEach(l=>{const X=e.selectedRow[l.prop];X!=null&&(i[l.label]=X)});const h=`asset_data_${e.fileName}`;localStorage.setItem(h,JSON.stringify(i)),V.success("资产包信息已更新并保存")}function ne(){return"GZTJ"+Date.now()+"000"+Math.random().toString(36).substr(2,9)}function C(){const e={id:ne(),title:"",type:y.TEXTAREA,value:"",required:!0,placeholder:""};k.value.push(e)}function E(e){k.value.splice(e,1)}function $(e){e.value=b(e.type),e.isValueLocked=!1,e.title=""}function x(e){return e.type===y.TEXTAREA&&s.value.title&&Z.value.length>0}function c(e,i){e.title=i;const h=O.value[i];h!=null?(e.value=h,e.isValueLocked=!0):(e.value="",e.isValueLocked=!1)}function S(e){return e.isValueLocked||!1}function b(e){switch(e){case y.TEXTAREA:return"";case y.DATE:return"";case y.AMOUNT:return 0;default:return""}}function f(e){const i=e.target,h=i.files;!h||h.length===0||(Array.from(h).forEach(l=>{const X={id:ne(),name:l.name,size:l.size,type:l.type,url:URL.createObjectURL(l),file:l,status:"success",isServerFile:!1};v.value.push(X)}),i.value="",V.success(`成功添加 ${h.length} 个文件`))}function I(){const e=document.createElement("input");e.type="file",e.multiple=!0,e.accept="*",e.style.display="none",e.addEventListener("change",f),document.body.appendChild(e),e.click(),document.body.removeChild(e)}function ue(e){const i=v.value[e];i.url&&!i.isServerFile&&URL.revokeObjectURL(i.url),v.value.splice(e,1)}function a(){const e={planTitle:s.value.title,caseStatus:T.value,remark:A.value,relatedFiles:v.value.map(i=>i.name),sections:k.value.map(i=>({title:i.title,content:u(i),type:i.type}))};return JSON.stringify(e,null,2)}function u(e){switch(e.type){case y.DATE:return e.value?new Date(e.value).toLocaleDateString():"";case y.AMOUNT:return e.value?`¥${Number(e.value).toFixed(2)}`:"¥0.00";default:return String(e.value||"")}}function p(){for(let e=0;e<k.value.length;e++){const i=k.value[e];if(!i.title.trim())return V.error(`第${e+1}个字段的标题不能为空`),!1;if(i.required&&(!i.value||String(i.value).trim()===""))return V.error(`${i.title}是必填字段，请填写内容`),!1}return!0}function R(e){return{[y.TEXTAREA]:"多行文本",[y.DATE]:"日期选择",[y.AMOUNT]:"金额输入",[y.FILE]:"文件上传"}[e]||"未知类型"}function ee(){const e=new FormData;e.append("id",z.planData.id),e.append("title",s.value.title),e.append("caseStatus",T.value),e.append("remark",A.value),e.append("jsonData",a()),v.value.forEach((h,l)=>{h.file&&!h.isServerFile&&(e.append("relatedFiles",h.file),e.append(`relatedFiles_${l}_id`,h.id||""),e.append(`relatedFiles_${l}_name`,h.name))});const i=v.value.filter(h=>h.isServerFile);return e.append("existingFiles",JSON.stringify(i.map(h=>h.id))),e.append("fields",JSON.stringify(k.value)),e}async function de(){if(L.value){F.value=!0;try{if(!await new Promise(h=>{L.value.validate(l=>{h(l)})})||!p())return;if(k.value.length===0){V.error("请至少保留一个字段");return}const i={id:z.planData.id,asset_package_name:s.value.asset_package_name,mediation_config:k.value,caseStatus:T.value,fileList:v.value,remark:A.value,formData:ee()};M("ensure",i)}finally{F.value=!1}}}return(e,i)=>{const h=Ae,l=Ue,X=Ne,g=ye,we=ke,Me=Je,Oe=Le,Ie=xe;return n(),m(j,null,[o(be,{visible:e.showDialog,"onUpdate:visible":le,width:"1200px",title:"编辑方案"},{default:r(()=>[t("div",zt,[o(X,{ref_key:"planFormRef",ref:L,model:s.value,rules:oe,"label-width":"110px"},{default:r(()=>[e.planData.id?(n(),N(l,{key:0,label:"调解案件号"},{default:r(()=>[o(h,{"model-value":e.planData.id,disabled:"",placeholder:"系统自动生成"},null,8,["model-value"])]),_:1})):ie("",!0),o(l,{label:"资产包名称",prop:"asset_package_name"},{default:r(()=>[o(h,{modelValue:s.value.asset_package_name,"onUpdate:modelValue":i[0]||(i[0]=_=>s.value.asset_package_name=_),readonly:"",placeholder:"请点击选择资产包名称",onClick:K,style:{cursor:"pointer"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),t("div",Xt,[t("div",Bt,[t("div",qt,[Gt,t("div",Kt,[o(J,{onClick:I,height:32},{default:r(()=>[Ht,U("选择文件 ")]),_:1}),v.value.length>0?(n(),m("div",Wt,[(n(!0),m(j,null,q(v.value,(_,_e)=>(n(),m("div",{key:_.id,class:"file-item"},[t("span",{class:"file-name",title:_.name},B(_.name),9,Zt),t("i",{class:"jt-20-delete file-remove",onClick:w=>ue(_e),title:"删除文件"},null,8,Qt)]))),128))])):ie("",!0)])])])]),t("div",ea,[t("div",ta,[aa,o(J,{onClick:C,height:34,"btn-type":"blue"},{default:r(()=>[la,U("添加字段 ")]),_:1})]),k.value.length>0?(n(),m("div",sa,[(n(!0),m(j,null,q(k.value,(_,_e)=>(n(),m("div",{key:_.id,class:"field-item"},[t("div",na,[t("span",oa,"字段 "+B(_e+1),1),o(J,{onClick:w=>E(_e),height:32,"btn-type":"red"},{default:r(()=>[ia,U("删除 ")]),_:2},1032,["onClick"])]),t("div",ca,[t("div",ua,[ra,o(we,{modelValue:_.type,"onUpdate:modelValue":w=>_.type=w,onChange:w=>$(_),style:{width:"280px"}},{default:r(()=>[(n(),m(j,null,q(Y,w=>o(g,{key:w.value,label:w.label,value:w.value},{default:r(()=>[t("span",da,[t("i",{class:Te(w.icon),style:{"margin-right":"8px"}},null,2),U(" "+B(w.label),1)])]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),t("div",_a,[pa,x(_)?(n(),N(we,{key:0,modelValue:_.title,"onUpdate:modelValue":w=>_.title=w,onChange:w=>c(_,w),"allow-create":"",filterable:"",placeholder:"选择或输入字段标题",style:{width:"280px"}},{default:r(()=>[(n(!0),m(j,null,q(Z.value,w=>(n(),N(g,{key:w.value,label:w.label,value:w.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):(n(),N(h,{key:1,modelValue:_.title,"onUpdate:modelValue":w=>_.title=w,placeholder:"请输入字段标题",style:{width:"280px"}},null,8,["modelValue","onUpdate:modelValue"]))])]),t("div",va,[t("label",ma,[U(" 内容预览： "),o(Me,{size:"small",type:_.required?"danger":"info"},{default:r(()=>[U(B(R(_.type))+B(_.required?" (必填)":""),1)]),_:2},1032,["type"])]),t("div",fa,[_.type===H(y).TEXTAREA?(n(),N(h,{key:0,modelValue:_.value,"onUpdate:modelValue":w=>_.value=w,type:"textarea",rows:3,disabled:S(_),placeholder:_.placeholder||"请输入内容"},null,8,["modelValue","onUpdate:modelValue","disabled","placeholder"])):_.type===H(y).DATE?(n(),N(Oe,{key:1,modelValue:_.value,"onUpdate:modelValue":w=>_.value=w,type:"date",disabled:S(_),placeholder:_.placeholder||"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue","onUpdate:modelValue","disabled","placeholder"])):_.type===H(y).AMOUNT?(n(),N(Ie,{key:2,modelValue:_.value,"onUpdate:modelValue":w=>_.value=w,min:0,precision:2,disabled:S(_),placeholder:_.placeholder||"请输入金额",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue","disabled","placeholder"])):ie("",!0)])])]))),128))])):(n(),m("div",ha,ba))]),t("div",ya,[o(J,{onClick:de,loading:F.value,height:34,"btn-type":"blue"},{default:r(()=>[ka,U("确认 ")]),_:1},8,["loading"]),o(J,{onClick:le,height:34},{default:r(()=>[wa,U("取消 ")]),_:1})])])]),_:1},8,["visible"]),o(Re,{visible:P.value,"onUpdate:visible":i[1]||(i[1]=_=>P.value=!1),onConfirm:se},null,8,["visible"])],64)}}});const $a=fe(Va,[["__scopeId","data-v-af4c09ed"]]),Ca=D=>(ve("data-v-f8a48405"),D=D(),me(),D),Ea={class:"plan-management"},Da={class:"search-header"},Sa={class:"search-row"},Fa={class:"search-item"},Ta={class:"search-item"},Aa=Ca(()=>t("i",{class:"jt-20-add"},null,-1)),Ua={class:"file-name"},Na={class:"fields-preview"},La={class:"field-types"},xa=["title"],Ra={key:0,class:"more-fields"},Ma={class:"operation-buttons"},Oa=["onClick"],Ia=["onClick"],ge=10,ja=pe({__name:"mediationInformation",setup(D){const M=d({id:"",title:"",fields:[],caseStatus:te.PENDING}),z=d(0),L=d(1),F=d(""),P=d([]),s=d(!1),T=d(""),v=d(!1),A=d(!1),k=d(!1);function Y(){L.value=1,O()}async function O(){s.value=!0;try{const C={page:L.value,page_size:ge,search:F.value};T.value&&(C.case_status=T.value);const{data:E}=await Xe(C),{state:$,msg:x}=E;if($==="success"){const{results:c,count:S}=E.data;P.value=c,z.value=S}else V.error(x)}catch(C){console.error("获取调解计划列表失败:",C),V.error("获取调解计划列表失败")}finally{s.value=!1}}function Z(C){L.value=C,O()}function oe(){v.value=!0}async function ae(C){try{const{data:E}=await Be(C),{state:$,msg:x}=E;$==="success"?(V.success("新增成功"),v.value=!1,Y()):V.error(x)}catch(E){console.error("新增调解计划失败:",E),V.error("新增失败")}}async function Q(C){V.info("功能建设中")}async function le(C){const{data:E}=await qe(C,Number(C.id)),{state:$,msg:x}=E;$==="success"?(V.success(x),A.value=!1,O()):V.error(x||"编辑失败")}function K(C){M.value={...C},k.value=!0}async function se(){if(!M.value.id){V.error("缺少必要参数");return}const{data:C}=await Ge(Number(M.value.id)),{state:E,msg:$}=C;E==="success"?(V.success($),k.value=!1,O()):V.error($||"删除失败")}function ne(){A.value=!1,M.value={id:"",title:"",fields:[],caseStatus:te.PENDING}}return ze(()=>{O()}),(C,E)=>{const $=Ee,x=De,c=Se,S=Fe;return n(),m(j,null,[t("div",Ea,[t("div",Da,[t("div",Sa,[t("div",Fa,[o(je,{modelValue:F.value,"onUpdate:modelValue":E[0]||(E[0]=b=>F.value=b),placeholder:"搜索方案名称或案件号",onClick:Y},null,8,["modelValue"])]),t("div",Ta,[o(J,{onClick:oe,height:34},{default:r(()=>[Aa,U("新增调解")]),_:1})])])]),t("div",null,[Ce((n(),N(x,{data:P.value,border:"","cell-style":H(Ve),"header-cell-style":H($e),class:"plan-table"},{default:r(()=>[o($,{type:"index",label:"序号",width:"60",align:"center"},{default:r(({$index:b})=>[U(B(ge*(L.value-1)+b+1),1)]),_:1}),o($,{prop:"case_number",label:"调解案件号",align:"center",width:"180"}),o($,{prop:"case_status_cn",label:"案件状态",align:"center",width:"150"}),o($,{prop:"creditor_name",label:"债权人",align:"center",width:"150"}),o($,{prop:"debtor_name",label:"债务人",align:"center",width:"150"}),o($,{prop:"fileList",label:"相关文件",align:"center","min-width":"100"},{default:r(({row:b})=>[(n(!0),m(j,null,q(b.fileList,f=>(n(),m("div",{class:"file-list",key:f.id},[t("span",Ua,B(f.name),1)]))),128))]),_:1}),o($,{label:"调解信息配置",align:"center","min-width":"180"},{default:r(({row:b})=>[t("div",Na,[t("div",La,[(n(!0),m(j,null,q(b.mediation_config.slice(0,3),f=>(n(),m("span",{key:f.id,class:"field-type-tag",title:f.title},B(f.title),9,xa))),128)),b.mediation_config.length>3?(n(),m("span",Ra," +"+B(b.fields.length-3),1)):ie("",!0)])])]),_:1}),o($,{label:"操作",align:"center",width:"250"},{default:r(({row:b})=>[t("div",Ma,[t("div",{onClick:f=>Q(b),class:"operation-btn edit-btn"},"编辑",8,Oa),t("div",{onClick:f=>K(b),class:"operation-btn delete-btn"},"删除",8,Ia)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[S,s.value]]),o(c,{class:"pagi",background:"",layout:"prev, pager, next",total:z.value,"current-page":L.value,"page-size":ge,onCurrentChange:Z},null,8,["total","current-page"])])]),o(Jt,{"show-dialog":v.value,onClose:E[1]||(E[1]=b=>v.value=!1),onEnsure:ae},null,8,["show-dialog"]),o($a,{"show-dialog":A.value,"plan-data":M.value,onClose:ne,onEnsure:le},null,8,["show-dialog","plan-data"]),o(Ke,{"show-dialog":k.value,onEnsure:se,onClose:E[2]||(E[2]=b=>k.value=!1)},null,8,["show-dialog"])],64)}}});const el=fe(ja,[["__scopeId","data-v-f8a48405"]]);export{el as default};
