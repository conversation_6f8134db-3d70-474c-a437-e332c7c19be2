<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import CustomDialog from '@/components/common/CustomDialog.vue'
import CustomButton from '@/components/common/CustomButton.vue'
import type {
  DataImportDetail,
  FieldMapping,
  FileColumn,
  FieldConfigOption,
  DebtorFieldOption
} from '../auth/type'
import { getDataImportDetail, editDataImport, getCreditor, getFieldConfig, getDebtorFieldChoices } from '@/axios/system'

// 债权人选项接口定义
interface CreditorOption {
  id: number
  name: string
}

// 组件属性定义
interface Props {
  visible: boolean              // 弹框显示状态
  recordId: number | null       // 记录ID
}

// 组件事件定义
interface Emits {
  (e: 'close'): void           // 关闭弹框
  (e: 'success'): void         // 编辑成功
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const detailLoading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<{
  package_name: string
  source_file_name: string
  creditor: number | null
  field_mappings_detail: FieldMapping[]
}>({
  package_name: '',              // 资产包名称
  source_file_name: '',               // 原文件名称
  creditor: null,               // 债权人ID
  field_mappings_detail: [] // 字段映射配置
})

// 债权人选项数据
const creditorOptions = ref<CreditorOption[]>([])
const creditorLoading = ref(false)

// 字段配置选项数据（用于下拉框）
const fieldConfigOptions = ref<FieldConfigOption[]>([])
const fieldConfigLoading = ref(false)

// 债务人字段选项数据
const debtorFieldOptions = ref<DebtorFieldOption[]>([])
const debtorFieldLoading = ref(false)

// 表单验证规则
const formRules = {
  package_name: [
    { required: true, message: '请输入资产包名称', trigger: 'blur' }
  ],
  source_file_name: [
    { required: true, message: '原文件名称不能为空', trigger: 'blur' }
  ],
  creditor: [
    { required: true, message: '请选择债权人', trigger: 'change' }
  ]
}

// 文件列选项（保留兼容性）
// const file_columns = ref<FileColumn[]>([])

// 监听弹框显示状态，显示时加载详情
watch(() => props.visible, (visible) => {
  if (visible && props.recordId) {
    loadDetail()
    loadCreditorOptions()
    loadFieldConfigOptions()
    loadDebtorFieldOptions()
  }
})

/**
 * 加载数据导入详情
 */
async function loadDetail() {
  if (!props.recordId) return
  
  detailLoading.value = true
    // 调用实际的详情接口
    const data = await getDataImportDetail(props.recordId)
    const {data:detail} = data.data
    
    // 填充表单数据
    formData.package_name = detail.package_name
    formData.source_file_name = detail.source_file_name
    formData.creditor = detail.creditor || null
    formData.field_mappings_detail = detail.field_mappings_detail || []
    // file_columns.value = detail.file_columns || []
     
    detailLoading.value = false 
}

/**
 * 加载债权人选项
 */
async function loadCreditorOptions() {
  creditorLoading.value = true
  // 调用实际的债权人列表接口
  const { data } = await getCreditor({ page_size: 1000 })
  const { state, msg } =data
  if(state == 'success'){
    // 转换数据格式
    creditorOptions.value = data.data.results.map((item: any) => ({
      id: item.id,
      name: item.creditor_name
    }))
  }else{
    ElMessage.error(msg)
  }
  creditorLoading.value = false
}

/**
 * 加载字段配置选项（用于下拉框数据源）
 */
async function loadFieldConfigOptions() {
  fieldConfigLoading.value = true
  try {
    // 调用字段配置接口，获取所有字段配置
    const {data} = await getFieldConfig({ page_size: 1000 })
    const {state, msg} = data
    if(state === 'success') {
      // 转换数据格式：key=id，value=field_name
      fieldConfigOptions.value = data.data.results.map((item: any) => ({
        id: item.id,
        field_name: item.field_name
      }))
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    ElMessage.error('加载字段配置失败')
  } finally {
    fieldConfigLoading.value = false
  }
}

/**
 * 加载债务人字段选项
 */
async function loadDebtorFieldOptions() {
  debtorFieldLoading.value = true
  const {data} = await getDebtorFieldChoices()
  const {state, msg} = data
  if(state === 'success') {
    // 使用接口返回的数据格式：value作为选项值，label作为显示文本
    debtorFieldOptions.value = data.data || []
  } else {
    ElMessage.error(msg || '加载债务人选项失败')
  }
  debtorFieldLoading.value = false
}

// 注意：Element Plus 的 filterable 下拉框会自动根据 label 进行过滤
// 不需要自定义 filter-method，默认行为就能满足需求

/**
 * 字段映射列过滤（保留兼容性）
 * @param query 搜索关键字
 */
/* function filterFileColumns(query: string) {
  if (!query) return file_columns.value
  return file_columns.value.filter(item =>
    item.name.toLowerCase().includes(query.toLowerCase()) ||
    item.label.toLowerCase().includes(query.toLowerCase())
  )
} */

/**
 * 更新字段映射 - 支持新的数据结构
 * @param index 字段索引
 * @param fieldConfigId 字段配置ID（可以为null）
 */
function updateFieldMapping(index: number, fieldConfigId: any) {
  const configId = fieldConfigId as number | null
  if (formData.field_mappings_detail[index]) {
    if (configId) {
      // 找到对应的字段配置
      const fieldConfig = fieldConfigOptions.value.find(config => config.id === configId)
      if (fieldConfig) {
        formData.field_mappings_detail[index].mapped_field_config = {
          id: fieldConfig.id,
          field_name: fieldConfig.field_name
        }
      }
    } else {
      // 清空映射
      formData.field_mappings_detail[index].mapped_field_config = null
    }
  }
}

/**
 * 更新债务人字段映射
 * @param index 字段索引
 * @param debtorFieldValue 债务人字段值（可以为null）
 */
function updateDebtorFieldMapping(index: number, debtorFieldValue: any) {
  const fieldValue = debtorFieldValue as string | null
  if (formData.field_mappings_detail[index]) {
    formData.field_mappings_detail[index].mapped_debtor_field_config = fieldValue
  }
}

/**
 * 获取当前选中的字段配置ID
 * @param mapping 字段映射对象
 * @returns 字段配置ID或null
 */
function getSelectedFieldConfigId(mapping: FieldMapping): number | null {
  return mapping.mapped_field_config?.id || null
}

/**
 * 获取当前选中的债务人字段映射值
 * @param mapping 字段映射对象
 * @returns 债务人字段映射值或null
 */
function getSelectedDebtorFieldValue(mapping: FieldMapping): string | null {
  return mapping.mapped_debtor_field_config || null
}

/**
 * 提交编辑 - 按照新的数据格式构造请求
 */
async function handleSubmit() {
  // 使用表单验证
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    return // 验证失败，不继续提交
  }

  loading.value = true
  try {
    // 按照需求构造请求数据格式
    const submitData = {
      package_name: formData.package_name,
      creditor: formData.creditor!,
      field_mappings: formData.field_mappings_detail.map(mapping => ({
        id: mapping.id!, // 映射记录ID（必需，用于标识要修改的映射）
        original_field_name: mapping.original_field_name!, // 原表头字段名称（不可修改，用于验证）
        mapped_field_config_id: mapping.mapped_field_config?.id || null, // 新的字段配置ID或null
        mapped_debtor_field_config: mapping.mapped_debtor_field_config || null // 债务人字段映射
      }))
    }

    const {data} = await editDataImport(submitData, props.recordId!)
    const {state, msg} = data
    if(state === 'success') {
      ElMessage.success(msg)
      emit('success')
      handleCancel()
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 取消操作
 */
function handleCancel() {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.package_name = ''
  formData.source_file_name = ''
  formData.creditor = null
  formData.field_mappings_detail = []
  // file_columns.value = []

  emit('close')
}
</script>

<template>
  <CustomDialog
    :visible="visible"
    title="编辑资产包"
    width="800px"
    @update:visible="handleCancel"
    class="edit-dialog">
    
    <div v-loading="detailLoading" class="form-container">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <div class="basic-info">
          <el-form-item label="资产包名称" prop="package_name" class="form-item">
            <el-input
              v-model="formData.package_name"
              placeholder="请输入资产包名称"
              clearable />
          </el-form-item>

          <el-form-item label="原文件名称" prop="source_file_name" class="form-item">
            <el-input
              v-model="formData.source_file_name"
              placeholder="原文件名称"
              disabled />
          </el-form-item>

          <el-form-item label="债权人" prop="creditor" class="form-item">
            <el-select
              v-model="formData.creditor"
              placeholder="请选择债权人"
              filterable
              clearable
              :loading="creditorLoading"
              style="width: 100%;">
              <el-option
                v-for="option in creditorOptions"
                :key="option.id"
                :label="option.name"
                :value="option.id" />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
      <div class="field-mapping">
        <div class="mapping-container">
          <div class="mapping-header">
            <div class="field-column">文件表头</div>
            <div class="file-column">资产包字段映射</div>
            <div class="debtor-column">债务人字段映射</div>
          </div>
          
          <div class="mapping-list">
            <div
              v-for="(mapping, index) in formData.field_mappings_detail"
              :key="`mapping-${mapping.id || index}-${mapping.fieldName || ''}`"
              class="mapping-item">
              <div class="field-info">
                <span class="field-label">
                  {{ mapping.original_field_name || mapping.fieldLabel }}
                  <!-- <span v-if="mapping.isRequired" class="required">*</span> -->
                </span>
                <!-- <span class="field-name">{{ mapping.fieldName }}</span> -->
              </div>
              <div class="column-select">
                <el-select
                  :model-value="getSelectedFieldConfigId(mapping)"
                  @update:model-value="(value) => updateFieldMapping(index, value)"
                  placeholder="请选择文件列"
                  filterable
                  clearable
                  :loading="fieldConfigLoading"
                  style="width: 100%;">
                  <el-option
                    v-for="option in fieldConfigOptions"
                    :key="option.id"
                    :label="option.field_name"
                    :value="option.id" />
                </el-select>
              </div>
              <div class="debtor-select">
                <el-select
                  :model-value="getSelectedDebtorFieldValue(mapping)"
                  @update:model-value="(value) => updateDebtorFieldMapping(index, value)"
                  placeholder="请选择债务人字段"
                  filterable
                  clearable
                  :loading="debtorFieldLoading"
                  style="width: 100%;">
                  <el-option
                    v-for="option in debtorFieldOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value" />
                </el-select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <CustomButton 
        @click="handleSubmit" 
        :loading="loading"
        :height="34" 
        btn-type="blue">
        <i class="jt-20-ensure"></i>确认
      </CustomButton>
      <CustomButton 
        @click="handleCancel" 
        :height="34">
        <i class="jt-20-delete"></i>取消
      </CustomButton>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-dialog {
  .form-container {
    padding: 20px 0;
    min-height: 400px;
    
    .basic-info {
      margin-bottom: 32px;

      .form-item {
        margin-bottom: 20px;
      }
    }
    
    .field-mapping {
      .mapping-container {
        border: 1px solid #ebeef5;
        border-radius: 6px;
        overflow: hidden;
        
        .mapping-header {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          background-color: #f5f7fa;

          .field-column,
          .file-column,
          .debtor-column {
            padding: 12px 16px;
            font-weight: 600;
            color: #606266;
            border-right: 1px solid #ebeef5;

            &:last-child {
              border-right: none;
            }
          }
        }
        
        .mapping-list {
          max-height: 400px;
          overflow-y: auto;
          
          .mapping-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            border-bottom: 1px solid #ebeef5;

            &:last-child {
              border-bottom: none;
            }

            .field-info {
              padding: 16px;
              border-right: 1px solid #ebeef5;
              display: flex;
              flex-direction: column;
              gap: 4px;

              .field-label {
                color: #303133;
                font-weight: 500;
                font-size: 14px;
              }

              .field-name {
                color: #909399;
                font-size: 12px;
                font-family: 'Monaco', 'Consolas', monospace;
              }
            }

            .column-select,
            .debtor-select {
              padding: 16px;
              display: flex;
              align-items: center;
              border-right: 1px solid #ebeef5;

              &:last-child {
                border-right: none;
              }
            }
          }
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    padding-top: 30px;
  }
}

// Element Plus 组件样式覆盖
:deep(.el-select) {
  .el-select__wrapper {
    border-radius: 4px;
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}

// 滚动条样式
.mapping-list {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}
</style> 