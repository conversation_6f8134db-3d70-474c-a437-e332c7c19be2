<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomButton from '@/components/common/CustomButton.vue'
import CustomInput from '@/components/common/CustomInput.vue'
import EditComplaintDialog from '@/components/system/dialogs/EditComplaintDialog.vue'
import { getFeedback, getFeedbackDetail, editFeedback } from '@/axios/system'
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"

/**
 * 投诉反馈数据接口
 */
interface complaintFeedback {
  id: number
  case_number: string        // 案件编号
  feedback_type: string // 反馈类型
  category: string   // 具体类别
  description: string // 详细描述
  phone_number: string       // 联系方式
  process_status: string // 处理状态
  handler: string          // 处理人
  handle_time: string       // 处理时间
  handle_result: string     // 处理结果
}

/**
 * API响应接口
 */
interface ApiResponse {
  data: {
    results: complaintFeedback[]
    count: number
  }
}

// 响应式数据
const loading = ref(false)
const tableData = ref<complaintFeedback[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = 10

// 搜索条件
const searchCategory = ref('')           // 具体类别搜索
const filterStatus = ref<''>('') // 处理状态筛选
const filterType = ref<''>('')   // 反馈类型筛选

// 编辑弹框
const showEditDialog = ref(false)
const currentEditItem = ref<complaintFeedback | null>(null)

/**
 * 反馈类型选项
 */
const feedbackTypeOptions = [
  { label: '意见建议', value: 'suggestion' },
  { label: '服务投诉', value: 'complaint '},
]

/**
 * 处理状态选项
 */
const processStatusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已解决', value: 'resolved' },
  { label: '已关闭', value: 'closed' }
]

async function fetchComplaintList() {
  loading.value = true
  const params = {
    page: page.value,
    page_size: pageSize,
    search: searchCategory.value,
    process_status: filterStatus.value,
    feedback_type: filterType.value
  }
  const {data} = await getFeedback(params)
  const {state,msg} = data
  if(state == "success"){
    const { results, count } = data.data
    tableData.value = results
    total.value = count
  }else{
    ElMessage.error(msg)
  }
  loading.value = false
}

function handleSearch() {
  page.value = 1
  fetchComplaintList()
}


function handlePageChange(newPage: number) {
  page.value = newPage
  fetchComplaintList()
}

async function openEditDialog(row: complaintFeedback,index:number) {
  currentEditItem.value = row
  showEditDialog.value = true
}

function closeEditDialog() {
  showEditDialog.value = false
  currentEditItem.value = null
}

/**
 * 处理编辑确认
 */
async function handleEditConfirm(updateData: { handler: string; handle_time: string; process_status: string; handle_result: string }) {
  if (!currentEditItem.value) return
  
  // 调用editFeedback接口
  const {data} = await editFeedback(updateData, currentEditItem.value.id)
  const {state,msg} = data
  if(state == "success"){
    ElMessage.success(msg)
    fetchComplaintList()
    closeEditDialog()
  } else {
    ElMessage.error(msg)
  }
}

/**
 * 格式化日期显示
 */
function formatDate(dateString: string): string {
  if (!dateString) return ' '
  return dateString.split('T')[0]
}

// 组件挂载时获取数据
onMounted(() => {
  fetchComplaintList()
})
</script>

<template>
  <div class="complaint-feedback-page">
    <!-- 搜索和筛选区域 -->
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput 
            v-model="searchCategory" 
            placeholder="搜索案件编号" 
            @keyup.enter="handleSearch"
            @click="handleSearch" />
        </div>
        
        <div class="search-item">
          <label>处理状态：</label>
          <el-select 
            v-model="filterStatus" 
            placeholder="请选择处理状态"
            clearable
            @change="handleSearch">
            <el-option
              v-for="option in processStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
        
        <div class="search-item">
          <label>反馈类型：</label>
          <el-select 
            v-model="filterType" 
            placeholder="请选择反馈类型"
            clearable
            @change="handleSearch">
            <el-option
              v-for="option in feedbackTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column type="index" label="序号" width="60" align="center">
          <template #default="{ $index }">
            {{ (page - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="case_number" label="案件编号" width="150" align="center" />
        <el-table-column label="反馈类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.feedback_type_cn === '服务投诉' ? 'danger' : 'primary'" size="small">
              {{ row.feedback_type_cn }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category_cn" label="具体类别" width="190" align="center" />
        <el-table-column prop="description" label="详细描述" min-width="200" align="left"/>
        <el-table-column prop="phone_number" label="联系方式" width="130" align="center" />
        <el-table-column prop="process_status_cn" label="处理状态" width="100" align="center"/>
        <el-table-column prop="handler" label="处理人" width="100" align="center" />
        <el-table-column label="处理时间" width="120" align="center">
          <template #default="{ row }">
            {{ formatDate(row.handle_time) }}
          </template>
        </el-table-column>
        <el-table-column label="处理结果" min-width="150" align="left">
          <template #default="{ row }">
            <div class="result-text">{{ row.handle_result}}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="{ row,$index }">
            <div class="operation-buttons">
              <div class="operation-btn edit-btn" @click="openEditDialog(row,$index)">编辑</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="page"
          :page-size="pageSize"
          @current-change="handlePageChange" />
      </div>
    </div>

    <!-- 编辑弹框 -->
    <EditComplaintDialog 
      :visible="showEditDialog"
      :complaint-data="currentEditItem"
      @update:visible="closeEditDialog"
      @confirm="handleEditConfirm" />
  </div>
</template>

<style lang="scss" scoped>
.complaint-feedback-page {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    margin-bottom: 20px;
    
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          min-width: 80px;
          font-weight: 500;
          color: #333;
        }
        
        :deep(.el-select) {
          width: 160px;
        }
      }
    }
  }
  
  .table-container {
    .description-text,
    .result-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 200px;
    }
    
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .complaint-feedback-page {
    .search-header {
      .search-row {
        flex-direction: column;
        align-items: stretch;
        
        .search-item {
          flex-direction: column;
          align-items: flex-start;
          
          label {
            min-width: auto;
          }
          
          :deep(.el-select) {
            width: 100%;
          }
        }
      }
    }
  }
}
</style> 