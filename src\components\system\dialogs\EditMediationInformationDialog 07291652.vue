<script lang="ts" setup>
import { ref, type Ref, watch, computed } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
// import VueExpressionEditor from "vue-expression-editor"; // 暂时不使用，改用自定义编辑器
import { ElMessage } from "element-plus";
import type { FieldType, MediationField, UploadFile } from '../auth/type';
import { FieldType as FieldTypeEnum, FieldConfigOption,DebtorFieldOption } from '../auth/type';
import { getDebtor, editMediationCase, getDataImportDetail, getMediationCaseDetail, getExpressionVariables, parseExpression } from '@/axios/system';

/**
 * 扩展的上传文件接口，增加服务器文件标识
 */
interface ExtendedUploadFile extends UploadFile {
  isServerFile?: boolean
  file?: File | string
  url?: string
  type?: string
}

/**
 * 扩展的调解字段接口，支持表达式编辑
 */
interface ExtendedMediationField extends MediationField {
  expression?: string  // 表达式内容
  preview?: string     // 预览内容
  mapped_field_config?: {
    id: number
    field_name: string
  } | null
  mapped_debtor_field_config?: string | null
}

const props = defineProps<{
  visible: boolean,
  rowData: any,  // 要编辑的行数据
  editMode: 'asset' | 'mediation'  // 编辑模式
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void,
  (e: 'confirm', formData: FormData): void
}>()

// 表单引用
const formRef = ref()
const loading = ref(false)
const detailLoading = ref(false)

// 表单数据
const formData = ref({
  debtor: '',
  creditor: null as number | null,
  creditor_name: '',
  asset_package_name: '',
  field_mappings_detail: [] as ExtendedMediationField[],
  attachments: [] as ExtendedUploadFile[]
})

// 字段配置选项数据（用于下拉框）
const fieldConfigOptions = ref<FieldConfigOption[]>([])
const fieldConfigLoading = ref(false)

// 债务人字段选项数据
const debtorFieldOptions = ref<DebtorFieldOption[]>([])
const debtorFieldLoading = ref(false)

// 债务人选项
const debtorOptions = ref<Array<{label: string, value: number}>>([])

// 相关文件
const relatedFiles: Ref<ExtendedUploadFile[]> = ref([])

// 动态字段列表
const dynamicFields: Ref<ExtendedMediationField[]> = ref([])

// 表达式编辑相关数据
const expressionVariables = ref<Array<{code: string, name: string, value?: number}>>([])
// const expressionLoading = ref(false)
const previewContent = ref('')
const previewLoading = ref(false)

// 计算属性：对话框标题
const dialogTitle = computed(() => {
  return props.editMode === 'asset' ? '编辑资产包信息' : '编辑调解案件信息'
})

// 计算属性：可编辑字段
const editableFields = computed(() => {
  if (props.editMode === 'asset') {
    return ['field_mappings_detail', 'attachments']  // 资产包模式：只能编辑调解信息配置和相关文件
  } else {
    return ['debtor', 'attachments']  // 调解案件模式：只能编辑债务人和相关文件
  }
})

// 监听弹框显示状态，进行数据回显
watch(() => props.visible, (newVal) => {
  if (newVal && props.rowData) {
    loadDetailData()
    getDebtorOptions()
    // 如果是资产包模式，加载表达式变量
    /* if (props.editMode === 'asset') {
      loadExpressionVariables()
    } */
  }
}, { immediate: true })

// 监听行数据变化，进行数据回显
watch(() => props.rowData, (newVal) => {
  if (newVal && props.visible) {
    loadDetailData()
  }
}, { deep: true })

/**
 * 根据编辑模式加载详情数据
 */
async function loadDetailData() {
  if (!props.rowData || !props.rowData.id) return

  detailLoading.value = true
  try {
    let detailData: any = null

    // 根据编辑模式调用不同的详情接口
    if (props.editMode === 'asset') {
      // 资产包模式：调用 getDataImportDetail 接口
      const response = await getDataImportDetail(Number(props.rowData.id))
      const {data:detail} = response.data
      detailData = detail
    } else {
      // 调解案件模式：调用 getMediationCaseDetail 接口
      const response = await getMediationCaseDetail(Number(props.rowData.id))
      const {data:detail} = response.data
      detailData = detail
    }

    // 使用详情数据进行回显
    if (detailData) {
      loadRowData(detailData)
    }
  } catch (error) {
    console.error('加载详情数据失败:', error)
    ElMessage.error('加载详情数据失败，请重试')
  } finally {
    detailLoading.value = false
  }
}

/**
 * 加载行数据进行回显
 * @param rowData 行数据
 */
function loadRowData(rowData: any) {
  if (!rowData) return

  console.log('加载编辑数据:', rowData) // 调试日志

  // 基础信息回显
  formData.value.debtor = rowData.debtor || ''
  formData.value.creditor = rowData.creditor
  formData.value.creditor_name = rowData.creditor_name || ''
  formData.value.asset_package_name = rowData.asset_package_name || rowData.package_name || ''

  // 文件回显处理 - 标记为服务器文件
  relatedFiles.value = rowData.attachments ? JSON.parse(JSON.stringify(rowData.attachments)).map((file: UploadFile) => ({
    ...file,
    isServerFile: true
  } as ExtendedUploadFile)) : []

  // 动态字段深拷贝
  formData.value.field_mappings_detail = rowData.field_mappings_detail || []

  // 处理调解配置字段（用于表达式编辑）
  if (rowData.mediation_config && Array.isArray(rowData.mediation_config)) {
    dynamicFields.value = rowData.mediation_config.map((config: any) => ({
      id: config.id || generateFieldId(),
      title: config.title || '',
      type: config.type || FieldTypeEnum.TEXTAREA,
      // 将后端的大括号格式转换为用户友好的显示格式
      expression: convertBracesToDisplayFormat(config.expression || ''),
      preview: config.preview || ''
    }))
  } else {
    dynamicFields.value = []
  }
  // 资产包tab（配置变量）
  if (props.editMode === 'asset') {
    expressionVariables.value = rowData.mapped_field_names.map((name: string) => ({
      code: name,
      name: name, // 显示中文变量名
      value: 0 // 默认值
    }))
  }
}

// 获取债务人选项
function getDebtorOptions() {
  getDebtor({ page: 1, page_size: 1000 }).then(res => {
    const { state, msg } = res.data
    if (state === 'success') {
      debtorOptions.value = res.data.data.results.map((item: any) => ({
        label: item.debtor_name,
        value: item.id
      }))
    } else {
      ElMessage.error(msg)
    }
  })
}

// 关闭弹框
function close() {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.value.creditor = null
  formData.value.field_mappings_detail = []
  previewContent.value = ''
  emit('update:visible', false)
}

// 生成唯一字段ID
function generateFieldId(): string {
  return 'GZTJ' + Math.random().toString(36).substring(2, 11)
}

// 添加新字段
function addField() {
  const newField: ExtendedMediationField = {
    id: generateFieldId(),
    title: '',
    type: FieldTypeEnum.TEXTAREA,
    expression: '',
    preview: ''
  }
  dynamicFields.value.push(newField)
}

// 删除字段
function removeField(index: number) {
  dynamicFields.value.splice(index, 1)
}

/**
 * 相关文件上传处理 - 支持多文件同时选择
 * @param event 文件选择事件
 */
function handleRelatedFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const fileList = target.files
  
  if (!fileList || fileList.length === 0) return
  
  // 遍历所有选中的文件
  Array.from(fileList).forEach(file => {
    const uploadFile: ExtendedUploadFile = {
      id: generateFieldId(),
      file_name: file.name,
      url: URL.createObjectURL(file),
      file: file,
      isServerFile: false
    }

    relatedFiles.value.push(uploadFile)
  })
  
  // 清空input值，允许重复选择相同文件
  target.value = ''
}

/**
 * 触发文件选择器（重新上传）
 */
function triggerFileSelector() {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true // 支持多文件选择
  input.accept = '*' // 不限制文件类型
  input.style.display = 'none'
  
  input.addEventListener('change', handleRelatedFileChange)
  
  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

// 删除相关文件
function removeRelatedFile(fileIndex: number) {
  const file = relatedFiles.value[fileIndex]
  // 释放创建的URL
  if (file.file && !file.isServerFile) {
    URL.revokeObjectURL(file.url!)
  }
  relatedFiles.value.splice(fileIndex, 1)
}

/**
 * 更新字段映射 - 支持新的数据结构
 * @param index 字段索引
 * @param fieldConfigId 字段配置ID（可以为null）
 */
function updateFieldMapping(index: number, fieldConfigId: any) {
  const configId = fieldConfigId as number | null
  if (formData.value.field_mappings_detail[index]) {
    if (configId) {
      // 找到对应的字段配置
      const fieldConfig = fieldConfigOptions.value.find(config => config.id === configId)
      if (fieldConfig) {
        formData.value.field_mappings_detail[index].mapped_field_config = {
          id: fieldConfig.id,
          field_name: fieldConfig.field_name
        }
      }
    } else {
      // 清空映射
      formData.value.field_mappings_detail[index].mapped_field_config = null
    }
  }
}

/**
 * 更新债务人字段映射
 * @param index 字段索引
 * @param debtorFieldValue 债务人字段值（可以为null）
 */
function updateDebtorFieldMapping(index: number, debtorFieldValue: any) {
  const fieldValue = debtorFieldValue as string | null
  if (formData.value.field_mappings_detail[index]) {
    formData.value.field_mappings_detail[index].mapped_debtor_field_config = fieldValue
  }
}

/**
 * 获取当前选中的字段配置ID
 * @param mapping 字段映射对象
 * @returns 字段配置ID或null
 */
function getSelectedFieldConfigId(mapping: ExtendedMediationField): number | null {
  return mapping.mapped_field_config?.id || null
}

/**
 * 获取当前选中的债务人字段映射值
 * @param mapping 字段映射对象
 * @returns 债务人字段映射值或null
 */
function getSelectedDebtorFieldValue(mapping: ExtendedMediationField): string | null {
  return mapping.mapped_debtor_field_config || null
}

// 获取表达式变量列表
/* async function loadExpressionVariables() {
  if (!props.rowData?.id) return

  expressionLoading.value = true
  try {
    // 模拟变量数据，实际应该从接口获取
    expressionVariables.value = [
      { code: 'originalPrice', name: '原价', value: 1000 },
      { code: 'discountPrice', name: '优惠价', value: 800 },
      { code: 'discountRate', name: '折扣率', value: 0.8 },
      { code: 'tax', name: '税费', value: 50 },
      { code: 'serviceFee', name: '服务费', value: 20 }
    ]

    // mapped_field_names: ["借款人名称", "借款人身份证号码"]

    // 实际接口调用（暂时注释）
    // const response = await getExpressionVariables(Number(props.rowData.id))
    // const { data } = response.data
    // if (data.state === 'success') {
    //   expressionVariables.value = data.data
    // }
  } catch (error) {
    console.error('获取表达式变量失败:', error)
    ElMessage.error('获取表达式变量失败')
  } finally {
    expressionLoading.value = false
  }
} */

// 处理表达式变化事件 - 创建闭包函数来传递fieldIndex
function createExpressionChangeHandler(fieldIndex: number) {
  return async (expression: any) => {
    const expressionStr = String(expression)
    if (!expressionStr.trim()) {
      previewContent.value = ''
      return
    }

    console.log(expression, '===expression', 'fieldIndex:', fieldIndex)

    // 实时显示：保持用户友好的@符号格式，不进行转换
    // 只在保存时转换为大括号格式

    // 更新字段的表达式内容（保持原始格式）
    if (dynamicFields.value[fieldIndex]) {
      dynamicFields.value[fieldIndex].expression = expressionStr
    }
  }
}

// 处理表达式失焦事件 - 创建闭包函数来传递fieldIndex
function createExpressionBlurHandler(fieldIndex: number) {
  return async (expression: any) => {
    const expressionStr = String(expression)
    if (!expressionStr.trim()) {
      return
    }

    console.log('失焦处理:', expressionStr, 'fieldIndex:', fieldIndex)

    // 失焦时进行表达式处理和预览
    const processedExpression = processChineseVariables(expressionStr)

    // 调用后端解析接口进行预览（使用转换后的格式）
    await parseExpressionContent(processedExpression, fieldIndex)
  }
}

// 兼容性函数 - 保持原有函数名
async function handleExpressionChange(expression: any, fieldIndex?: number) {
  if (fieldIndex === undefined) {
    console.warn('handleExpressionChange called without fieldIndex')
    return
  }
  return createExpressionChangeHandler(fieldIndex)(expression)
}

async function handleExpressionBlur(expression: any, fieldIndex?: number) {
  if (fieldIndex === undefined) {
    console.warn('handleExpressionBlur called without fieldIndex')
    return
  }
  return createExpressionBlurHandler(fieldIndex)(expression)
}

// 反向转换：将大括号格式转换为用户友好的显示格式
function convertBracesToDisplayFormat(expression: string): string {
  if (!expression || !expression.trim()) {
    return expression
  }

  // 将 {变量名} 转换为 变量名（不使用@符号，直接显示变量名）
  const displayExpression = expression.replace(/\{([^}]+)\}/g, '$1')

  console.log('显示转换:', expression, '->', displayExpression)
  return displayExpression
}

// 处理变量表达式，转换为大括号格式
function processChineseVariables(expression: string): string {
  // 处理三种情况：
  // 1. @符号开头的变量：@合同号 -> {合同号}
  // 2. 直接输入的中文变量名：合同号 -> {合同号}（仅当该变量名在变量列表中存在时）
  // 3. 已经被大括号包裹的变量：{合同号} -> {合同号}（保持不变）
  // 其他普通文本、数字、运算符保持原样

  if (!expression || !expression.trim()) {
    return expression
  }

  let processedExpression = expression.trim()

  // 获取当前可用的变量名列表
  const availableVariableNames = expressionVariables.value.map(variable => variable.name)

  if (availableVariableNames.length === 0) {
    console.log('没有可用的变量列表')
    return processedExpression
  }

  // 1. 处理@符号开头的变量名（支持中文、英文、数字组合）
  // 更精确的正则表达式，匹配@后面的完整变量名
  processedExpression = processedExpression.replace(/@([^\s+\-*/(){}@,，。！？；：""'']+)/g, (fullMatch, variableName) => {
    // 移除@符号，用大括号包裹变量名
    const cleanVariableName = variableName.trim()
    console.log(`处理@变量: ${fullMatch} -> {${cleanVariableName}}`)
    return `{${cleanVariableName}}`
  })

  // 2. 处理直接输入的变量名（按变量名长度从长到短排序，避免短变量名覆盖长变量名）
  const sortedVariableNames = [...availableVariableNames].sort((a, b) => b.length - a.length)

  sortedVariableNames.forEach(variableName => {
    if (!variableName || variableName.trim() === '') return

    // 转义特殊字符，创建精确匹配的正则表达式
    const escapedVariableName = variableName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    // 创建更简单的正则表达式，匹配独立的变量名
    // 使用单词边界 \b 确保完整匹配，但对中文字符需要特殊处理
    let regex: RegExp
    if (/[\u4e00-\u9fa5]/.test(variableName)) {
      // 中文变量名，使用更宽松的边界匹配
      regex = new RegExp(`(?<!\\{)${escapedVariableName}(?!\\})`, 'g')
    } else {
      // 英文变量名，使用单词边界
      regex = new RegExp(`(?<!\\{)\\b${escapedVariableName}\\b(?!\\})`, 'g')
    }

    // 替换所有匹配的变量名
    let lastProcessed = ''
    while (lastProcessed !== processedExpression) {
      lastProcessed = processedExpression
      processedExpression = processedExpression.replace(regex, (match, offset) => {
        // 检查这个位置是否已经在大括号内
        const beforeMatch = processedExpression.substring(0, offset)

        // 计算大括号的平衡
        const openBraces = (beforeMatch.match(/\{/g) || []).length
        const closeBraces = (beforeMatch.match(/\}/g) || []).length

        // 如果大括号不平衡，说明当前在大括号内，不处理
        if (openBraces > closeBraces) {
          return match
        }

        console.log(`处理直接变量: ${match} -> {${match}}`)
        return `{${match}}`
      })
    }
  })

  // 3. 清理可能的重复大括号（如 {{变量名}} -> {变量名}）
  processedExpression = processedExpression.replace(/\{\{([^}]+)\}\}/g, '{$1}')

  console.log('原始表达式:', expression)
  console.log('可用变量列表:', availableVariableNames)
  console.log('转换后的表达式:', processedExpression)

  return processedExpression
}

// 插入变量到表达式中
function insertVariable(variable: any, fieldIndex: number) {
  if (!dynamicFields.value[fieldIndex]) return

  const currentExpression = dynamicFields.value[fieldIndex].expression || ''
  const variableName = variable.name

  // 在当前表达式末尾添加变量名（不使用@符号，直接添加变量名）
  const newExpression = currentExpression ? `${currentExpression}+${variableName}` : variableName

  // 更新字段表达式
  dynamicFields.value[fieldIndex].expression = newExpression

  console.log(`插入变量: ${variableName}, 新表达式: ${newExpression}`)
}

// 解析表达式内容
async function parseExpressionContent(expression: string, fieldIndex: number) {
  // previewLoading.value = true
  // const response = await parseExpression({
  //   expression: expression,
  //   package_id: props.rowData?.id ? Number(props.rowData.id) : undefined
  // })
  // const { data } = response.data
  // if (data.state === 'success') {
  //   previewContent.value = data.data.result
  //   if (dynamicFields.value[fieldIndex]) {
  //     dynamicFields.value[fieldIndex].preview = data.data.result
  //   }
  // } else {
  //   ElMessage.error(data.msg || '表达式解析失败')
  // }
  // previewLoading.value = false
  /* try {
    // 模拟解析结果
    const mockResult = `解析结果: ${expression.replace(/\{([^}]+)\}/g, '$1的值')} = 计算结果`
    previewContent.value = mockResult

    // 更新字段的预览内容
    if (dynamicFields.value[fieldIndex]) {
      dynamicFields.value[fieldIndex].preview = mockResult
    }

    // 实际接口调用（暂时注释）
    const response = await parseExpression({
      expression: expression,
      package_id: props.rowData?.id ? Number(props.rowData.id) : undefined
    })
    const { data } = response.data
    if (data.state === 'success') {
      previewContent.value = data.data.result
      if (dynamicFields.value[fieldIndex]) {
        dynamicFields.value[fieldIndex].preview = data.data.result
      }
    } else {
      ElMessage.error(data.msg || '表达式解析失败')
    }
  } catch (error) {
    console.error('表达式解析失败:', error)
    ElMessage.error('表达式解析失败，请检查表达式格式')
  } finally {
    previewLoading.value = false
  } */
}

// 确认编辑
async function ensureEdit() {
  if (!formRef.value) return

  loading.value = true
  try {
    const submitFormData = new FormData()
    
    // 根据编辑模式添加不同的字段
    if (props.editMode === 'mediation') {
      // 调解案件模式：可编辑债务人
      submitFormData.append('debtor', formData.value.debtor)
    }
    
    // props.editMode === 'asset'添加调解信息配置（两种模式都可能需要）
    if (editableFields.value.includes('field_mappings_detail')) {
      // 处理动态字段数据，确保包含表达式信息
      // field.expression如果是变量则改为{变量}+数字+{变量}
      const processedFields = dynamicFields.value.map(field => {
        const processedExpression = processChineseVariables(field.expression || '')
        return {
          ...field,
          expression: processedExpression
        }
      })
      /* const processedFields = dynamicFields.value.map(field => ({
        id: field.id,
        title: field.title,
        type: field.type,
        value: field.value,
        expression: field.expression || '',
        preview: field.preview || ''
      })) */
      console.log(processedFields,'=====配置')
      submitFormData.append('mediation_config', JSON.stringify(processedFields))
    }

    // 处理文件上传
    if (editableFields.value.includes('attachments')) {
      relatedFiles.value.forEach((fileItem) => {
        if (fileItem.file && !fileItem.isServerFile) {
          // 新上传的文件
          submitFormData.append('file', fileItem.file)
        } else if (fileItem.isServerFile && fileItem.id) {
          // 保留的服务器文件
          submitFormData.append('file_id', fileItem.id)
        }
      })
    }else{
      relatedFiles.value.forEach((fileItem) => {
        if (fileItem.file && !fileItem.isServerFile) {
          submitFormData.append('file', fileItem.file)
        }
      })
    }

    // emit('confirm', submitFormData)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog :visible="visible" @update:visible="close" width="1000px" :title="dialogTitle">
    <div class="edit-dialog-content" v-loading="detailLoading" element-loading-text="正在加载详情数据...">
      <el-form ref="formRef" :model="formData" label-width="110px">
        <el-form-item 
          v-if="editMode === 'mediation'" 
          label="债务人" 
          prop="debtor">
          <el-select
            v-model="formData.debtor"
            filterable
            placeholder="请选择债务人"
            style="width: 100%"
            clearable>
            <el-option
              v-for="option in debtorOptions"
              :key="option.value + 'zwr'"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="债权人" prop="creditor_name">
          <el-input
            v-model="formData.creditor_name"
            disabled />
        </el-form-item>
        <el-form-item label="资产包名称" prop="asset_package_name">
          <el-input
            v-model="formData.asset_package_name"
            disabled />
        </el-form-item>
      </el-form>

      <!-- 字段配置信息展示（禁用） - 仅调解案件模式显示 -->
      <div v-if="editMode === 'mediation' && dynamicFields.length > 0" class="config-display-section">
        <div class="section-header">
          <h3>字段配置信息（只读）</h3>
        </div>

        <div class="fields-display">
          <div
            v-for="(field, index) in dynamicFields"
            :key="field.id"
            class="field-display-item">

            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
            </div>

            <div class="field-config">
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <el-input
                  v-model="field.title"
                  disabled
                  style="width: 280px;"/>
              </div>

              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select
                  v-model="field.type"
                  disabled
                  style="width: 280px;">
                  <el-option label="文本输入" :value="FieldTypeEnum.TEXTAREA" />
                  <el-option label="日期选择" :value="FieldTypeEnum.DATE" />
                  <el-option label="金额输入" :value="FieldTypeEnum.AMOUNT" />
                </el-select>
              </div>
            </div>

            <div class="field-preview">
              <label class="config-label">内容预览：</label>
              <div class="preview-content">
                <el-input
                  v-if="field.type === FieldTypeEnum.TEXTAREA"
                  v-model="field.value"
                  type="textarea"
                  disabled
                  :rows="3"
                  placeholder="请输入内容" />

                <el-date-picker
                  v-else-if="field.type === FieldTypeEnum.DATE"
                  v-model="field.value"
                  type="date"
                  disabled
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />

                <el-input
                  v-else-if="field.type === FieldTypeEnum.AMOUNT"
                  v-model="field.value"
                  disabled
                  placeholder="请输入金额"
                  style="width: 200px;" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="editableFields.includes('attachments')" class="file-section">
        <div class="section-header">
          <label class="field-label">相关文件</label>
          <CustomButton @click="triggerFileSelector" :height="32">
            <i class="jt-20-upload"></i>选择文件
          </CustomButton>
        </div>
        
        <div v-if="relatedFiles.length > 0" class="file-list">
          <div
            v-for="(file, fileIndex) in relatedFiles"
            :key="(file.id || 'file') + fileIndex"
            class="file-item">
            <div class="file-info">
              <span class="file-name" :title="file.file_name">{{ file.file_name }}</span>
              <span class="file-type" v-if="file.isServerFile">[服务器文件]</span>
              <span class="file-type" v-else>[新上传]</span>
            </div>
            <div class="file-actions">
              <i
                class="jt-20-delete file-action file-remove"
                @click="removeRelatedFile(fileIndex)"
                title="删除文件"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 调解信息配置（仅资产包模式可编辑） -->
      <div v-if="editMode === 'asset' && editableFields.includes('field_mappings_detail')" class="config-section">
        <div class="section-header">
          <h3>调解信息配置</h3>
          <CustomButton @click="addField" :height="32" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>

        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div
            v-for="(field, index) in dynamicFields"
            :key="field.id || `field-${index}`"
            class="field-item">

            <!-- 字段头部：显示序号和删除按钮 -->
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton
                @click="removeField(index)"
                :height="28"
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>

            <!-- 字段配置 -->
            <div class="field-config">
              <!-- 字段类型 -->
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select
                  v-model="field.type"
                  style="width: 280px;">
                  <el-option label="文本输入" :value="FieldTypeEnum.TEXTAREA" />
                  <el-option label="日期选择" :value="FieldTypeEnum.DATE" />
                  <el-option label="金额输入" :value="FieldTypeEnum.AMOUNT" />
                </el-select>
              </div>

              <!-- 字段标题 -->
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <el-input
                  v-model="field.title"
                  placeholder="请输入字段标题"
                  style="width: 280px;" />
              </div>
            </div>

            <!-- 表达式编辑区域 -->
            <div class="expression-section">
              <label class="config-label">表达式编辑：</label>

              <!-- 自定义表达式编辑器 - 支持中文输入 -->
              <div class="custom-expression-editor">
                <el-input
                  v-model="field.expression"
                  placeholder="请输入表达式，如：合同号+10+借据号"
                  @input="createExpressionChangeHandler(index)"
                  @blur="createExpressionBlurHandler(index)"
                  class="expression-input"
                />

                <!-- 变量选择按钮 -->
                <div class="variable-buttons" v-if="expressionVariables.length > 0">
                  <span class="variable-label">可用变量：</span>
                  <el-button
                    v-for="variable in expressionVariables"
                    :key="variable.code"
                    size="small"
                    type="primary"
                    plain
                    @click="insertVariable(variable, index)"
                    class="variable-btn"
                  >
                    {{ variable.name }}
                  </el-button>
                </div>

                <!-- 使用说明 -->
                <div class="expression-help">
                  <el-text size="small" type="info">
                    提示：可以直接输入中文变量名，或点击上方按钮插入变量。支持 +、-、*、/ 等运算符。
                  </el-text>
                </div>
              </div>
            </div>

            <!-- 内容预览区域 -->
            <div class="preview-section">
              <!-- <label class="config-label"></label> -->
              内容预览：<div class="preview-content" v-loading="previewLoading">
                {{ field.preview }}
                <!-- <el-input
                  :value="field.preview || ''"
                  type="textarea"
                  :rows="2"
                  readonly
                  placeholder="表达式解析结果将在此显示"
                  class="preview-textarea"
                /> -->
              </div>
            </div>
          </div>
        </div>

        <div v-else class="empty-fields">
          <p>暂无字段配置，请点击"添加字段"开始配置</p>
        </div>
      </div>

      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.edit-dialog-content {
  .file-section, .config-section, .config-display-section {
    margin: 20px 0;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 8px 0;

      .field-label, h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
  }

  // 字段映射样式（参考EditDialog.vue）
  .field-mapping {
    .mapping-container {
      border: 1px solid #ebeef5;
      border-radius: 6px;
      overflow: hidden;

      .mapping-header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        background-color: #f5f7fa;

        .field-column,
        .file-column,
        .debtor-column {
          padding: 12px 16px;
          font-weight: 600;
          color: #606266;
          border-right: 1px solid #ebeef5;

          &:last-child {
            border-right: none;
          }
        }
      }

      .mapping-list {
        max-height: 400px;
        overflow-y: auto;

        .mapping-item {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          border-bottom: 1px solid #ebeef5;

          &:last-child {
            border-bottom: none;
          }

          .field-info {
            padding: 16px;
            border-right: 1px solid #ebeef5;
            display: flex;
            flex-direction: column;
            gap: 4px;

            .field-label {
              color: #303133;
              font-weight: 500;
              font-size: 14px;
            }
          }

          .column-select,
          .debtor-select {
            padding: 16px;
            display: flex;
            align-items: center;
            border-right: 1px solid #ebeef5;

            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }
  }

  .config-display-section {
    .fields-display {
      .field-display-item {
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        padding: 15px 20px;
        margin-bottom: 20px;
        background: #f9f9f9;

        .field-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .field-index {
            font-weight: bold;
            color: #909399;
            font-size: 16px;
            padding: 4px 12px;
            background-color: #f4f4f5;
            border-radius: 16px;
            border: 1px solid #dcdfe6;
          }
        }

        .field-config {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          margin-bottom: 10px;

          .config-row {
            display: flex;
            align-items: center;
            gap: 12px;
          }

          .config-label {
            min-width: 90px;
            font-size: 14px;
            color: #666;
            font-weight: 500;
          }
        }

        .field-preview {
          .config-label {
            display: block;
            margin-bottom: 12px;
            font-weight: bold;
            color: #333;
            font-size: 15px;
          }
        }
      }
    }
  }
  
  .file-list {
    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      margin-bottom: 8px;
      border-radius: 6px;
      background-color: #f0f0f0;

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        gap: 8px;
        
        .file-name {
          flex: 1;
          color: #333;
          font-weight: 500;
        }
        
        .file-type {
          font-size: 12px;
          color: #666;
        }
      }

      .file-actions {
        .file-remove {
          color: #f56c6c;
          cursor: pointer;
          padding: 4px;
          border-radius: 50%;
          transition: all 0.2s ease;

          &:hover {
            color: #fff;
            background-color: #f56c6c;
          }
        }
      }
    }
  }
  
  .fields-list {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: #fafafa;

      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }

      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 15px;

        .config-row {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .config-label {
          min-width: 90px;
        }
      }

      .expression-section {
        margin-bottom: 15px;

        .config-label {
          display: block;
          margin-bottom: 8px;
        }
      }

      .preview-section {
        margin-bottom: 8px;
      }
      /* .preview-section {
        .config-label {
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .preview-content {
          .preview-textarea {
            :deep(.el-textarea__inner) {
              background-color: #f5f7fa;
              border-color: #dcdfe6;
              color: #606266;
              resize: none;
            }
          }
        }
      } */

      .field-preview {
        .config-label {
          display: block;
          margin-bottom: 12px;
          font-weight: bold;
          color: #333;
          font-size: 15px;
        }
      }
    }
  }
  
  .empty-fields {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #ddd;
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
}
:deep(.editor-content){
  display: none;
}
:deep(.formula-editor){
  padding:0
}
:deep(.clear-button){
  font-size: 16px;
  background-color: #D94323;
}
:deep(.formula-input-container .input-tip){
  margin: 2px 13px;
  text-align: left;
}
:deep(.variable-code){
  display: none;
}

// 自定义表达式编辑器样式
.custom-expression-editor {
  .expression-input {
    margin-bottom: 12px;
  }

  .variable-buttons {
    margin-bottom: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;

    .variable-label {
      font-size: 12px;
      color: #666;
      margin-right: 8px;
      font-weight: 500;
    }

    .variable-btn {
      margin-right: 6px;
      margin-bottom: 4px;
      font-size: 12px;
      height: 24px;
      padding: 0 8px;
    }
  }

  .expression-help {
    .el-text {
      font-size: 12px;
      line-height: 1.4;
    }
  }
}
</style>
