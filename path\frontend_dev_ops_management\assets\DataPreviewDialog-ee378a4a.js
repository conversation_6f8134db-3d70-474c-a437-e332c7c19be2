/* empty css             */import{h as N,c as A}from"./headerCellStyle-17161c7c.js";import{_ as B}from"./construct-a2f67563.js";import{_ as L}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{P as v}from"./types-d67a131c.js";import{a as O,r as h,c as f,s as M,o as n,q as m,w as _,J as R,f as d,F as g,g as a,t as u,h as V,A as F,aI as J,n as y,D as $,P as q,aJ as z,E as w,N as H,K,M as U,p as j,m as G}from"./index-8a4876d8.js";import{_ as Q}from"./_plugin-vue_export-helper-c27b6911.js";const p=r=>(j("data-v-9ee4e3d0"),r=r(),G(),r),W={class:"preview-content"},X={class:"data-info"},Y={class:"info-item"},Z=p(()=>a("span",{class:"label"},"资产包名称：",-1)),ee={class:"value"},ae={class:"info-item"},te=p(()=>a("span",{class:"label"},"文件名称：",-1)),se={class:"value"},le={class:"table-wrapper"},ie={key:1,class:"no-data"},oe=p(()=>a("img",{src:B,alt:"无数据"},null,-1)),ne=p(()=>a("div",null,"暂无预览数据",-1)),re=[oe,ne],ce=O({__name:"DataPreviewDialog",props:{visible:{type:Boolean},previewType:{},packageId:{}},emits:["close"],setup(r,{emit:b}){const l=r,c=h(!1),i=h(null),k=f(()=>l.previewType===v.OPERATION?"运营数据预览":"原始数据预览"),D=f(()=>"calc(70vh - 200px)");M(()=>l.visible,t=>{t&&l.packageId&&I()});async function I(){var t,o;if(l.packageId){c.value=!0;try{let e;if(l.previewType===v.OPERATION?e=await q(l.packageId):e=await z(l.packageId),e.data.code===200)i.value=e.data.data;else{w.error(e.data.msg||"数据加载失败");return}}catch(e){console.error("预览数据加载失败:",e),w.error(((o=(t=e==null?void 0:e.response)==null?void 0:t.data)==null?void 0:o.msg)||"预览数据加载失败")}finally{c.value=!1}}}function S(){i.value=null,b("close")}function x(t,o){const e=t[o.key];if(e==null||e==="")return"-";switch(o.type){case"currency":return typeof e=="string"?e:`¥${e.toLocaleString()}`;case"number":return typeof e=="string"?e:e.toLocaleString();case"date":return e;case"text":default:return String(e)}}function C(t){return{textAlign:t||"left"}}return(t,o)=>{const e=H,P=K,T=U;return n(),m(L,{visible:t.visible,title:k.value,width:"90%","onUpdate:visible":S},{default:_(()=>[R((n(),d("div",W,[i.value?(n(),d(g,{key:0},[a("div",X,[a("div",Y,[Z,a("span",ee,u(i.value.package_name),1)]),a("div",ae,[te,a("span",se,u(i.value.file_name),1)])]),a("div",le,[V(P,{data:i.value.data,border:"",stripe:"","header-cell-style":y(N),"cell-style":y(A),"max-height":D.value,style:{width:"100%"}},{default:_(()=>[(n(!0),d(g,null,F(i.value.columns,s=>(n(),m(e,{key:s.key,prop:s.key,label:s.label,width:s.width,"min-width":s.width||120,align:s.align||"left"},{default:_(({row:E})=>[a("div",{style:J(C(s.align))},u(x(E,s)),5)]),_:2},1032,["prop","label","width","min-width","align"]))),128))]),_:1},8,["data","header-cell-style","cell-style","max-height"])])],64)):c.value?$("",!0):(n(),d("div",ie,re))])),[[T,c.value]])]),_:1},8,["visible","title"])}}});const me=Q(ce,[["__scopeId","data-v-9ee4e3d0"]]);export{me as D};
