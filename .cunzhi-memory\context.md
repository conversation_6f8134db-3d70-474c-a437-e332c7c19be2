# 项目上下文信息

- 用户需要修复 DataImportDialog.vue 和 ReuploadDialog.vue 两个弹框的文件上传显示问题：文件选择后没有正确显示文件名称。最终需要将文件上传功能封装成可复用组件。
- EditDialog.vue 编辑弹框已重构完成：1) 支持从 field_mappings_detail 获取 original_field_name 显示系统字段；2) 集成 getFieldConfig 接口作为下拉框数据源（key=id，value=field_name）；3) 支持根据 mapped_field_config.id 自动选中下拉框；4) 下拉框支持搜索、清空、允许空值；5) 保存时按照新格式构造数据：field_mappings[{package_name, creditor, field_mappings[{id, original_field_name, mapped_field_config_id}]}]
- ReuploadDialog.vue组件已完成功能实现：1) 添加fileName prop用于接收已上传文件名；2) 实现文件列表回显功能，支持单个或多个文件名显示；3) 保持原有Excel文件上传功能(.xlsx,.xls)；4) 接口调用使用正确的reset_mappings参数；5) 添加了文件列表的样式美化，包括Excel图标和文件名显示。组件符合项目复用性要求。
- 调解案件管理功能的正确组件关系：父组件是disposalPlan.vue，子组件是AddDisposalPlan.vue和EditDisposalPlan.vue，不是mediationInformation.vue相关组件
- 调解管理系统开发需求：1) mediationInformation.vue需要从模拟数据改为真实API调用getMediationPlan；2) 新增案件状态筛选功能，调用getStatusChoicesOptions接口；3) 调整表格列显示顺序；4) 修改AddMediationInformation.vue支持资产包选择和数据预览；5) 添加删除功能调用deleteMediationPlan接口
- AddMediationInformation.vue和AssetPackageSelector.vue组件间数据传递功能已实现：1) AssetPackageSelector存储getDataImportList完整数据，根据选中资产包名称匹配获取creditor_name和creditor字段；2) 确认时传递资产包ID、债权人信息、选中行序号给AddMediationInformation；3) AddMediationInformation接收并处理asset_package_id、creditor、creditor_name、asset_package_row_number参数；4) 更新了表单数据结构和提交逻辑以支持新的数据传递需求
- 调解案件编辑功能实施：需要修复EditMediationInformation.vue的数据回显、完善文件处理、修正数据提交参数、优化用户体验
- personnelDispatch.vue 编辑弹框需求：调解案件号和案件状态字段禁用，调解员下拉框可编辑，使用getUser API获取调解员列表，参考项目现有弹框设计风格
- DeleteConfirmDialog组件替换计划：第一阶段需要替换6个使用ElMessageBox.confirm的页面：fieldConfiguration.vue、dataImport.vue、creditor.vue、debtor.vue、caseShow.vue、debtor copy.vue；第二阶段需要替换4个使用DeleteTips组件的页面；mediationInformation.vue已完成替换无需处理
- 调解信息管理页面功能增强需求：1) 列表结构优化：添加序号列、全选复选框；2) 选择和发起功能：添加选择/发起按钮，复选框状态控制；3) 筛选功能增强：添加资产包和案件状态下拉筛选器；4) 操作列功能扩展：添加预览调解信息和预览调解方案内容按钮
- EditMediationInformationDialog编辑弹框已实现条件显示功能：1) 资产包tab模式：可编辑调解信息配置和相关文件，使用updateAttachmentsMediation接口；2) 调解案件tab模式：可编辑债务人信息和相关文件，使用editMediationCase接口；3) 其他字段均为只读显示；4) 父组件mediationInformation.vue的handleEditConfirm方法根据activeTab.value选择正确的保存接口
- 动态导入模块失败问题已解决：问题根因是开发服务器停止运行，mediationInformation.vue文件本身无问题，路由配置正确，需要重启开发服务器
- 用户反馈EditMediationInformationDialog.vue存在两个问题：1.VueExpressionEditor中文输入异常 2.逻辑处理类型单选框异常行为和默认值问题
- 用户反馈修复后VueExpressionEditor仍然不能输入中文，单选框仍然不能正确选中单个选项，需要更深入的修复方案
- 用户反馈VueExpressionEditor组件可以输入中文，因为上午还输入成功过，要求继续使用VueExpressionEditor组件而不是替换为el-input
- 用户反馈VueExpressionEditor仍然不能输入中文，单选框没有默认选择"文本格式化模式"，选中时两个单选框都被选中了，需要深度修复这两个问题
- 用户反馈VueExpressionEditor组件中文输入不稳定，一会可以输入一会不可以输入，需要核实本次修改是否有遗漏
- 用户反馈VueExpressionEditor组件输入中文时提示"请使用 @ 符号选择变量"，仍然不能输入中文，需要进一步优化
- 用户确认之前13:30前已经实现过VueExpressionEditor中文输入功能，现在又不能输入了，需要检查原因。变量有100多个，需要支持中文、加减乘除、数字、@符号选择变量
