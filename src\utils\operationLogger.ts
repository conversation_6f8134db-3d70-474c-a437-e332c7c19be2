import { addOperationLog } from '@/axios/system'
import { getMenuHierarchy } from './menuPermissions'
import type { OperationLogData } from '@/components/common/type'
import { ElMessage } from 'element-plus'
import { checkTokenExists } from './tokenValidator'

class OperationLogger {
  private menuList: any[] = []
  
  /**
   * 设置菜单列表用于层级识别
   */
  setMenuList(menuList: any[]) {
    this.menuList = menuList
  }
  
  /**
   * 记录用户操作日志
   * @param buttonName 按钮名称
   * @param customMenuName 自定义菜单名称（可选）
   */
  async logOperation(buttonName: string, customMenuName?: string) {
    try {
      // 检查token是否存在，如果不存在则不记录日志
      if (!checkTokenExists()) {
        return
      }

      const currentPath = window.location.pathname
      const menuName = customMenuName || getMenuHierarchy(currentPath, this.menuList)

      const logData: OperationLogData = {
        page_plate: menuName,
        button_name: buttonName,
        button_type: buttonName,
        page_url: currentPath,
      }
      
      // 调用后端接口记录日志
      const response = await addOperationLog(logData)
      
      // 输出响应结果
      console.log('操作日志记录成功:', response?.data)
      
    } catch (error) {
      // 日志记录失败不应该影响用户正常操作，只在控制台输出错误
      console.error('操作日志记录失败:', error)
    }
  }
  
  /**
   * 从DOM元素中提取按钮名称
   */
  extractButtonName(element: HTMLElement): string {
    // 优先获取元素的文本内容
    let buttonName = element.textContent?.trim() || ''
    
    // 如果文本为空，尝试获取title属性
    if (!buttonName) {
      buttonName = element.getAttribute('title') || ''
    }
    
    // 如果仍为空，尝试获取aria-label属性
    if (!buttonName) {
      buttonName = element.getAttribute('aria-label') || ''
    }
    
    // 清理按钮名称，移除多余的空格和换行
    buttonName = buttonName.replace(/\s+/g, ' ').trim()
    
    // 如果名称过长，截取前20个字符
    if (buttonName.length > 20) {
      buttonName = buttonName.substring(0, 20) + '...'
    }
    
    return buttonName || '未知按钮'
  }
}

// 导出单例实例
export const operationLogger = new OperationLogger()

// 导出便捷方法
export function logButtonClick(buttonName: string, customMenuName?: string) {
  operationLogger.logOperation(buttonName, customMenuName)
} 