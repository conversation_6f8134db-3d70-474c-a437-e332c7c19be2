/* empty css             */import{C as Ve,c as Ce,h as ke}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{a as ae,r as p,s as _e,o as r,q as x,w as s,J as te,f as V,h as a,F as O,A as T,g as n,i as g,D as L,E as U,Q as se,S as ie,k as ve,j as fe,a4 as ye,a5 as he,l as ge,M as oe,p as ne,m as re,a6 as de,z as we,I as $e,n as pe,a7 as me,a8 as xe,a9 as Ue,aa as Ee,ab as Ae,K as Oe,L as Te,t as ee,ac as Pe,ad as ze,ae as Se,N as je}from"./index-8a4876d8.js";import{C as Y}from"./CustomButton-ea16d5c5.js";/* empty css                     *//* empty css                  *//* empty css                 */import{_ as be}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{_ as ue}from"./_plugin-vue_export-helper-c27b6911.js";const Z=k=>(ne("data-v-c5c54281"),k=k(),re(),k),Ie={class:"add-creditor-form"},Be={class:"contact-list"},qe={class:"contact-input-group"},Le=Z(()=>n("i",{class:"el-icon-plus"},null,-1)),Fe={class:"contact-list"},Ne={class:"contact-input-group"},Re=Z(()=>n("i",{class:"el-icon-plus"},null,-1)),Ke={class:"contact-list"},Me={class:"contact-input-group"},Je={class:"contact-controls"},Qe=Z(()=>n("i",{class:"el-icon-plus"},null,-1)),Ge={class:"dialog-footer"},He=Z(()=>n("i",{class:"jt-20-ensure"},null,-1)),We=Z(()=>n("i",{class:"jt-20-delete"},null,-1)),Xe=ae({__name:"AddCreditor",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm"],setup(k,{emit:S}){const j=k,$=p(),w=p(!1),y=p({creditor_type:"",creditor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]}),m=p([{phone:"",phone_type:"",is_primary:!0}]),_=p([{email:"",email_type:"",is_primary:!0}]),v=p([{address:"",address_type:"",is_primary:!0}]),P=p([]),z=p([]),E=p(!1);async function F(){E.value=!0;const{data:e}=await de(),{state:l,msg:i}=e;if(l==="success"){const{creditor_types:f,id_types:d}=e.data;if(e.data){const u=Object.entries(f).map(([h,o])=>({label:o,value:h}));P.value=u;const C=Object.entries(d).map(([h,o])=>({label:o,value:h}));z.value=C}}else U.error(i);E.value=!1}const I={creditor_type:[{required:!0,message:"请选择债权人类型",trigger:"change"}],creditor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};_e(()=>j.visible,async e=>{e&&(await F(),N())});function N(){y.value={creditor_name:"",creditor_type:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]},m.value=[{phone:"",phone_type:"",is_primary:!0}],_.value=[{email:"",email_type:"",is_primary:!0}],v.value=[{address:"",address_type:"",is_primary:!0}],$.value&&$.value.clearValidate()}function A(){S("update:visible")}function B(){m.value.push({phone:"",phone_type:"",is_primary:!1})}function R(e){m.value.length>1&&(m.value.splice(e,1),!m.value.find(l=>l.is_primary)&&m.value.length>0&&(m.value[0].is_primary=!0))}function K(e){m.value.forEach((l,i)=>{l.is_primary=i===e})}function M(){_.value.push({email:"",email_type:"",is_primary:!1})}function J(e){_.value.length>1&&(_.value.splice(e,1),!_.value.find(l=>l.is_primary)&&_.value.length>0&&(_.value[0].is_primary=!0))}function Q(e){_.value.forEach((l,i)=>{l.is_primary=i===e})}function G(){v.value.push({address:"",address_type:"",is_primary:!1})}function H(e){v.value.length>1&&(v.value.splice(e,1),!v.value.find(l=>l.is_primary)&&v.value.length>0&&(v.value[0].is_primary=!0))}function W(e){v.value.forEach((l,i)=>{l.is_primary=i===e})}async function q(){if($.value){w.value=!0;try{if(!await new Promise(u=>{$.value.validate(C=>{u(C)})}))return;const l=m.value.filter(u=>u.phone.trim()),i=_.value.filter(u=>u.email.trim()),f=v.value.filter(u=>u.address.trim()),d={...y.value,phones:l.length>0?l:void 0,emails:i.length>0?i:void 0,addresses:f.length>0?f:void 0};S("confirm",d)}catch(e){console.error("表单验证失败:",e),U.error(e)}finally{w.value=!1}}}return(e,l)=>{const i=se,f=ie,d=ve,u=fe,C=ye,h=he,o=ge,X=oe;return r(),x(be,{visible:j.visible,title:"新增债权人",width:"600px","onUpdate:visible":A},{default:s(()=>[te((r(),V("div",Ie,[a(o,{ref_key:"formRef",ref:$,model:y.value,rules:I,"label-width":"120px","label-position":"right"},{default:s(()=>[a(d,{label:"类型",prop:"creditor_type"},{default:s(()=>[a(f,{modelValue:y.value.creditor_type,"onUpdate:modelValue":l[0]||(l[0]=t=>y.value.creditor_type=t),placeholder:"请选择债权人类型",loading:E.value,style:{width:"100%"}},{default:s(()=>[(r(!0),V(O,null,T(P.value,t=>(r(),x(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(d,{label:"姓名",prop:"creditor_name"},{default:s(()=>[a(u,{modelValue:y.value.creditor_name,"onUpdate:modelValue":l[1]||(l[1]=t=>y.value.creditor_name=t),placeholder:"请输入债权人姓名"},null,8,["modelValue"])]),_:1}),a(d,{label:"证件类型",prop:"id_type"},{default:s(()=>[a(f,{modelValue:y.value.id_type,"onUpdate:modelValue":l[2]||(l[2]=t=>y.value.id_type=t),placeholder:"请选择证件类型",loading:E.value,style:{width:"100%"}},{default:s(()=>[(r(!0),V(O,null,T(z.value,t=>(r(),x(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(d,{label:"证件号码",prop:"id_number"},{default:s(()=>[a(u,{modelValue:y.value.id_number,"onUpdate:modelValue":l[3]||(l[3]=t=>y.value.id_number=t),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),a(d,{label:"联系电话"},{default:s(()=>[n("div",Be,[(r(!0),V(O,null,T(m.value,(t,b)=>(r(),V("div",{key:b,class:"contact-item"},[n("div",qe,[a(u,{modelValue:t.phone,"onUpdate:modelValue":c=>t.phone=c,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(C,{modelValue:t.is_primary,"onUpdate:modelValue":c=>t.is_primary=c,label:!0,onChange:c=>K(b),class:"primary-radio"},{default:s(()=>[g("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),m.value.length>1?(r(),x(h,{key:0,onClick:c=>R(b),type:"danger",text:"",size:"small"},{default:s(()=>[g("删除")]),_:2},1032,["onClick"])):L("",!0)])]))),128)),a(h,{onClick:B,type:"primary",text:"",size:"small"},{default:s(()=>[Le,g(" 添加电话 ")]),_:1})])]),_:1}),a(d,{label:"联系邮箱"},{default:s(()=>[n("div",Fe,[(r(!0),V(O,null,T(_.value,(t,b)=>(r(),V("div",{key:b,class:"contact-item"},[n("div",Ne,[a(u,{modelValue:t.email,"onUpdate:modelValue":c=>t.email=c,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(C,{modelValue:t.is_primary,"onUpdate:modelValue":c=>t.is_primary=c,label:!0,onChange:c=>Q(b),class:"primary-radio"},{default:s(()=>[g("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),_.value.length>1?(r(),x(h,{key:0,onClick:c=>J(b),type:"danger",text:"",size:"small"},{default:s(()=>[g("删除")]),_:2},1032,["onClick"])):L("",!0)])]))),128)),a(h,{onClick:M,type:"primary",text:"",size:"small"},{default:s(()=>[Re,g(" 添加邮箱 ")]),_:1})])]),_:1}),a(d,{label:"联系地址"},{default:s(()=>[n("div",Ke,[(r(!0),V(O,null,T(v.value,(t,b)=>(r(),V("div",{key:b,class:"contact-item"},[n("div",Me,[a(u,{modelValue:t.address,"onUpdate:modelValue":c=>t.address=c,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),n("div",Je,[a(C,{modelValue:t.is_primary,"onUpdate:modelValue":c=>t.is_primary=c,label:!0,onChange:c=>W(b),class:"primary-radio"},{default:s(()=>[g("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),v.value.length>1?(r(),x(h,{key:0,onClick:c=>H(b),type:"danger",text:"",size:"small"},{default:s(()=>[g("删除")]),_:2},1032,["onClick"])):L("",!0)])])]))),128)),a(h,{onClick:G,type:"primary",text:"",size:"small"},{default:s(()=>[Qe,g(" 添加地址 ")]),_:1})])]),_:1})]),_:1},8,["model"]),n("div",Ge,[a(Y,{onClick:q,loading:w.value,height:34,"btn-type":"blue"},{default:s(()=>[He,g("确认 ")]),_:1},8,["loading"]),a(Y,{onClick:A,height:34},{default:s(()=>[We,g("取消 ")]),_:1})])])),[[X,w.value]])]),_:1},8,["visible"])}}});const Ye=ue(Xe,[["__scopeId","data-v-c5c54281"]]),D=k=>(ne("data-v-10c9d223"),k=k(),re(),k),Ze={class:"edit-creditor-form"},De={class:"contact-list"},el={class:"contact-input-group"},ll=D(()=>n("i",{class:"el-icon-plus"},null,-1)),al={class:"contact-list"},tl={class:"contact-input-group"},sl=D(()=>n("i",{class:"el-icon-plus"},null,-1)),il={class:"contact-list"},ol={class:"contact-input-group"},nl={class:"contact-controls"},rl=D(()=>n("i",{class:"el-icon-plus"},null,-1)),dl={class:"dialog-footer"},ul=D(()=>n("i",{class:"jt-20-ensure"},null,-1)),cl=D(()=>n("i",{class:"jt-20-delete"},null,-1)),pl=ae({__name:"EditCreditor",props:{visible:{type:Boolean,default:!1},creditorData:{default:null}},emits:["update:visible","confirm"],setup(k,{emit:S}){const j=k,$=p(),w=p(!1),y=p({id:0,creditor_type:"",creditor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]}),m=p([{phone:"",phone_type:"",is_primary:!0}]),_=p([{email:"",email_type:"",is_primary:!0}]),v=p([{address:"",address_type:"",is_primary:!0}]),P=p([]),z=p([]),E=p(!1);async function F(){E.value=!0;const{data:e}=await de(),{state:l,msg:i}=e;if(l==="success"){const{creditor_types:f,id_types:d}=e.data;if(e.data){const u=Object.entries(f).map(([h,o])=>({label:o,value:h}));P.value=u;const C=Object.entries(d).map(([h,o])=>({label:o,value:h}));z.value=C}}else U.error(i);E.value=!1}const I={creditor_type:[{required:!0,message:"请选择债权人类型",trigger:"change"}],creditor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};_e(()=>[j.visible,j.creditorData],async([e,l])=>{e&&l&&typeof l=="object"&&(await F(),N(l))},{immediate:!0});function N(e){y.value={id:e.id,creditor_type:e.creditor_type,creditor_name:e.creditor_name,id_type:e.id_type,id_number:e.id_number,phones:e.phones||[],emails:e.emails||[],addresses:e.addresses||[]},e.phones&&e.phones.length>0?m.value=[...e.phones]:e.phone?m.value=[{phone:e.phone,phone_type:"",is_primary:!0}]:m.value=[{phone:"",phone_type:"",is_primary:!0}],e.emails&&e.emails.length>0?_.value=[...e.emails]:e.email?_.value=[{email:e.email,email_type:"",is_primary:!0}]:_.value=[{email:"",email_type:"",is_primary:!0}],e.addresses&&e.addresses.length>0?v.value=[...e.addresses]:e.address?v.value=[{address:e.address,address_type:"",is_primary:!0}]:v.value=[{address:"",address_type:"",is_primary:!0}],$.value&&$.value.clearValidate()}function A(){S("update:visible")}function B(){m.value.push({phone:"",phone_type:"",is_primary:!1})}function R(e){m.value.length>1&&(m.value.splice(e,1),!m.value.find(l=>l.is_primary)&&m.value.length>0&&(m.value[0].is_primary=!0))}function K(e){m.value.forEach((l,i)=>{l.is_primary=i===e})}function M(){_.value.push({email:"",email_type:"",is_primary:!1})}function J(e){_.value.length>1&&(_.value.splice(e,1),!_.value.find(l=>l.is_primary)&&_.value.length>0&&(_.value[0].is_primary=!0))}function Q(e){_.value.forEach((l,i)=>{l.is_primary=i===e})}function G(){v.value.push({address:"",address_type:"",is_primary:!1})}function H(e){v.value.length>1&&(v.value.splice(e,1),!v.value.find(l=>l.is_primary)&&v.value.length>0&&(v.value[0].is_primary=!0))}function W(e){v.value.forEach((l,i)=>{l.is_primary=i===e})}async function q(){if($.value){w.value=!0;try{if(!await new Promise(u=>{$.value.validate(C=>{u(C)})}))return;const l=m.value.filter(u=>u.phone.trim()),i=_.value.filter(u=>u.email.trim()),f=v.value.filter(u=>u.address.trim()),d={...y.value,phones:l.length>0?l:void 0,emails:i.length>0?i:void 0,addresses:f.length>0?f:void 0};S("confirm",d)}catch(e){console.error("表单验证失败:",e),U.error(e)}finally{w.value=!1}}}return(e,l)=>{const i=se,f=ie,d=ve,u=fe,C=ye,h=he,o=ge,X=oe;return r(),x(be,{visible:j.visible,title:"编辑债权人",width:"600px","onUpdate:visible":A},{default:s(()=>[te((r(),V("div",Ze,[a(o,{ref_key:"formRef",ref:$,model:y.value,rules:I,"label-width":"120px","label-position":"right"},{default:s(()=>[a(d,{label:"类型",prop:"creditor_type"},{default:s(()=>[a(f,{modelValue:y.value.creditor_type,"onUpdate:modelValue":l[0]||(l[0]=t=>y.value.creditor_type=t),placeholder:"请选择债权人类型",loading:E.value,style:{width:"100%"}},{default:s(()=>[(r(!0),V(O,null,T(P.value,t=>(r(),x(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(d,{label:"姓名",prop:"creditor_name"},{default:s(()=>[a(u,{modelValue:y.value.creditor_name,"onUpdate:modelValue":l[1]||(l[1]=t=>y.value.creditor_name=t),placeholder:"请输入债权人姓名"},null,8,["modelValue"])]),_:1}),a(d,{label:"证件类型",prop:"id_type"},{default:s(()=>[a(f,{modelValue:y.value.id_type,"onUpdate:modelValue":l[2]||(l[2]=t=>y.value.id_type=t),placeholder:"请选择证件类型",loading:E.value,style:{width:"100%"}},{default:s(()=>[(r(!0),V(O,null,T(z.value,t=>(r(),x(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(d,{label:"证件号码",prop:"id_number"},{default:s(()=>[a(u,{modelValue:y.value.id_number,"onUpdate:modelValue":l[3]||(l[3]=t=>y.value.id_number=t),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),a(d,{label:"联系电话"},{default:s(()=>[n("div",De,[(r(!0),V(O,null,T(m.value,(t,b)=>(r(),V("div",{key:b,class:"contact-item"},[n("div",el,[a(u,{modelValue:t.phone,"onUpdate:modelValue":c=>t.phone=c,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(C,{modelValue:t.is_primary,"onUpdate:modelValue":c=>t.is_primary=c,label:!0,onChange:c=>K(b),class:"primary-radio"},{default:s(()=>[g("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),m.value.length>1?(r(),x(h,{key:0,onClick:c=>R(b),type:"danger",text:"",size:"small"},{default:s(()=>[g("删除")]),_:2},1032,["onClick"])):L("",!0)])]))),128)),a(h,{onClick:B,type:"primary",text:"",size:"small"},{default:s(()=>[ll,g(" 添加电话 ")]),_:1})])]),_:1}),a(d,{label:"联系邮箱"},{default:s(()=>[n("div",al,[(r(!0),V(O,null,T(_.value,(t,b)=>(r(),V("div",{key:b,class:"contact-item"},[n("div",tl,[a(u,{modelValue:t.email,"onUpdate:modelValue":c=>t.email=c,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(C,{modelValue:t.is_primary,"onUpdate:modelValue":c=>t.is_primary=c,label:!0,onChange:c=>Q(b),class:"primary-radio"},{default:s(()=>[g("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),_.value.length>1?(r(),x(h,{key:0,onClick:c=>J(b),type:"danger",text:"",size:"small"},{default:s(()=>[g("删除")]),_:2},1032,["onClick"])):L("",!0)])]))),128)),a(h,{onClick:M,type:"primary",text:"",size:"small"},{default:s(()=>[sl,g(" 添加邮箱 ")]),_:1})])]),_:1}),a(d,{label:"联系地址"},{default:s(()=>[n("div",il,[(r(!0),V(O,null,T(v.value,(t,b)=>(r(),V("div",{key:b,class:"contact-item"},[n("div",ol,[a(u,{modelValue:t.address,"onUpdate:modelValue":c=>t.address=c,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),n("div",nl,[a(C,{modelValue:t.is_primary,"onUpdate:modelValue":c=>t.is_primary=c,label:!0,onChange:c=>W(b),class:"primary-radio"},{default:s(()=>[g("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),v.value.length>1?(r(),x(h,{key:0,onClick:c=>H(b),type:"danger",text:"",size:"small"},{default:s(()=>[g("删除")]),_:2},1032,["onClick"])):L("",!0)])])]))),128)),a(h,{onClick:G,type:"primary",text:"",size:"small"},{default:s(()=>[rl,g(" 添加地址 ")]),_:1})])]),_:1})]),_:1},8,["model"]),n("div",dl,[a(Y,{onClick:q,loading:w.value,height:34,"btn-type":"blue"},{default:s(()=>[ul,g("确认 ")]),_:1},8,["loading"]),a(Y,{onClick:A,height:34},{default:s(()=>[cl,g("取消 ")]),_:1})])])),[[X,w.value]])]),_:1},8,["visible"])}}});const ml=ue(pl,[["__scopeId","data-v-10c9d223"]]),ce=k=>(ne("data-v-11f0a051"),k=k(),re(),k),_l={class:"creditor-management-page"},vl={class:"search-header"},fl={class:"search-row"},yl={class:"search-item"},hl={class:"search-item"},gl=ce(()=>n("label",null,"债权人类型",-1)),bl={class:"search-item"},Vl=ce(()=>n("label",null,"证件类型",-1)),Cl={class:"search-item"},kl=ce(()=>n("i",{class:"jt-20-add"},null,-1)),wl={class:"table-container"},$l={class:"operation-buttons"},xl=["onClick"],Ul=["onClick"],El={key:0,class:"pagination-wrapper"},le=10,Al=ae({__name:"creditor",setup(k){const S=p(!1),j=p([]),$=p(0),w=p(1),y=p(""),m=p(""),_=p(""),v=p(!1),P=p(!1),z=p(null),E=p([]),F=p([]),I=p(!1);async function N(){I.value=!0;const{data:e}=await de(),{state:l,msg:i}=e;if(l==="success"){const{creditor_types:f,id_types:d}=e.data;if(e.data){const u=Object.entries(f).map(([h,o])=>({label:o,value:h}));E.value=u;const C=Object.entries(d).map(([h,o])=>({label:o,value:h}));F.value=C}}else U.error(i);I.value=!1}async function A(){if(!me()){U.error("登录状态已失效，请重新登录");return}S.value=!0;const e={page:w.value,page_size:le};y.value.trim()&&(e.search=y.value.trim()),m.value&&(e.creditor_type=m.value),_.value&&(e.id_type=_.value);const{data:l}=await xe(e),{state:i,msg:f}=l;i==="success"?(j.value=l.data.results,$.value=l.data.count):U.error(f),S.value=!1}function B(){Ue("搜索","债权人管理"),w.value=1,A()}function R(e){w.value=e,A()}function K(){v.value=!0}function M(){v.value=!1}async function J(e){const{data:l}=await Ee(e),{state:i,msg:f}=l;i==="success"&&(U.success(f),v.value=!1,w.value=1,A())}async function Q(e){const{data:l}=await Pe(e.id),{state:i,msg:f}=l;i==="success"?(z.value=l.data,P.value=!0):U.error(f)}function G(){P.value=!1,z.value=null}async function H(e){const{data:l}=await Ae(e,e.id),{state:i,msg:f}=l;i==="success"&&(U.success(f),P.value=!1,z.value=null,A())}async function W(e){try{if(!me()){U.error("登录状态已失效，请重新登录");return}await ze.confirm(`确定要删除债权人"${e.creditor_name}"吗？`,"删除确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}),await Se(e.id),U.success("删除成功"),A()}catch(l){U.error(l)}}function q(e,l){if(!e||e.length===0)return"";const i=e.find(f=>f.is_primary);return i?i[l]||"":e[0][l]}return we(async()=>{await N(),await A()}),(e,l)=>{const i=se,f=ie,d=je,u=Oe,C=Te,h=oe;return r(),V("div",_l,[n("div",vl,[n("div",fl,[n("div",yl,[a(Ve,{modelValue:y.value,"onUpdate:modelValue":l[0]||(l[0]=o=>y.value=o),placeholder:"搜索姓名、证件号码",onKeyup:$e(B,["enter"]),onClick:B},null,8,["modelValue","onKeyup"])]),n("div",hl,[gl,a(f,{modelValue:m.value,"onUpdate:modelValue":l[1]||(l[1]=o=>m.value=o),placeholder:"债权人类型",clearable:"",onChange:B,loading:I.value,style:{width:"200px"}},{default:s(()=>[(r(!0),V(O,null,T(E.value,o=>(r(),x(i,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),n("div",bl,[Vl,a(f,{modelValue:_.value,"onUpdate:modelValue":l[2]||(l[2]=o=>_.value=o),placeholder:"证件类型",clearable:"",onChange:B,loading:I.value,style:{width:"140px"}},{default:s(()=>[(r(!0),V(O,null,T(F.value,o=>(r(),x(i,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),n("div",Cl,[a(Y,{onClick:K,height:34},{default:s(()=>[kl,g("新增债权人 ")]),_:1})])])]),n("div",wl,[te((r(),x(u,{data:j.value,border:"",style:{width:"100%"},"cell-style":pe(Ce),"header-cell-style":pe(ke)},{default:s(()=>[a(d,{type:"index",label:"序号",width:"80",align:"center"},{default:s(({$index:o})=>[g(ee(le*(w.value-1)+o+1),1)]),_:1}),a(d,{label:"类型",width:"140",align:"center",prop:"creditor_type_cn"}),a(d,{align:"center",prop:"creditor_name",label:"姓名","min-width":"200"}),a(d,{label:"证件类型",width:"120",align:"center",prop:"id_type_cn"}),a(d,{align:"center",prop:"id_number",label:"证件号码","min-width":"200"}),a(d,{align:"center",label:"联系电话",width:"130"},{default:s(({row:o})=>[g(ee(q(o.phones,"phone")),1)]),_:1}),a(d,{align:"center",label:"联系邮箱",width:"180"},{default:s(({row:o})=>[g(ee(q(o.emails,"email")),1)]),_:1}),a(d,{align:"center",label:"联系地址","min-width":"200"},{default:s(({row:o})=>[g(ee(q(o.addresses,"address")),1)]),_:1}),a(d,{label:"操作",width:"180",align:"center",fixed:"right"},{default:s(({row:o})=>[n("div",$l,[n("div",{onClick:X=>Q(o),class:"operation-btn edit-btn"},"编辑",8,xl),n("div",{onClick:X=>W(o),class:"operation-btn delete-btn"},"删除",8,Ul)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[h,S.value]]),$.value>0?(r(),V("div",El,[a(C,{background:"",layout:"prev, pager, next",total:$.value,"current-page":w.value,"page-size":le,onCurrentChange:R},null,8,["total","current-page"])])):L("",!0)]),a(Ye,{visible:v.value,"onUpdate:visible":M,onConfirm:J},null,8,["visible"]),a(ml,{visible:P.value,"creditor-data":z.value,"onUpdate:visible":G,onConfirm:H},null,8,["visible","creditor-data"])])}}});const Nl=ue(Al,[["__scopeId","data-v-11f0a051"]]);export{Nl as default};
