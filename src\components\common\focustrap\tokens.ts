import type { InjectionKey, Ref } from "vue"

export const FOCUSOUT_PREVENTED = 'focus-trap.focusout-prevented'

export const FOCUSOUT_PREVENTED_OPTS: EventInit = {
  cancelable: true,
  bubbles: false,
}

export type FocusTrapInjectionContext = {
  focusTrapRef: Ref<HTMLElement | undefined>
  onKeydown: (e: KeyboardEvent) => void
}
const key = Symbol('elFocusTrap')
export const FOCUS_TRAP_INJECTION_KEY: InjectionKey<FocusTrapInjectionContext> = key