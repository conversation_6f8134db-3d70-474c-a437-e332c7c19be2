import './assets/styles/common.scss'
import './assets/styles/icons.scss'
import './assets/styles/customElement.scss'
import './assets/styles/commonClass.scss'
import { ElCollapseTransition } from 'element-plus'
import 'element-plus/theme-chalk/el-message.css'
import 'element-plus/theme-chalk/el-message.css';
import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入自定义指令
import logClickDirective from './directives/logClick'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 注册全局指令
app.use(logClickDirective)

app.component(ElCollapseTransition.name, ElCollapseTransition)

app.mount('#app')
