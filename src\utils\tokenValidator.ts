/**
 * Token验证工具类
 * 提供统一的token检查和管理功能
 */

/**
 * 检查token是否存在的统一函数
 * 支持从URL参数和sessionStorage获取token
 * @returns {boolean} token是否存在
 */
export function checkTokenExists(): boolean {
  // 获取URL参数中的token
  const urlParams = new URLSearchParams(window.location.search)
  const urlToken = urlParams.get('token')
  const urlTokenType = urlParams.get('token_type')
  
  // 获取sessionStorage中的token
  const sessionToken = sessionStorage.getItem('access_token')
  const sessionTokenType = sessionStorage.getItem('token_type')
  
  // 如果URL参数或sessionStorage中有token，则认为token存在
  return !!(urlToken && urlTokenType) || !!(sessionToken && sessionTokenType)
}

/**
 * 获取当前有效的token信息
 * 优先从URL参数获取，其次从sessionStorage获取
 * @returns {object} token信息对象
 */
export function getCurrentToken(): { token: string | null, tokenType: string | null } {
  // 获取URL参数中的token
  const urlParams = new URLSearchParams(window.location.search)
  const urlToken = urlParams.get('token')
  const urlTokenType = urlParams.get('token_type')
  
  if (urlToken && urlTokenType) {
    return { token: urlToken, tokenType: urlTokenType }
  }
  
  // 获取sessionStorage中的token
  const sessionToken = sessionStorage.getItem('access_token')
  const sessionTokenType = sessionStorage.getItem('token_type')
  
  return { token: sessionToken, tokenType: sessionTokenType }
}

/**
 * 清除所有token相关信息
 */
export function clearTokens(): void {
  sessionStorage.removeItem('access_token')
  sessionStorage.removeItem('refresh_token')
  sessionStorage.removeItem('token_type')
  sessionStorage.removeItem('username')
  sessionStorage.removeItem('group_name')
  sessionStorage.removeItem('role_name')
  sessionStorage.removeItem('user_all_permissions')
}

/**
 * 检查token是否有效（简单检查格式）
 * @param token - 需要检查的token
 * @returns {boolean} token是否有效
 */
export function isValidTokenFormat(token: string): boolean {
  // 简单的token格式检查：非空且长度大于10
  return typeof token === 'string' && token.trim().length > 10
} 