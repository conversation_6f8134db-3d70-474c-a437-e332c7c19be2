<script lang="ts" setup>
import { onMounted, ref, type Ref } from 'vue'
import CustomButton from '@/components/common/CustomButton.vue';
import CustomInput from '@/components/common/CustomInput.vue';
import AddMediationInformation from '../../dialogs/AddMediationInformation.vue';
import EditMediationInformation from '../../dialogs/EditMediationInformation(废弃）.vue';
import DeleteConfirmDialog from '@/components/common/dialog/DeleteConfirmDialog.vue';
import { headerCellStyle, cellStyle } from "@/common/functions/headerCellStyle"
import type { Mediation, MediationParams, AddMediationParams, EditMediationParams, MediationField } from '../../auth/type';
import { FieldType, CaseStatus } from '../../auth/type';
import { getMediationCase, addMediationCase, getMediationCaseDetail, editMediationCase, deleteMediationCase } from '@/axios/system'
import { ElMessage } from 'element-plus';

// 选中的方案行数据
const selectRow: Ref<Mediation> = ref({
  id: '',
  title: '',
  fields: [],
  caseStatus: CaseStatus.PENDING
})

// 分页和搜索
const total = ref(0)
const page = ref(1)
const page_size = 10
const search = ref('')
const planList = ref<Mediation[]>([])
const loading = ref(false)

// 案件状态筛选
const case_status = ref('')
// const statusOptions = ref<Array<{label: string, value: string}>>([])

// 对话框控制
const showAdd = ref(false)
const showEdit = ref(false)
const showDelete = ref(false)

// 获取案件状态选项
/* async function getStatusOptions() {
  try {
    const { data } = await getStatusChoicesOptions()
    const { state, msg } = data
    if (state === 'success') {
      statusOptions.value = data.data.map((item: any) => ({
        label: item.label,
        value: item.value
      }))
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error('获取状态选项失败:', error)
    ElMessage.error('获取状态选项失败')
  }
} */
// 重置到第一页并搜索
function setPage1() {
  page.value = 1
  searchPlanList()
}

// 搜索调解计划列表
async function searchPlanList() {
  loading.value = true
  try {
    const params: MediationParams = {
      page: page.value,
      page_size,
      search: search.value
    }

    // 如果有案件状态筛选，添加到参数中
    if (case_status.value) {
      (params as any).case_status = case_status.value
    }

    const { data } = await getMediationCase(params)
    const { state, msg } = data

    if (state === 'success') {
      const { results, count } = data.data
      planList.value = results
      total.value = count
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error('获取调解计划列表失败:', error)
    ElMessage.error('获取调解计划列表失败')
  } finally {
    loading.value = false
  }
}

// 分页改变
function pageChanged(p: number) {
  page.value = p
  searchPlanList()
}



// 打开新增方案对话框
function openAddPlanDialog() {
  showAdd.value = true 
}

// 提交新增调解计划
async function submitAddPlan(params: AddMediationParams) {
  try {
    const { data } = await addMediationCase(params)
    const { state, msg } = data

    if (state === 'success') {
      ElMessage.success('新增成功')
      showAdd.value = false
      setPage1()
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error('新增调解计划失败:', error)
    ElMessage.error('新增失败')
  }
}

// 打开编辑方案对话框
async function openEditPlanDialog(row: Mediation) {
  const { data } = await getMediationCaseDetail(Number(row.id))
  const { state, msg } = data
  if (state === 'success') {
    selectRow.value = data.data
  } else {
    ElMessage.error(msg)
  }
  showEdit.value = true
}

// 提交编辑调解计划
async function submitEditPlan(params: EditMediationParams) {
  const { data } = await editMediationCase(params, Number(params.id))
  const { state, msg } = data

  if (state === 'success') {
    ElMessage.success(msg)
    showEdit.value = false
    searchPlanList()
  } else {
    ElMessage.error(msg || '编辑失败')
  }
}

// 打开删除确认对话框
function openEnsureDeleteDialog(row: Mediation) {
  selectRow.value = { ...row }
  showDelete.value = true
}

// 删除调解计划
async function deletePlanRow() {
  if (!selectRow.value.id) {
    ElMessage.error('缺少必要参数')
    return
  }

  const { data } = await deleteMediationCase(Number(selectRow.value.id))
  const { state, msg } = data

  if (state === 'success') {
    ElMessage.success(msg)
    showDelete.value = false
    searchPlanList()
  } else {
    ElMessage.error(msg || '删除失败')
  }
}

// 关闭编辑对话框
function closeEditDialog() {
  showEdit.value = false
  selectRow.value = {
    id: '',
    title: '',
    fields: [],
    caseStatus: CaseStatus.PENDING
  }
}

// 获取案件状态标签
function getCaseStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'mediating': '调解中',
    'completed': '已完成',
    'closed': '已关闭'
  }
  return statusMap[status] || status
}

// 获取案件状态类型（用于el-tag的type属性）
function getCaseStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'mediating': 'primary',
    'completed': 'success',
    'closed': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取公证状态标签
function getNotarizationLabel(status: string): string {
  const statusMap: Record<string, string> = {
    'not_required': '无需公证',
    'pending': '待公证',
    'in_progress': '公证中',
    'completed': '已公证',
    'rejected': '公证失败'
  }
  return statusMap[status] || '未知'
}

// 获取公证状态类型
function getNotarizationType(status: string): string {
  const typeMap: Record<string, string> = {
    'not_required': 'info',
    'pending': 'warning',
    'in_progress': 'primary',
    'completed': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}



// 格式化字段类型显示
/* function getFieldTypeIcon(type: FieldType): string {
  const iconMap = {
    [FieldType.TEXTAREA]: '📝',
    [FieldType.TEXTAREA]: '📄',
    [FieldType.DATE]: '📅',
    [FieldType.AMOUNT]: '💰',
    [FieldType.FILE]: '📎'
  }
  return iconMap[type] || '📝'
} */

// 预览JSON数据
/* function previewJsonData(row: Mediation) {
  try {
    const jsonData = JSON.parse(row.jsonData)
    ElMessage({
      message: `方案JSON预览：\n${JSON.stringify(jsonData, null, 2)}`,
      type: 'info',
      duration: 8000,
      showClose: true
    })
  } catch (error) {
    ElMessage.error('JSON数据格式错误')
  }
} */

// 组件挂载时获取方案列表和状态选项
onMounted(() => {
  // getStatusOptions()
  searchPlanList()
})
</script>

<template>
  <div class="plan-management">
    <div class="search-header">
      <div class="search-row">
        <div class="search-item">
          <CustomInput v-model="search" placeholder="搜索方案名称或案件号" @click="setPage1"></CustomInput>
        </div>
        <!-- <div class="search-item">
          <label>案件状态</label>
          <el-select
            v-model="case_status"
            placeholder="案件状态"
            clearable
            @change="setPage1"
            style="width: 150px; margin-left: 12px;">
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>
        </div> -->
        <div class="search-item">
          <CustomButton @click="openAddPlanDialog" :height="34"><i class="jt-20-add"></i>新增调解</CustomButton>
        </div>
      </div>
    </div>
    <div>
      <el-table
        :data="planList"
        v-loading="loading"
        border
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle"
        class="plan-table">
        
        <!-- 序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center">
          <template v-slot="{$index}">
            {{page_size * (page - 1) + $index + 1}}
          </template>
        </el-table-column>
        <el-table-column prop="id" label="调解案件号" align="center" width="180"></el-table-column>
        <el-table-column prop="caseStatus" label="案件状态" align="center" width="100">
          <template v-slot="{row}">
            <el-tag :type="getCaseStatusType(row.caseStatus)" size="small">
              {{ getCaseStatusLabel(row.caseStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="debtor" label="债务人" align="center" width="120"></el-table-column>
        <el-table-column prop="creditor" label="债权人" align="center" width="120"></el-table-column>
        <el-table-column prop="mediator" label="调解员" align="center" width="100">
          <template v-slot="{row}">
            <span>{{ row.mediator}}</span>
          </template>
        </el-table-column>
        <el-table-column label="调解信息配置" align="center" min-width="180">
          <template v-slot="{row}">
            <div class="fields-preview">
              <div class="field-types">
                <span
                  v-for="field in row.fields.slice(0, 3)"
                  :key="field.id"
                  class="field-type-tag"
                  :title="field.title">
				          {{ field.title }}
                </span>
                <span v-if="row.fields.length > 3" class="more-fields">
                  +{{ row.fields.length - 3 }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="planConfirmed" label="确认调解方案" align="center" width="150">
          <template v-slot="{row}">
            <el-tag :type="row.planConfirmed ? 'success' : 'info'" size="small">
              {{ row.planConfirmed ? '已确认' : '待确认' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="agreement" label="调解协议" align="center" width="100">
          <template v-slot="{row}">
            <el-tag :type="row.agreement ? 'success' : 'info'" size="small">
              {{ row.agreement ? '已签署' : '未签署' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="electronicSignature" label="电子签名" align="center" width="100">
          <template v-slot="{row}">
            <el-tag :type="row.electronicSignature ? 'success' : 'info'" size="small">
              {{ row.electronicSignature ? '已签名' : '未签名' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notarizationStatus" label="协议公证状态" align="center" width="150">
          <template v-slot="{row}">
            <el-tag :type="getNotarizationType(row.notarizationStatus)" size="small">
              {{ getNotarizationLabel(row.notarizationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- 字段数量统计 -->
        <!-- <el-table-column label="字段统计" align="center" width="120">
          <template v-slot="{row}">
            <div class="field-stats">
              <el-tag size="small" type="info">{{ row.fields?.length || 0 }}个字段</el-tag>
              <div class="required-count">
                <el-tag size="small" type="warning">
                  {{ row.fields?.filter(f => f.required).length || 0 }}必填
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column> -->
        
        <!-- 相关文件 -->
        <el-table-column prop="fileList" label="相关文件" align="center" min-width="100">
          <template v-slot="{row}">
            <div class="file-list" v-for="file in row.fileList" :key="file.id">
              <span class="file-name">{{ file.name }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 备注 -->
        <el-table-column prop="remark" label="备注" align="center" min-width="100">
          <template v-slot="{row}">
            <div class="remark">
              <span class="remark-text">{{ row.remark }}</span>
            </div>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column label="操作" align="center" width="250">
          <template v-slot="{row}">
            <div class="operation-buttons">
              <!-- <div @click="previewJsonData(row)" class="operation-btn preview-btn">预览</div> -->
              <div @click="openEditPlanDialog(row)" class="operation-btn edit-btn">编辑</div>
              <div @click="openEnsureDeleteDialog(row)" class="operation-btn delete-btn">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <el-pagination
        class="pagi"
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="page"
        :page-size="page_size"
        @current-change="pageChanged"
      ></el-pagination>
    </div>
  </div>

  <!-- 新增方案对话框 -->
  <AddMediationInformation
    :show-dialog="showAdd"
    @close="showAdd = false"
    @ensure="submitAddPlan">
  </AddMediationInformation>

  <!-- 编辑方案对话框 -->
  <EditMediationInformation
    :show-dialog="showEdit"
    :plan-data="selectRow"
    @close="closeEditDialog"
    @ensure="submitEditPlan">
  </EditMediationInformation>
  
  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDelete"
    title="删除调解信息"
    :message="`确认删除选中的调解信息吗？此操作不可撤销。`"
    confirm-text="确认"
    cancel-text="取消"
    @update:visible="showDelete = $event"
    @confirm="deletePlanRow"
    @cancel="showDelete = false" />
</template>

<style lang="scss" scoped>
.plan-management {
  margin: 24px;
  background-color: #fff;
  height: calc(100% - 65px);
  padding: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .search-header {
    margin-bottom: 20px;
    
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          min-width: 80px;
          font-weight: 500;
          color: #333;
        }
        
        :deep(.el-select) {
          width: 160px;
        }
        
        :deep(.el-date-editor) {
          height: 36px;
        }
      }
    }
  }


}

.plan-table {
  border-radius: 8px;
  overflow: hidden;
  
  .plan-title {
    .title-text {
      color: #333;
      line-height: 1.4;
    }
  }
  
  .fields-preview {
    .field-types {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      justify-content: center;
      
      .field-type-tag {
        display: inline-block;
        padding: 2px 6px;
        background-color: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 12px;
        font-size: 12px;
        color: #0369a1;
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .more-fields {
        padding: 2px 6px;
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
  
  .field-stats {
    .required-count {
      margin-top: 4px;
    }
  }
  
  .create-time {
    font-size: 13px;
    color: #666;
  }
}

.pagi {
  margin-top: 20px;
  text-align: center;
  
  :deep(.el-pagination) {
    justify-content: center;
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .plan-management {
    .search-header {
      grid-template-columns: 1fr auto;
    }
  }
  
  .plan-table {
    .fields-preview {
      .field-types {
        .field-type-tag {
          max-width: 60px;
        }
      }
    }
  }
}
</style>