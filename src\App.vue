<script setup lang="ts">
// import HomeView from "./views/HomeView.vue";
import { RouterView } from "vue-router"
import { ElConfigProvider } from "element-plus";
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { ref, onMounted } from "vue";
import { useUserStore } from '@/stores/userStore'

const size = 'default'
const zIndex = ref(2000)
const locale = ref(zhCn)
const userStore = useUserStore()

/**
 * 应用初始化时无感地处理用户信息获取
 * 不阻断页面正常渲染，让401错误由axios拦截器处理跳转
 */
async function initializeUserInfo() {
  
  // 处理URL参数中的所有认证信息（其他系统跳转过来的情况）
  const urlParams = new URLSearchParams(window.location.search)
  const urlToken = urlParams.get('token')
  const urlTokenType = urlParams.get('token_type')
  const urlRefreshToken = urlParams.get('refresh_token')
  const urlExpiresIn = urlParams.get('expires_in')
  // 如果URL中包含完整的认证信息，则存储到sessionStorage
  if (urlToken && urlTokenType) {    
    // 存储主要认证信息
    sessionStorage.setItem('access_token', urlToken)
    sessionStorage.setItem('token_type', urlTokenType)
    
    // 存储刷新令牌（如果存在）
    if (urlRefreshToken) {
      sessionStorage.setItem('refresh_token', urlRefreshToken)
    }
    
    // 存储过期时间（如果存在）
    if (urlExpiresIn) {
      sessionStorage.setItem('expires_in', urlExpiresIn)
      // 计算并存储实际的过期时间戳
      const expirationTime = Date.now() + (parseInt(urlExpiresIn) * 1000)
      sessionStorage.setItem('token_expires_at', expirationTime.toString())
    }
    
    // 清除URL参数中的所有认证信息，避免刷新页面时重复处理或泄露
    const url = new URL(window.location.href)
    url.searchParams.delete('token')
    url.searchParams.delete('token_type')
    url.searchParams.delete('refresh_token')
    url.searchParams.delete('expires_in')
    window.history.replaceState({}, document.title, url.toString())
  }

  // 无感地尝试获取用户信息
  // 不管有没有token都尝试获取，让后端决定是否需要登录
  try {
    const success = await userStore.fetchUserInfo()
    if (success) {
      console.log('用户信息获取成功')
    } else {
      console.log('用户信息获取失败，但不阻断页面显示')
      // 不显示错误消息，让用户自然地通过401跳转到登录页
    }
  } catch (error) {
    // 不显示错误消息，不阻断页面渲染
    // 如果是401错误，axios拦截器会处理跳转
  }
}

// 应用启动时初始化用户信息
onMounted(() => {
  initializeUserInfo()
})

</script>

<template>
  <ElConfigProvider :size="size" :z-index="zIndex" :locale="locale">
    <!-- 无感渲染，始终显示正常页面 -->
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<style lang="scss">
.pagi {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.el-table--default {
  font-size: 16px !important;
  .el-table__cell {
    padding: 10.5px 0 !important;
  }
}
.el-table__expand-icon {
  transform: none !important;
}
// .el-checkbox__inner, .el-checkbox__inner::after{
//   transition: none !important;
// }
.add-role {
  .el-checkbox {
    --el-checkbox-font-size: 16px;
    --el-checkbox-input-height: 16px;
    --el-checkbox-input-width: 16px;
  }
  .el-checkbox__inner::after {
    left: 5px !important;
    top: 2px !important;
  }
  .el-table--default .cell {
    margin-left: 20px !important;
  }
  .el-table__row--level-1,.el-checkbox:last-of-type {
    color: #676768 !important;
  }
}
.form-display {
  .el-input {
    .el-input__wrapper{
      padding: 1px;
    }
    .el-input__inner{
      height: 38px;
      padding-left: 11px;
      border-radius: 4px;
    }
    .el-input__suffix {
      transition: all .3s;
      position: absolute;
      top: 13px;
      right: 12px;
      .el-input__suffix-inner {
        display: initial;
      }
    }
  }
}
</style>
