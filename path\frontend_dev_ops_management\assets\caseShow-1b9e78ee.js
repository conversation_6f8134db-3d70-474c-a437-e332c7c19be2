/* empty css             */import{C as ie,c as re,h as de}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 *//* empty css                       *//* empty css                  */import{a as F,r as _,s as A,o as Y,q as T,w as t,J as j,f as L,h as e,g as o,i as E,E as v,j as H,k as O,T as K,l as Q,M as N,p as P,m as R,z as ue,I as ce,n as G,F as _e,a7 as S,al as me,a9 as I,am as pe,an as fe,K as ge,L as ve,t as q,ad as he,ao as we,N as ye}from"./index-8a4876d8.js";import{C as U}from"./CustomButton-ea16d5c5.js";/* empty css                     */import{_ as W}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{_ as z}from"./_plugin-vue_export-helper-c27b6911.js";const X=i=>(P("data-v-357f1ff3"),i=i(),R(),i),be={class:"form-container"},Ve={class:"dialog-footer"},De=X(()=>o("i",{class:"jt-20-ensure"},null,-1)),Ce=X(()=>o("i",{class:"jt-20-delete"},null,-1)),xe=F({__name:"AddCase",props:{showDialog:{type:Boolean}},emits:["close","ensure"],setup(i,{emit:D}){const r=i,d=_(),f=_(!1),a=_({case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""}),C={case_name:[{required:!0,message:"请输入案例名称",trigger:"blur"}],case_date:[{required:!0,message:"请选择案例日期",trigger:"change"}],amount:[{required:!0,message:"请输入涉及金额",trigger:"blur"},{pattern:/^\d*(\.\d{1,2})?$/,message:"金额不能包含字母,小数点后两位",trigger:"blur"}],mediation_result:[{required:!0,message:"请选择调解结果",trigger:"change"}],case_details:[{required:!0,message:"请输入案例详情",trigger:"blur"},{min:10,max:1e3,message:"案例详情长度在10到1000个字符",trigger:"blur"}]};A(()=>r.showDialog,u=>{u&&y()});function y(){a.value={case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""},d.value&&d.value.clearValidate()}function x(){D("close")}async function w(){if(d.value){f.value=!0;try{if(!await new Promise(l=>{d.value.validate(s=>{l(s)})}))return;D("ensure",{...a.value})}catch(u){console.error("表单验证失败:",u),v.error("表单验证失败，请检查输入内容")}finally{f.value=!1}}}return(u,l)=>{const s=H,m=O,b=K,$=Q,M=N;return Y(),T(W,{visible:u.showDialog,"onUpdate:visible":x,width:"600px",title:"新增案例信息"},{default:t(()=>[j((Y(),L("div",be,[e($,{ref_key:"formRef",ref:d,model:a.value,rules:C,"label-width":"100px",class:"case-form"},{default:t(()=>[e(m,{label:"案例名称",prop:"case_name"},{default:t(()=>[e(s,{modelValue:a.value.case_name,"onUpdate:modelValue":l[0]||(l[0]=g=>a.value.case_name=g),placeholder:"请输入案例名称",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(m,{label:"案例日期",prop:"case_date"},{default:t(()=>[e(b,{modelValue:a.value.case_date,"onUpdate:modelValue":l[1]||(l[1]=g=>a.value.case_date=g),type:"date",placeholder:"选择案例日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(m,{label:"涉及金额",prop:"amount"},{default:t(()=>[e(s,{modelValue:a.value.amount,"onUpdate:modelValue":l[2]||(l[2]=g=>a.value.amount=g),placeholder:"请输入涉及金额",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(m,{label:"调解结果",prop:"mediation_result"},{default:t(()=>[e(s,{modelValue:a.value.mediation_result,"onUpdate:modelValue":l[3]||(l[3]=g=>a.value.mediation_result=g),placeholder:"请输入调解结果",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(m,{label:"案例详情",prop:"case_details"},{default:t(()=>[e(s,{modelValue:a.value.case_details,"onUpdate:modelValue":l[4]||(l[4]=g=>a.value.case_details=g),type:"textarea",placeholder:"请详细描述案例的具体情况、处理过程等",rows:5,maxlength:"1000","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),o("div",Ve,[e(U,{onClick:w,height:34,loading:f.value,"btn-type":"blue"},{default:t(()=>[De,E("确认 ")]),_:1},8,["loading"]),e(U,{onClick:x,height:34},{default:t(()=>[Ce,E("取消 ")]),_:1})])])),[[M,f.value]])]),_:1},8,["visible"])}}});const Ee=z(xe,[["__scopeId","data-v-357f1ff3"]]),Z=i=>(P("data-v-fa4a2b59"),i=i(),R(),i),ke={class:"form-container"},Ye={class:"dialog-footer"},$e=Z(()=>o("i",{class:"jt-20-ensure"},null,-1)),Me=Z(()=>o("i",{class:"jt-20-delete"},null,-1)),Ue=F({__name:"EditCase",props:{showDialog:{type:Boolean},caseData:{}},emits:["close","ensure"],setup(i,{emit:D}){const r=i,d=_(),f=_(!1),a=_({case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""}),C={case_name:[{required:!0,message:"请输入案例名称",trigger:"blur"}],case_date:[{required:!0,message:"请选择案例日期",trigger:"change"}],amount:[{required:!0,message:"请输入涉及金额",trigger:"blur"},{pattern:/^\d*(\.\d{1,2})?$/,message:"金额不能包含字母,小数点后两位",trigger:"blur"}],mediation_result:[{required:!0,message:"请选择调解结果",trigger:"change"}],case_details:[{required:!0,message:"请输入案例详情",trigger:"blur"},{min:10,max:1e3,message:"案例详情长度在10到1000个字符",trigger:"blur"}]};A(()=>r.showDialog,l=>{l&&y()}),A(()=>r.caseData,()=>{r.showDialog&&r.caseData&&y()},{deep:!0});function y(){r.caseData?a.value={...r.caseData,mediation_result:r.caseData.mediation_result}:x(),d.value&&d.value.clearValidate()}function x(){a.value={case_name:"",case_date:"",amount:0,mediation_result:"",case_details:""}}function w(){D("close")}async function u(){if(d.value){f.value=!0;try{if(!await new Promise(s=>{d.value.validate(m=>{s(m)})}))return;D("ensure",{...a.value})}catch(l){console.error("表单验证失败:",l),v.error("表单验证失败，请检查输入内容")}finally{f.value=!1}}}return(l,s)=>{const m=H,b=O,$=K,M=Q,g=N;return Y(),T(W,{visible:l.showDialog,"onUpdate:visible":w,width:"600px",title:"编辑案例信息"},{default:t(()=>[j((Y(),L("div",ke,[e(M,{ref_key:"formRef",ref:d,model:a.value,rules:C,"label-width":"100px",class:"case-form"},{default:t(()=>[e(b,{label:"案例名称",prop:"case_name"},{default:t(()=>[e(m,{modelValue:a.value.case_name,"onUpdate:modelValue":s[0]||(s[0]=h=>a.value.case_name=h),placeholder:"请输入案例名称",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(b,{label:"案例日期",prop:"case_date"},{default:t(()=>[e($,{modelValue:a.value.case_date,"onUpdate:modelValue":s[1]||(s[1]=h=>a.value.case_date=h),type:"date",placeholder:"选择案例日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(b,{label:"涉及金额",prop:"amount"},{default:t(()=>[e(m,{modelValue:a.value.amount,"onUpdate:modelValue":s[2]||(s[2]=h=>a.value.amount=h),placeholder:"请输入涉及金额",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(b,{label:"调解结果",prop:"mediation_result"},{default:t(()=>[e(m,{modelValue:a.value.mediation_result,"onUpdate:modelValue":s[3]||(s[3]=h=>a.value.mediation_result=h),placeholder:"请输入调解结果",maxlength:"100",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(b,{label:"案例详情",prop:"case_details"},{default:t(()=>[e(m,{modelValue:a.value.case_details,"onUpdate:modelValue":s[4]||(s[4]=h=>a.value.case_details=h),type:"textarea",placeholder:"请详细描述案例的具体情况、处理过程等",rows:5,maxlength:"1000","show-word-limit":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),o("div",Ye,[e(U,{onClick:u,height:34,loading:f.value,"btn-type":"blue"},{default:t(()=>[$e,E("确认 ")]),_:1},8,["loading"]),e(U,{onClick:w,height:34},{default:t(()=>[Me,E("取消 ")]),_:1})])])),[[g,f.value]])]),_:1},8,["visible"])}}});const Se=z(Ue,[["__scopeId","data-v-fa4a2b59"]]),ee=i=>(P("data-v-a6592c45"),i=i(),R(),i),Ie={class:"case-show-page"},qe={class:"search-header"},Be={class:"search-row"},Ae={class:"search-item"},Fe={class:"search-item"},Te=ee(()=>o("label",null,"案例日期：",-1)),je={class:"search-item"},Le=ee(()=>o("i",{class:"jt-20-add"},null,-1)),Ke={class:"table-container"},Ne={class:"case-details-text"},Pe={class:"operation-buttons"},Re=["onClick"],ze=["onClick"],Je={class:"pagination-wrapper"},B=10,Ge=F({__name:"caseShow",setup(i){const D=_(0),r=_(1),d=_(""),f=_(""),a=_([]),C=_(!1),y=_(!1),x=_(!1),w=_(void 0);async function u(){if(!S()){v.error("登录状态已失效，请重新登录");return}x.value=!0;const c={page:r.value,page_size:B,search:d.value,case_date:f.value},{data:n}=await me(c),{state:k,msg:V}=n;k==="success"?(a.value=n.data.results,D.value=n.data.count):v.error(V),x.value=!1}function l(){I("搜索案例","案例展示"),r.value=1,u()}function s(c){r.value=c,u()}function m(){I("新增案例","案例展示"),y.value=!0}function b(c){I("编辑案例","案例展示"),w.value={...c},C.value=!0}function $(){y.value=!1}function M(){C.value=!1,w.value=void 0}async function g(c){try{if(!S()){v.error("登录状态已失效，请重新登录");return}const n=await pe(c);v.success("案例新增成功"),y.value=!1,u()}catch{v.error("新增案例失败，请重试")}}async function h(c){var n;try{if(!S()){v.error("登录状态已失效，请重新登录");return}const k=await fe(c,(n=w.value)==null?void 0:n.id);v.success("案例编辑成功"),C.value=!1,w.value=void 0,u()}catch(k){console.error("编辑案例失败:",k),v.error("编辑案例失败，请重试")}}async function ae(c){try{if(!S()){v.error("登录状态已失效，请重新登录");return}await he.confirm(`确定要删除案例"${c.case_name}"吗？`,"删除确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}),I("删除案例","案例展示");const n=await we(c.id);v.success("案例删除成功"),u()}catch(n){n!=="cancel"&&(console.error("删除案例失败:",n),v.error("删除案例失败，请重试"))}}function te(c){return`¥${c.toLocaleString()}`}return ue(()=>{u()}),(c,n)=>{const k=K,V=ye,le=ge,se=ve,oe=N;return Y(),L(_e,null,[o("div",Ie,[o("div",qe,[o("div",Be,[o("div",Ae,[e(ie,{modelValue:d.value,"onUpdate:modelValue":n[0]||(n[0]=p=>d.value=p),placeholder:"搜索案例名称、案例详情",onKeyup:ce(l,["enter"]),onClick:l},null,8,["modelValue","onKeyup"])]),o("div",Fe,[Te,e(k,{modelValue:f.value,"onUpdate:modelValue":n[1]||(n[1]=p=>f.value=p),type:"date",placeholder:"请选择案例日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:l},null,8,["modelValue"])]),o("div",je,[e(U,{onClick:m,height:34},{default:t(()=>[Le,E("新增案例 ")]),_:1})])])]),o("div",Ke,[j((Y(),T(le,{data:a.value,border:"",style:{width:"100%"},"cell-style":G(re),"header-cell-style":G(de)},{default:t(()=>[e(V,{type:"index",label:"序号",width:"80",align:"center"},{default:t(({$index:p})=>[E(q(B*(r.value-1)+p+1),1)]),_:1}),e(V,{align:"center",prop:"case_name",label:"案例名称","min-width":"200"}),e(V,{align:"center",prop:"case_date",label:"案例日期",width:"120"}),e(V,{label:"涉及金额",width:"140",align:"center"},{default:t(({row:p})=>[E(q(te(p.amount)),1)]),_:1}),e(V,{label:"调解结果",width:"320",align:"center",prop:"mediation_result"},{default:t(({row:p})=>[E(q(p.mediation_result),1)]),_:1}),e(V,{align:"center",prop:"case_details",label:"案例详情","min-width":"500"},{default:t(({row:p})=>[o("div",Ne,q(p.case_details),1)]),_:1}),e(V,{label:"操作",width:"180",align:"center",fixed:"right"},{default:t(({row:p,$index:J})=>[o("div",Pe,[o("div",{onClick:ne=>b(p,J),class:"operation-btn edit-btn"},"编辑",8,Re),o("div",{onClick:ne=>ae(p,J),class:"operation-btn delete-btn"},"删除",8,ze)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[oe,x.value]]),o("div",Je,[e(se,{background:"",layout:"prev, pager, next",total:D.value,"current-page":r.value,"page-size":B,onCurrentChange:s},null,8,["total","current-page"])])])]),e(Ee,{"show-dialog":y.value,onClose:$,onEnsure:g},null,8,["show-dialog"]),e(Se,{"show-dialog":C.value,"case-data":w.value,onClose:M,onEnsure:h},null,8,["show-dialog","case-data"])],64)}}});const oa=z(Ge,[["__scopeId","data-v-a6592c45"]]);export{oa as default};
