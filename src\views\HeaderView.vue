<script lang="ts" setup>
import { reactive, ref, computed } from "vue"
import { useRouter } from "vue-router"
import { getLogout, setPassword } from "@/axios/login"
// import { client_id, client_secret } from "@/common/variables/env"
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import CustomDialog from '@/components/common/CustomDialog.vue';
import { useUserStore } from '@/stores/userStore';

interface RuleForm {
  old_password: string,
  new_password: string,
  password: string,
}
const router = useRouter()
const userStore = useUserStore()

// 使用computed从userStore获取响应式的用户信息
const group_name = computed(() => userStore.userInfo.group_name || sessionStorage.getItem('group_name') || '')
const username = computed(() => userStore.userInfo.username || sessionStorage.getItem('username') || '')

const showModifyPassword = ref(false)
const ruleFormRef = ref<FormInstance>()
const formData = reactive<RuleForm>({
  old_password :'',
  new_password :'',
  password :'',
})
const rules = reactive<FormRules<RuleForm>>({
  old_password: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入确认密码', trigger: 'blur' },
  ],
})
const formLabelWidth = '110px'
function openModifyPasswordDialog() {
  showModifyPassword.value = true
}
async function modifyPassword(formEl: FormInstance | undefined) {
  if(!formEl || !formEl.validate) return
  await formEl.validate(async (valid, fields) => {
    if(valid) {
      if (formData.new_password != formData.password) {
        ElMessage.error("请输入相同的新密码")
        return;
      }
      // 至少八个字符，大小写、数字和特殊符号组成
      const password = /^.*(?=.{8,30})(?=.*\d)(?=.*[A-Z]{1,})(?=.*[a-z]{1,})(?=.*[.+!@#$%^&*?()]).*/;
      if (!password.test(formData.new_password) || !password.test(formData.password)) {
        ElMessage.error("新密码必须包含大小写字母、数字和特殊字符，至少8个字符")
        return;
      }
      if (formData.old_password === formData.new_password && formData.password) {
        ElMessage.error("原密码不能与新密码一致")
        return;
      }
      const { data } = await setPassword(formData)
      const { state, msg } = data
      if(state === 'success') {
        ElMessage.success('修改密码成功')
        formEl.resetFields();
        sessionStorage.clear()
      } else {
        ElMessage.error(msg)
      }
    }
  })
}
async function cancelForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
  showModifyPassword.value = false
}

const token = sessionStorage.getItem('access_token')

async function signOut() {
  if(token) {
    const { data } = await getLogout()
    const { state, msg } = data
    if(state === 'success') {
      ElMessage.success('退出登录成功')
    } else {
      ElMessage.error(msg)
    }
  } else {
    sessionStorage.clear()
  }
}
</script>

<template>
  <div class="header-view">
    <div class="header-title">
      <!-- <i class="jt-50-party"></i> -->
      <span>智能处置运营管理</span>
    </div>
    <div class="options-bar">
        <div class="fake-btn">
          <img src="@/assets/images/icon/icon_pe.svg"/>
          <span>{{ group_name }}{{ username }}</span>
        </div>
        <div class="fake-btn" @click="openModifyPasswordDialog">
          <img src="@/assets/images/icon/icon_password.png" />
          <span>修改密码</span>
        </div>
        <div class="fake-btn" @click="signOut">
          <img src="@/assets/images/icon/icon_tui.svg" />
          <span>退出登录</span>
        </div>
    </div>
  </div>
  <CustomDialog title="修改密码" :visible="showModifyPassword" width="520px" :markclose="true" @update:visible="showModifyPassword = false">
    <template #default>
      <el-form :model="formData" ref="ruleFormRef" :rules="rules">
        <el-form-item label="原密码" prop="old_password" :label-width="formLabelWidth">
          <el-input
            show-password
            v-model="formData.old_password "
            placeholder="请输入原密码"
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="new_password" :label-width="formLabelWidth">
          <el-input
            show-password
            v-model="formData.new_password"
            placeholder="请输入新密码"
          ></el-input>
        </el-form-item>
        <div class="newPassword">* 必须包含大小写字母、数字和特殊字符，至少8个字符</div>
        <el-form-item label="确认新密码" prop="password" :label-width="formLabelWidth">
          <el-input
            show-password
            v-model="formData.password"
            placeholder="请再次输入新密码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="btns-group">
        <CustomButton @click="modifyPassword(ruleFormRef)" :height="34" btn-type="blue"><i class="jt-20-ensure"></i>确认</CustomButton>
        <CustomButton @click="cancelForm(ruleFormRef)" :height="34"><i class="jt-20-delete"></i>取消</CustomButton>
      </div>
    </template>
  </CustomDialog>
</template>

<style lang="scss" scoped>
.header-view {
  display: flex;
  justify-content: space-between;
  height: 56px;
  background-color: #185294;
  .header-title {
    margin-left: 36px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-family:Microsoft YaHei;
    color:#ffffff;
    font-size:28px;
  }
  .options-bar {
    display: flex;
    gap: 20px;
    .fake-btn {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #fff;
      margin-right: 20px;
      cursor: pointer;
      > img {
        margin-top: 6px;
        margin-right: -6px;
      }
    }
  }
}
.newPassword{
  line-height: 12px;
  color: #828080;
  width: 100%;
  font-size: 14px;
  padding-left: 110px;
  margin-bottom: 16px;
}
.dialog_container{
  overflow-x: hidden !important;
}
.btns-group{
  display: flex;
  text-align: center;
  justify-content: center;
  gap: 10px;
}
</style>