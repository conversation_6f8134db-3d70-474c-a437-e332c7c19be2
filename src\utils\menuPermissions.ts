import type { MenuPermissionMap, PermissionCodeMap } from '@/components/common/type'

// 权限名称与编码的映射关系（根据实际权限数据结构调整）
export const permissionCodeMap: PermissionCodeMap = {
  // 运营管理相关权限映射
  '资产包管理': '资产包管理',
  '案件跟踪': '案件跟踪',
  '调解信息': '调解信息',
  '调解方案': '调解方案',
  '人员调度': '人员调度',
  '债权人管理': '债权人管理',
  '债务人管理': '债务人管理',
  '诉前保全': '诉前保全',
  '信息修复': '信息修复',
  '案例展示': '案例展示',
  '投诉建议': '投诉建议',
  '语音外呼记录': '语音外呼',
  '短信发送记录': '短信管理',
  
  // 数据管理相关权限映射
  '字段配置': '字段配置', 
  '数据导入': '数据导入',
  '数据分析': '数据分析'
}

// 菜单路径与权限的映射关系
export const menuPermissionMap: MenuPermissionMap = {
  // 运营管理 - 二级菜单（直接链接）
  '/home/<USER>/assetPackage': ['资产包管理'],

  '/home/<USER>/creditor': ['债权人管理'],
  '/home/<USER>/debtor': ['债务人管理'],
  '/home/<USER>/preAction': ['诉前保全'],
  '/home/<USER>/informationRepair': ['信息修复'],
  '/home/<USER>/caseShow': ['案例展示'],
  '/home/<USER>/complaint': ['投诉建议'],
  
  // 运营管理 > 调解管理 - 三级菜单
  '/home/<USER>/caseTracking': ['案件跟踪'],
  '/home/<USER>/mediationInformation': ['调解信息'],
  '/home/<USER>/disposalPlan': ['调解方案'],
  '/home/<USER>/personnelDispatch': ['人员调度'],
  
  // 运营管理 > 外呼管理 - 三级菜单
  '/home/<USER>/outboundCall': ['语音外呼'],
  '/home/<USER>/messageRecord': ['短信管理'],
  
  // 数据管理 - 二级菜单
  '/home/<USER>/fieldConfiguration': ['字段配置'],
  '/home/<USER>/dataImport': ['数据导入'],
  '/home/<USER>/dataAnalysis': ['数据分析']
}

/**
 * 检查用户是否有访问指定菜单的权限
 * @param userPermissions 用户权限数组
 * @param menuPath 菜单路径
 * @returns 是否有权限
 */
export function hasMenuPermission(userPermissions: string[], menuPath: string): boolean {
  const requiredPermissions = menuPermissionMap[menuPath]
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true // 没有配置权限要求的菜单默认可访问
  }
  
  return requiredPermissions.some(permission => 
    userPermissions.includes(permission)
  )
}

/**
 * 根据当前路径获取菜单层级名称
 * @param currentPath 当前路径
 * @param menuList 菜单列表
 * @returns 菜单层级字符串 (例如: "运营管理 > 调解管理 > 调解方案")
 */
export function getMenuHierarchy(currentPath: string, menuList: any[]): string {
  // 遍历菜单结构，寻找匹配的路径
  for (const parent of menuList) {
    // 检查二级菜单（有直接链接的）
    for (const child of parent.children || []) {
      if (child.link === currentPath) {
        return `${parent.parent} > ${child.name}`
      }
      
      // 检查三级菜单
      if (child.children) {
        for (const grandChild of child.children) {
          if (grandChild.link === currentPath) {
            return `${parent.parent} > ${child.name} > ${grandChild.name}`
          }
        }
      }
    }
  }
  
  // 如果在菜单结构中找不到，尝试根据路径推断
  const pathMappings: { [key: string]: string } = {
    '/ops_management/home/<USER>/assetPackage': '运营管理 > 资产包管理',

    '/ops_management/home/<USER>/creditor': '运营管理 > 债权人管理',
    '/ops_management/home/<USER>/debtor': '运营管理 > 债务人管理',
    '/ops_management/home/<USER>/preAction': '运营管理 > 诉前保全',
    '/ops_management/home/<USER>/informationRepair': '运营管理 > 信息修复',
    '/ops_management/home/<USER>/caseShow': '运营管理 > 案例展示',
    '/ops_management/home/<USER>/complaint': '运营管理 > 投诉建议',

    '/ops_management/home/<USER>/caseTracking': '运营管理 > 调解管理 > 案件跟踪',
    '/ops_management/home/<USER>/mediationInformation': '运营管理 > 调解管理 > 调解信息',
    '/ops_management/home/<USER>/disposalPlan': '运营管理 > 调解管理 > 调解方案',
    '/ops_management/home/<USER>/personnelDispatch': '运营管理 > 调解管理 > 人员调度',

    '/ops_management/home/<USER>/outboundCall': '运营管理 > 外呼管理 > 语音外呼记录',
    '/ops_management/home/<USER>/messageRecord': '运营管理 > 外呼管理 > 短信发送记录',

    '/ops_management/home/<USER>/fieldConfiguration': '数据管理 > 数据治理 > 字段配置',
    '/ops_management/home/<USER>/dataImport': '数据管理 > 数据治理 > 数据导入',
    '/ops_management/home/<USER>/dataAnalysis': '数据管理 > 数据分析'
  }
  return pathMappings[currentPath] || '其他菜单'
} 