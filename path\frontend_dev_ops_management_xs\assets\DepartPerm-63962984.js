/* empty css             */import{C as v,_ as P,E as q,a as G,b as H}from"./CustomDialog.vue_vue_type_style_index_0_lang-84a7db76.js";import{C as J,_ as K,E as L,a as O,h as Q,c as W,b as X}from"./headerCellStyle-db8d8cf0.js";import{g as Y,d as Z,a as ee,p as te}from"./system-846db6a5.js";import{r as n,d as ae,k as se,o as le,c as ne,b as o,e as t,w as l,h as U,F as oe,f as c,t as ie,p as ue,g as de}from"./index-d5da4504.js";import{E as d}from"./index-efa25d88.js";import{_ as re}from"./_plugin-vue_export-helper-c27b6911.js";n();n(0);n(0);const f=g=>(ue("data-v-8d8de418"),g=g(),de(),g),ce={class:"depart"},pe={class:"search-header"},_e=f(()=>o("i",{class:"jt-24-add"},null,-1)),me={style:{display:"flex","justify-content":"center",gap:"14px"}},ve=["onClick"],fe=["onClick"],ge={class:"btns-group"},he=f(()=>o("i",{class:"jt-24-ensure"},null,-1)),be=f(()=>o("i",{class:"jt-24-delete"},null,-1)),Ce={class:"btns-group"},De=f(()=>o("i",{class:"jt-24-ensure"},null,-1)),ye=f(()=>o("i",{class:"jt-24-delete"},null,-1)),E=10,ke=ae({__name:"DepartPerm",setup(g){const r=n(),x=n(0),p=n(1),y=n(""),V=n([]);function h(){p.value=1,k()}async function k(){const a={ordering:"id",name:"",search:y.value,page:p.value,page_size:E},{data:e}=await Y(a),{state:i,msg:u}=e;i==="success"?(V.value=e.data.results,x.value=e.data.count):d.error(u)}function B(a){p.value=a,k()}const _=n(!1),w=n("");function F(){_.value=!0}async function N(){const a={permissions:[],first_name:w.value},{data:e}=await ee(a),{state:i,msg:u}=e;i==="success"?(d.success(u),_.value=!1,h()):d.error(u)}const m=n(!1),b=n("");function T(a,e){r.value=a,b.value=a.name,m.value=!0}async function z(){if(!r.value)return;const a={permissions:[],first_name:b.value},{id:e}=r.value,{data:i}=await te(a,e),{state:u,msg:D}=i;u==="success"?(d.success(D),m.value=!1,h()):d.error(D)}const C=n(!1);function A(a,e){r.value=a,C.value=!0}async function M(){if(r.value){const{id:a}=r.value,{data:e}=await Z(a),{state:i,msg:u}=e;i==="success"?(d.success(u),h(),C.value=!1):d.error(u)}}return se(()=>{k()}),(a,e)=>{const i=X,u=L,D=O,I=q,$=G,j=H;return le(),ne(oe,null,[o("div",ce,[o("div",pe,[t(J,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=s=>y.value=s),placeholder:"搜索部门名称",onClick:h},null,8,["modelValue"]),t(v,{onClick:F,height:34},{default:l(()=>[_e,c("新增部门")]),_:1})]),o("div",null,[t(u,{data:V.value,border:"","header-cell-style":U(Q),"cell-style":U(W)},{default:l(()=>[t(i,{type:"index",label:"序号",width:"60",align:"center"},{default:l(({$index:s})=>[c(ie(E*(p.value-1)+s+1),1)]),_:1}),t(i,{prop:"name",label:"部门",align:"center"}),t(i,{label:"操作",align:"center",width:"200"},{default:l(({row:s,$index:S})=>[o("div",me,[o("div",{onClick:R=>T(s,S),class:"operation-btn"},"编辑",8,ve),o("div",{onClick:R=>A(s,S),class:"operation-btn"},"删除",8,fe)])]),_:1})]),_:1},8,["data","header-cell-style","cell-style"]),t(D,{class:"pagi",background:"",layout:"prev, pager, next",total:x.value,"current-page":p.value,"page-size":E,onCurrentChange:B},null,8,["total","current-page"])])]),t(P,{title:"新增",visible:_.value,width:"400px",markclose:!0,"onUpdate:visible":e[3]||(e[3]=s=>_.value=!1)},{default:l(()=>[t(j,null,{default:l(()=>[t($,{label:"部门名称："},{default:l(()=>[t(I,{modelValue:w.value,"onUpdate:modelValue":e[1]||(e[1]=s=>w.value=s),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1}),o("div",ge,[t(v,{onClick:N,height:34},{default:l(()=>[he,c("确认")]),_:1}),t(v,{onClick:e[2]||(e[2]=s=>_.value=!1),height:34},{default:l(()=>[be,c("取消")]),_:1})])]),_:1},8,["visible"]),t(P,{title:"编辑",visible:m.value,width:"400px",markclose:!0,"onUpdate:visible":e[6]||(e[6]=s=>m.value=!1)},{default:l(()=>[t(j,null,{default:l(()=>[t($,{label:"部门名称："},{default:l(()=>[t(I,{modelValue:b.value,"onUpdate:modelValue":e[4]||(e[4]=s=>b.value=s),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1}),o("div",Ce,[t(v,{onClick:z,height:34},{default:l(()=>[De,c("确认")]),_:1}),t(v,{onClick:e[5]||(e[5]=s=>m.value=!1),height:34},{default:l(()=>[ye,c("取消")]),_:1})])]),_:1},8,["visible"]),t(K,{"show-dialog":C.value,onEnsure:M,onClose:e[7]||(e[7]=s=>C.value=!1)},null,8,["show-dialog"])],64)}}});const Se=re(ke,[["__scopeId","data-v-8d8de418"]]);export{Se as default};
