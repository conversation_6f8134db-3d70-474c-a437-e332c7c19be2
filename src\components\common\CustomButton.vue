<template>
  <div :class="['btn-container', dynamicBgColor]" :style="{height: height + 'px'}" @click.stop="handleClick" tabindex="0">
    <span class="btn__prefix" :style="{height: height + 'px'}">
      <div class="btn__prefix__inner">
        <slot name="prefix-icon"></slot>
      </div>
    </span>
    <span class="btn__default"><slot></slot></span>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { logButtonClick } from '@/utils/operationLogger'

const props = defineProps<{
  height?: number
  btnType?: 'red' | 'blue' | 'default',
  disabled?: boolean,
  logName?: string,        // 自定义日志记录的按钮名称
  menuName?: string,       // 自定义菜单名称
  disableLog?: boolean     // 是否禁用日志记录
}>()
const emit = defineEmits<{
  (e: 'click') : void
}>()

const colorBox = {
  red: 'custom__button__bg__red',
  default: 'custom__button__bg',
  blue: 'custom__button__bg__blue'
}
const dynamicBgColor = computed(() => {
  if(props.disabled) return 'custom__button__disabled'
  return props.btnType === undefined ? colorBox['default'] : colorBox[props.btnType]
})
function handleClick(event: Event) {
  if(props.disabled) return
  
  // 记录操作日志 (除非明确禁用)
  if (!props.disableLog) {
    const buttonName = props.logName || extractButtonName(event.target as HTMLElement)
    logButtonClick(buttonName, props.menuName)
  }
  
  emit('click')
}

/**
 * 从按钮元素中提取按钮名称
 */
function extractButtonName(element: HTMLElement): string {
  // 获取按钮的文本内容
  const buttonContainer = element.closest('.btn-container')
  const textContent = buttonContainer?.querySelector('.btn__default')?.textContent?.trim()
  
  if (textContent) {
    return textContent.replace(/\s+/g, ' ').trim()
  }
  
  return element.textContent?.trim() || '未知按钮'
}
</script>

<style lang="scss" scoped>
.btn-container {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  border-radius: .4rem;
  white-space: nowrap;
  .btn__prefix, .btn__default {
    display: inline-flex;
    align-items: center
  }
  .btn__prefix {
    margin-left: 1.1rem;
    margin-right: .4rem;
    .btn__prefix__inner {
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
  .btn__default {
    margin-right: 1.5rem;
    font-size: 16px;
    gap: 5px;
  }
}
.custom__button__bg {
  /* background-image: linear-gradient(180deg, 
		#F3F3F3 0%,
    #ececec 50%,
    #dfdfdf 51%,
		#dfdfdf 100%); */
  border: .1rem solid #d1d1d1;
  /* &:hover {
    background-image: linear-gradient(180deg, 
    #dcdcdc 0%,
    #f2f2f2 100%);
  } */
}

.custom__button__bg__red {
  background-color: #D94323;
  color: #fff;
  /* background-image: linear-gradient(180deg,
    #ffbca7 0%,
    #fc9278 50%,
    #f85d3d 51%,
    #f85d3d 100%); */
  border: 1px solid #FFAA74;
  /* &:hover {
    background-image: linear-gradient(180deg,
      #F85D3D 0%,
      #FFBCA7 100%);
  } */
}

.custom__button__disabled {
  background-image: linear-gradient(180deg, 
		#dfdfdf 0%,
    #ececec 50%,
    #dfdfdf 51%,
		#dfdfdf 100%);
  border: .1rem solid #d1d1d1;
  opacity: .65;
  color: #a2a2a2;
  cursor:not-allowed;
}

.custom__button__bg__blue {
  // background-image: linear-gradient(180deg, #56adff 0%, #80c1ff 50%, #3a8ee6 51%, #3a8ee6 100%);
  // border: 1px solid #55a5ef;
  background-color: #1377C4;
  color: #fff;
}

</style>