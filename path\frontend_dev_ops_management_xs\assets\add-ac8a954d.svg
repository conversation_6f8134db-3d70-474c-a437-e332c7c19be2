<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-4);
      }

      .cls-5 {
        fill: url(#linear-gradient-5);
      }

      .cls-6 {
        fill: url(#linear-gradient-6);
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: #cbedfa;
      }

      .cls-9 {
        fill: none;
      }
    </style>
    <linearGradient id="linear-gradient" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1450c5"/>
      <stop offset="1" stop-color="#15a6d8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1556cb"/>
      <stop offset="0.533" stop-color="#1586d3"/>
      <stop offset="1" stop-color="#29b0dd"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#155cd1"/>
      <stop offset="0.533" stop-color="#168dd7"/>
      <stop offset="1" stop-color="#3db9e1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1663d7"/>
      <stop offset="0.533" stop-color="#1695dc"/>
      <stop offset="1" stop-color="#52c3e6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1669dc"/>
      <stop offset="0.533" stop-color="#169de0"/>
      <stop offset="1" stop-color="#66cceb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#176fe2"/>
      <stop offset="0.533" stop-color="#17a4e4"/>
      <stop offset="1" stop-color="#7ad6ef"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1775e8"/>
      <stop offset="0.533" stop-color="#17ace8"/>
      <stop offset="1" stop-color="#8edff4"/>
    </linearGradient>
  </defs>
  <g id="icon_lurlan" transform="translate(-615.968 2.867)">
    <g id="组_1804" data-name="组 1804" transform="translate(619.968 1.132)">
      <g id="组_1803" data-name="组 1803" transform="translate(0)">
        <path id="路径_4572" data-name="路径 4572" class="cls-1" d="M663.22,31.958a.811.811,0,0,0-.809-.809H659a.808.808,0,0,1-.806-.806v-3.41a.81.81,0,0,0-.805-.811h-4.852a.812.812,0,0,0-.81.811v3.41a.808.808,0,0,1-.805.806h-3.413a.806.806,0,0,0-.805.809v4.852a.8.8,0,0,0,.805.8h3.413a.81.81,0,0,1,.805.807v3.416a.809.809,0,0,0,.81.8h4.852a.807.807,0,0,0,.805-.8V38.42a.81.81,0,0,1,.806-.807h3.412a.809.809,0,0,0,.809-.8Z" transform="translate(-646.702 -26.122)"/>
        <path id="路径_4573" data-name="路径 4573" class="cls-2" d="M652.779,42.592a.926.926,0,0,1-.924-.921v-3.18a.921.921,0,0,0-.919-.923h-3.18a.93.93,0,0,1-.927-.923V32.207a.931.931,0,0,1,.927-.926h3.18a.92.92,0,0,0,.919-.92V27.18a.927.927,0,0,1,.924-.923h4.44a.925.925,0,0,1,.924.923v3.181a.925.925,0,0,0,.923.92h3.181a.928.928,0,0,1,.922.926v4.438a.926.926,0,0,1-.922.923h-3.181a.926.926,0,0,0-.923.923v3.18a.923.923,0,0,1-.924.921Z" transform="translate(-646.742 -26.165)"/>
        <path id="路径_4574" data-name="路径 4574" class="cls-3" d="M653.031,42.548a1.044,1.044,0,0,1-1.041-1.042V38.557a1.041,1.041,0,0,0-1.038-1.036H648a1.042,1.042,0,0,1-1.038-1.037V32.452A1.039,1.039,0,0,1,648,31.417h2.951a1.047,1.047,0,0,0,1.038-1.042V27.428a1.043,1.043,0,0,1,1.041-1.043h4.031a1.042,1.042,0,0,1,1.032,1.043v2.947a1.048,1.048,0,0,0,1.042,1.042h2.948a1.036,1.036,0,0,1,1.038,1.035v4.033a1.039,1.039,0,0,1-1.038,1.037h-2.948a1.042,1.042,0,0,0-1.042,1.036v2.949a1.043,1.043,0,0,1-1.032,1.042Z" transform="translate(-646.785 -26.206)"/>
        <path id="路径_4575" data-name="路径 4575" class="cls-4" d="M653.277,42.5a1.159,1.159,0,0,1-1.156-1.154v-2.72a1.155,1.155,0,0,0-1.152-1.152h-2.718a1.157,1.157,0,0,1-1.153-1.156V32.7a1.157,1.157,0,0,1,1.153-1.156h2.718a1.155,1.155,0,0,0,1.152-1.154V27.675a1.16,1.16,0,0,1,1.156-1.153H656.9a1.154,1.154,0,0,1,1.152,1.153v2.718a1.159,1.159,0,0,0,1.156,1.154h2.719a1.159,1.159,0,0,1,1.152,1.156v3.615a1.159,1.159,0,0,1-1.152,1.156H659.2a1.159,1.159,0,0,0-1.156,1.152v2.72A1.154,1.154,0,0,1,656.9,42.5Z" transform="translate(-646.829 -26.25)"/>
        <path id="路径_4576" data-name="路径 4576" class="cls-5" d="M653.525,42.456a1.274,1.274,0,0,1-1.271-1.272V38.7a1.273,1.273,0,0,0-1.27-1.269H648.5a1.274,1.274,0,0,1-1.274-1.272V32.95a1.273,1.273,0,0,1,1.274-1.268h2.482a1.276,1.276,0,0,0,1.27-1.273V27.925a1.271,1.271,0,0,1,1.271-1.268h3.206A1.27,1.27,0,0,1,658,27.925v2.484a1.277,1.277,0,0,0,1.275,1.273h2.482a1.271,1.271,0,0,1,1.266,1.268v3.208a1.271,1.271,0,0,1-1.266,1.272h-2.482A1.274,1.274,0,0,0,658,38.7v2.486a1.273,1.273,0,0,1-1.27,1.272Z" transform="translate(-646.871 -26.294)"/>
        <path id="路径_4577" data-name="路径 4577" class="cls-6" d="M653.774,42.4a1.392,1.392,0,0,1-1.389-1.383V38.769A1.386,1.386,0,0,0,651,37.381h-2.254a1.392,1.392,0,0,1-1.388-1.387V33.2a1.392,1.392,0,0,1,1.388-1.387H651a1.388,1.388,0,0,0,1.385-1.387V28.171a1.394,1.394,0,0,1,1.389-1.388h2.8a1.392,1.392,0,0,1,1.385,1.388v2.252a1.391,1.391,0,0,0,1.385,1.387h2.25a1.39,1.39,0,0,1,1.389,1.387v2.8a1.389,1.389,0,0,1-1.389,1.387h-2.25a1.389,1.389,0,0,0-1.385,1.387V41.02A1.39,1.39,0,0,1,656.57,42.4Z" transform="translate(-646.912 -26.334)"/>
        <path id="路径_4578" data-name="路径 4578" class="cls-7" d="M654.02,42.357a1.505,1.505,0,0,1-1.5-1.5v-2.02a1.509,1.509,0,0,0-1.5-1.5H649a1.509,1.509,0,0,1-1.5-1.506v-2.38a1.508,1.508,0,0,1,1.5-1.507h2.02a1.508,1.508,0,0,0,1.5-1.5V28.421a1.508,1.508,0,0,1,1.5-1.5H656.4a1.512,1.512,0,0,1,1.506,1.5v2.017a1.5,1.5,0,0,0,1.5,1.5h2.025a1.506,1.506,0,0,1,1.5,1.507v2.38a1.507,1.507,0,0,1-1.5,1.506h-2.025a1.5,1.5,0,0,0-1.5,1.5v2.02a1.509,1.509,0,0,1-1.506,1.5Z" transform="translate(-646.956 -26.377)"/>
      </g>
      <path id="路径_4579" data-name="路径 4579" class="cls-8" d="M660.286,27.175c0,.141-.364.261-.808.261H655.7c-.446,0-.806-.12-.806-.261s.36-.259.806-.259h3.776C659.922,26.916,660.286,27.032,660.286,27.175Z" transform="translate(-649.332 -26.377)"/>
      <path id="路径_4580" data-name="路径 4580" class="cls-8" d="M651.766,34.643c0,.145-.36.265-.805.265H648.3c-.442,0-.806-.12-.806-.265s.364-.259.806-.259h2.662C651.406,34.384,651.766,34.5,651.766,34.643Z" transform="translate(-646.956 -28.774)"/>
      <path id="路径_4581" data-name="路径 4581" class="cls-8" d="M668.176,34.643c0,.145-.36.265-.805.265h-2.735c-.445,0-.809-.12-.809-.265s.364-.259.809-.259h2.735C667.816,34.384,668.176,34.5,668.176,34.643Z" transform="translate(-652.198 -28.774)"/>
    </g>
    <rect id="矩形_1086" data-name="矩形 1086" class="cls-9" width="24" height="24" transform="translate(615.968 -2.867)"/>
  </g>
</svg>
