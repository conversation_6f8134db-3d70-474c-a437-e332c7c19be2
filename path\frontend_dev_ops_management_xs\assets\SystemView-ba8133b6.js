import{d as t,o as n,c as a,e,b as s,h as o,R as l}from"./index-d5da4504.js";import{S as c}from"./SidebarComp-4efdfdd5.js";import{_ as d}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css             */const r={style:{display:"flex"}},m={class:"router-wrap"},h=t({__name:"SystemView",setup(p){const i=[{link:"",isActive:!0,isCollapse:!0,parent:"运营管理",children:[{isChildActive:!0,name:"资产包管理",link:"/home/<USER>/assetPackage",icon:"jt-20-asset-package"},{isChildActive:!1,isChildCollapse:!1,name:"调解管理",icon:"jt-20-mediation",children:[{isGrandChildActive:!1,name:"案件跟踪",link:"/home/<USER>/caseTracking",icon:"jt-20-disposal"},{isGrandChildActive:!1,name:"调解信息",link:"/home/<USER>/mediationInformation",icon:"jt-20-debtor-evaluation"},{isGrandChildActive:!0,name:"调解方案",link:"/home/<USER>/disposalPlan",icon:"jt-20-disposal-plan"},{isGrandChildActive:!1,name:"人员调度",link:"/home/<USER>/personnelDispatch",icon:"jt-20-personnel-dispatch"}]},{isChildActive:!1,name:"债权人管理",link:"/home/<USER>/creditor",icon:"jt-20-creditor"},{isChildActive:!1,name:"债务人管理",link:"/home/<USER>/debtor",icon:"jt-20-debtor"},{isChildActive:!1,name:"诉前保全管理",link:"/home/<USER>/preAction",icon:"jt-20-pre-action"},{isChildActive:!1,name:"信息修复",link:"/home/<USER>/informationRepair",icon:"jt-20-information-repair"},{isChildActive:!1,name:"案例展示",link:"/home/<USER>/caseShow",icon:"jt-20-creditor"},{isChildActive:!1,name:"投诉建议",link:"/home/<USER>/complaint",icon:"jt-20-complaint"},{isChildActive:!1,isChildCollapse:!1,name:"外呼管理",icon:"jt-20-outbound-call",children:[{isGrandChildActive:!1,name:"语音外呼记录",link:"/home/<USER>/outboundCall",icon:"jt-20-outbound-voice-call"},{isGrandChildActive:!1,name:"短信发送记录",link:"/home/<USER>/outboundCall",icon:"jt-20-message"}]}]},{link:"",isActive:!1,isCollapse:!1,parent:"数据管理",children:[{isChildActive:!1,name:"数据获取",link:"/home/<USER>/dataAcquisition",icon:"jt-20-data-acquisition"},{isChildActive:!1,name:"数据治理",link:"/home/<USER>/dataGovernance",icon:"jt-20-data-governance"},{isChildActive:!1,name:"数据分析",link:"/home/<USER>/dataAnalysis",icon:"jt-20-data-analysis"}]}];return(f,v)=>(n(),a("div",r,[e(c,{"menu-list":i}),s("div",m,[e(o(l))])]))}});const A=d(h,[["__scopeId","data-v-419e4e0e"]]);export{A as default};
