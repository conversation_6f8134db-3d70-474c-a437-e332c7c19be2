<script lang="ts" setup>
import { computed } from 'vue'
import CustomDialog from '@/components/common/CustomDialog.vue';

const props = defineProps<{
  showDialog: boolean
}>()

const emit = defineEmits<{
  (e: 'ensure'): void,
  (e: 'close'): void
}>()

let dialogVisible = computed({
  get() {
    return props.showDialog
  },
  set() {
    emit('close')
  }
})

function updateVisible() {
  dialogVisible.value = false
}
function ensureEditDepart() {
  emit('ensure')
}
</script>

<template>
  <CustomDialog 
    title="重置密码" 
    :visible="dialogVisible" 
    width="300px" 
    :markclose="true" 
    @update:visible="updateVisible">
    <div style="display: flex; flex-direction: column;gap: 16px;align-items: center;">
      <i class="jt-60-reset"></i>
      <span>确认重置密码？</span>
    </div>
    <div class="btns-group">
      <CustomButton @click="ensureEditDepart" :height="34" btn-type="blue"><i class="jt-20-ensure"></i>确认</CustomButton>
      <CustomButton @click="updateVisible" :height="34"><i class="jt-20-delete"></i>取消</CustomButton>
    </div>
  </CustomDialog>
</template>

<style lang="scss">
.btns-group {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 24px;
}
</style>