<script lang="ts" setup>
const emit = defineEmits<{
  (e: 'click'): void
}>() 
function clickEvent() {
  emit('click')
}
</script>

<template>
  <el-input>
    <!-- <template #prefix>
      <i class="jt-24-search"></i>
    </template> -->
    <template #suffix>
      <div class="search-btn" @click="clickEvent">搜索</div>
    </template>
  </el-input>
</template>

<style lang="scss" scoped>
.search-btn {
  width: 54px;
  height: 35px;
  line-height: 34px;
  margin-right: -10px;
  color: #fff;
  font-size: 16px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  background-color: #1377C4;
  cursor: pointer;
}
</style>