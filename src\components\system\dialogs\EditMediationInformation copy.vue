<script lang="ts" setup>
import { ref, type Ref, watch, computed } from "vue";
import CustomDialog from "@/components/common/CustomDialog.vue";
import CustomButton from "@/components/common/CustomButton.vue";
import AssetPackageSelector from "./AssetPackageSelector.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FieldType, MediationField as BaseMediationField, EditMediationParams, Mediation, UploadFile, FieldTypeOption, CaseStatusOption } from '../auth/type';
import { FieldType as FieldTypeEnum, CaseStatus } from '../auth/type';

/**
 * 扩展的调解字段接口，增加值锁定属性
 */
interface MediationField extends BaseMediationField {
  isValueLocked?: boolean
}

/**
 * 扩展的上传文件接口，增加服务器文件标识
 */
interface ExtendedUploadFile extends UploadFile {
  isServerFile?: boolean
}

/**
 * 资产包选择结果接口
 */
interface AssetSelectionResult {
  fileName: string
  selectedRow: Record<string, any>
  headers: Array<{ prop: string; label: string; width?: number }>
}

const props = defineProps<{
  showDialog: boolean,
  planData: Mediation  // 要编辑的方案数据
}>()

const emit = defineEmits<{
  (e: 'close'): void,
  (e: 'ensure', params: EditMediationParams): void
}>()

// 表单引用
const planFormRef = ref()
const loading = ref(false)

// 资产包选择器显示状态
const showAssetSelector = ref(false)

// 方案基础信息
const planForm = ref({
  title: ''
})

// 固定字段 - 案件状态
const caseStatus = ref<CaseStatus>(CaseStatus.PENDING)

// 固定字段 - 相关文件
const relatedFiles: Ref<ExtendedUploadFile[]> = ref([])

// 固定字段 - 备注
const remark = ref('')

// 动态字段列表（编辑模式下支持新增、删除和修改）
const dynamicFields: Ref<MediationField[]> = ref([])

// 案件状态选项配置（移除color属性）
const caseStatusOptions: CaseStatusOption[] = [
  { label: '待处理', value: CaseStatus.PENDING },
  { label: '处理中', value: CaseStatus.PROCESSING },
  { label: '调解中', value: CaseStatus.MEDIATING },
  { label: '已完成', value: CaseStatus.COMPLETED },
  { label: '已关闭', value: CaseStatus.CLOSED }
]

// 字段类型选项配置（移除文件上传选项）
const fieldTypeOptions: FieldTypeOption[] = [
  { label: '多行文本', value: FieldTypeEnum.TEXTAREA, icon: 'jt-24-edit' },
  { label: '日期选择', value: FieldTypeEnum.DATE, icon: 'jt-24-calendar' },
  { label: '金额输入', value: FieldTypeEnum.AMOUNT, icon: 'jt-24-money' }
]

/**
 * 获取浏览器存储的数据键值对
 */
const storedAssetData = computed(() => {
  const storageKey = `asset_data_${planForm.value.title}`
  const stored = localStorage.getItem(storageKey)
  try {
    return stored ? JSON.parse(stored) : {}
  } catch {
    return {}
  }
})

/**
 * 获取可用的字段标题选项（用于文本输入类型的字段）
 */
const availableFieldOptions = computed(() => {
  const data = storedAssetData.value
  return Object.keys(data).map(key => ({
    label: key,
    value: key
  }))
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请选择资产包名称', trigger: 'change' },
  ]
}

// 监听弹框显示状态
watch(() => props.showDialog, onOpenDialog)

// 监听方案数据变化
watch(() => props.planData, loadPlanData, { deep: true })

// 弹框打开时加载数据
function onOpenDialog(newVal: boolean) {
  if(newVal) {
    loadPlanData(props.planData)
  }
}

/**
 * 加载方案数据进行回显
 * @param planData 方案数据
 */
function loadPlanData(planData: Mediation) {
  if (!planData) return
  
  // 基础信息
  planForm.value.title = planData.title || ''
  
  // 固定字段
  caseStatus.value = planData.caseStatus || CaseStatus.PENDING
  
  // 文件回显处理
  relatedFiles.value = planData.fileList ? JSON.parse(JSON.stringify(planData.fileList)).map((file: UploadFile) => ({
    ...file,
    // 对于已存在的文件，标记为服务器文件
    status: 'success',
    isServerFile: true
  } as ExtendedUploadFile)) : []
  
  remark.value = planData.remark || ''
  
  // 动态字段深拷贝
  dynamicFields.value = planData.fields ? JSON.parse(JSON.stringify(planData.fields)).map((field: MediationField) => {
    // 已创建的字段默认设为必填
    field.required = true
    return field
  }) : []
}

// 关闭弹框
function close() {
  emit('close')
}

/**
 * 显示资产包选择器
 */
function showAssetPackageSelector() {
  showAssetSelector.value = true
}

/**
 * 处理资产包选择确认
 */
function handleAssetSelectionConfirm(result: AssetSelectionResult) {
  // 设置资产包名称
  planForm.value.title = result.fileName
  
  // 构造存储数据的键值对格式 - 表头作为key，数据作为value
  const storageData: Record<string, any> = {}
  result.headers.forEach(header => {
    const value = result.selectedRow[header.prop]
    if (value !== undefined && value !== null) {
      storageData[header.label] = value  // 表头label作为key，对应数据作为value
    }
  })
  
  // 存储到浏览器localStorage
  const storageKey = `asset_data_${result.fileName}`
  localStorage.setItem(storageKey, JSON.stringify(storageData))
  
  ElMessage.success('资产包信息已更新并保存')
}

// 生成唯一字段ID
function generateFieldId(): string {
  return 'GZTJ' + Date.now() + '000'+ Math.random().toString(36).substr(2, 9)
}

// 添加新字段
function addField() {
  const newField: MediationField = {
    id: generateFieldId(),
    title: '',
    type: FieldTypeEnum.TEXTAREA,
    value: '',
    required: true, // 新增字段也默认设为必填
    placeholder: ''
  }
  dynamicFields.value.push(newField)
}

// 删除字段
function removeField(index: number) {
  dynamicFields.value.splice(index, 1)
}

// 字段类型改变处理
function onFieldTypeChange(field: MediationField) {
  // 重置字段值和锁定状态
  field.value = getDefaultValueByType(field.type)
  field.isValueLocked = false
  field.title = ''
}

/**
 * 判断字段标题是否应该显示为选择器
 * 条件：字段类型=多行文本 + 资产包名称不为空 + 有存储的数据
 */
function shouldShowTitleSelector(field: MediationField): boolean {
  return field.type === FieldTypeEnum.TEXTAREA && 
         planForm.value.title && 
         availableFieldOptions.value.length > 0
}

/**
 * 处理字段标题选择
 */
function onFieldTitleSelect(field: MediationField, selectedKey: string) {
  field.title = selectedKey
  
  // 如果选择的key有对应的value，则自动填充并锁定
  const storedValue = storedAssetData.value[selectedKey]
  if (storedValue !== undefined && storedValue !== null) {
    field.value = storedValue
    field.isValueLocked = true  // 锁定值，禁用编辑
  } else {
    field.value = ''
    field.isValueLocked = false
  }
}

/**
 * 判断字段值是否应该被禁用
 */
function isFieldValueDisabled(field: MediationField): boolean {
  return field.isValueLocked || false
}

// 根据字段类型获取默认值
function getDefaultValueByType(type: FieldType): any {
  switch (type) {
    case FieldTypeEnum.TEXTAREA:
      return ''
    case FieldTypeEnum.DATE:
      return ''
    case FieldTypeEnum.AMOUNT:
      return 0
    default:
      return ''
  }
}

/**
 * 相关文件上传处理 - 支持多文件同时选择
 * @param event 文件选择事件
 */
function handleRelatedFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const fileList = target.files
  
  if (!fileList || fileList.length === 0) return
  
  // 遍历所有选中的文件
  Array.from(fileList).forEach(file => {
    const uploadFile: UploadFile = {
      id: generateFieldId(),
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file),
      file: file,
      status: 'success',
      isServerFile: false // 标记为新上传的文件
    }
    
    relatedFiles.value.push(uploadFile)
  })
  
  // 清空input值，允许重复选择相同文件
  target.value = ''
  ElMessage.success(`成功添加 ${fileList.length} 个文件`)
}

/**
 * 触发文件选择器（重新上传）
 */
function triggerFileSelector() {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true // 支持多文件选择
  input.accept = '*' // 不限制文件类型
  input.style.display = 'none'
  
  input.addEventListener('change', handleRelatedFileChange)
  
  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

// 删除相关文件
function removeRelatedFile(fileIndex: number) {
  const file = relatedFiles.value[fileIndex]
  // 释放创建的URL
  if (file.url && !file.isServerFile) {
    URL.revokeObjectURL(file.url)
  }
  relatedFiles.value.splice(fileIndex, 1)
}

/**
 * 下载相关文件
 * @param file 文件对象
 */
function downloadRelatedFile(file: UploadFile) {
  if (file.isServerFile) {
    // 服务器文件，调用下载API
    ElMessage.info('正在下载文件...')
    // TODO: 调用文件下载API
    // window.open(`/api/files/download/${file.id}`, '_blank')
  } else if (file.url) {
    // 本地文件，直接下载
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } else {
    ElMessage.error('文件下载失败')
  }
}

/**
 * 预览相关文件
 * @param file 文件对象
 */
function previewRelatedFile(file: UploadFile) {
  if (file.url) {
    // 如果是图片或PDF，可以直接预览
    if (file.type?.includes('image') || file.type?.includes('pdf')) {
      window.open(file.url, '_blank')
    } else {
      ElMessage.info('该文件类型不支持预览')
    }
  } else {
    ElMessage.info('文件预览功能开发中...')
    // TODO: 调用文件预览API
  }
}

// 生成微信小程序渲染格式的JSON数据
function generateWechatRenderData(): string {
  const renderData = {
    planTitle: planForm.value.title,
    caseStatus: caseStatus.value,
    remark: remark.value,
    relatedFiles: relatedFiles.value.map(f => f.name),
    sections: dynamicFields.value.map(field => ({
      title: field.title,
      content: formatFieldValueForRender(field),
      type: field.type
    }))
  }
  return JSON.stringify(renderData, null, 2)
}

// 格式化字段值用于渲染
function formatFieldValueForRender(field: MediationField): string {
  switch (field.type) {
    case FieldTypeEnum.DATE:
      return field.value ? new Date(field.value).toLocaleDateString() : ''
    case FieldTypeEnum.AMOUNT:
      return field.value ? `¥${Number(field.value).toFixed(2)}` : '¥0.00'
    default:
      return String(field.value || '')
  }
}

// 验证动态字段
function validateDynamicFields(): boolean {
  for (let i = 0; i < dynamicFields.value.length; i++) {
    const field = dynamicFields.value[i]
    
    // 验证字段标题不能为空
    if (!field.title.trim()) {
      ElMessage.error(`第${i + 1}个字段的标题不能为空`)
      return false
    }
    
    // 验证必填字段的内容
    if (field.required) {
      if (!field.value || String(field.value).trim() === '') {
        ElMessage.error(`${field.title}是必填字段，请填写内容`)
        return false
      }
    }
  }
  return true
}

// 获取字段类型显示名称（保留必填提示）
function getFieldTypeLabel(type: FieldType): string {
  const typeMap: Record<FieldType, string> = {
    [FieldTypeEnum.TEXTAREA]: '多行文本',
    [FieldTypeEnum.DATE]: '日期选择',
    [FieldTypeEnum.AMOUNT]: '金额输入',
    [FieldTypeEnum.FILE]: '文件上传'
  }
  return typeMap[type] || '未知类型'
}

// 计算字段类型图标类名
function getFieldTypeIcon(type: FieldType): string {
  const option = fieldTypeOptions.find(opt => opt.value === type)
  return option?.icon || 'jt-24-edit'
}

/**
 * 创建FormData对象用于文件上传
 * @returns FormData对象
 */
function createFormData(): FormData {
  const formData = new FormData()
  
  // 添加基础信息
  formData.append('id', props.planData.id!)
  formData.append('title', planForm.value.title)
  formData.append('caseStatus', caseStatus.value)
  formData.append('remark', remark.value)
  formData.append('jsonData', generateWechatRenderData())
  
  // 添加新上传的相关文件
  relatedFiles.value.forEach((fileItem, index) => {
    if (fileItem.file && !fileItem.isServerFile) { // 只添加新上传的文件
      formData.append(`relatedFiles`, fileItem.file)
      formData.append(`relatedFiles_${index}_id`, fileItem.id || '')
      formData.append(`relatedFiles_${index}_name`, fileItem.name)
    }
  })
  
  // 添加保留的服务器文件ID
  const existingFiles = relatedFiles.value.filter(file => file.isServerFile)
  formData.append('existingFiles', JSON.stringify(existingFiles.map(file => file.id)))
  
  // 添加动态字段数据
  formData.append('fields', JSON.stringify(dynamicFields.value))
  
  return formData
}

// 获取文件大小显示格式
function formatFileSize(size: number): string {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// 获取文件类型图标
function getFileTypeIcon(fileType: string): string {
  if (fileType.includes('image')) return 'jt-24-image'
  if (fileType.includes('pdf')) return 'jt-24-pdf'
  if (fileType.includes('word')) return 'jt-24-word'
  if (fileType.includes('excel')) return 'jt-24-excel'
  return 'jt-24-file'
}

// 确认编辑方案
async function ensureEdit() {
  if (!planFormRef.value) return
  
  loading.value = true
  try {
    // 验证基础表单
    const isValidForm = await new Promise((resolve) => {
      planFormRef.value.validate((valid: boolean) => {
        resolve(valid)
      })
    })
    
    if (!isValidForm) return
    
    // 验证动态字段
    if (!validateDynamicFields()) return
    
    // 检查是否至少有一个字段
    if (dynamicFields.value.length === 0) {
      ElMessage.error('请至少保留一个字段')
      return
    }
    
    // 构造提交数据
    const params: EditMediationParams = {
      id: props.planData.id!,
      title: planForm.value.title,
      fields: dynamicFields.value,
      jsonData: generateWechatRenderData(),
      caseStatus: caseStatus.value,
      fileList: relatedFiles.value,
      remark: remark.value,
      formData: createFormData() // 添加FormData用于文件上传
    }
    
    emit('ensure', params)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <CustomDialog :visible="showDialog" @update:visible="close" width="1200px" title="编辑方案">
    <div class="edit-plan-content">
      <!-- 基础信息表单 -->
      <el-form ref="planFormRef" :model="planForm" :rules="rules" label-width="110px">
        <!-- 方案ID显示（只读） -->
        <el-form-item label="调解案件号" v-if="planData.id">
          <el-input 
            :model-value="planData.id" 
            disabled 
            placeholder="系统自动生成" />
        </el-form-item>
        <el-form-item label="资产包名称" prop="title">
          <el-input 
            v-model="planForm.title" 
            readonly
            placeholder="请点击选择资产包名称"
            @click="showAssetPackageSelector"
            style="cursor: pointer;" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="planForm.remark" 
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="500"
            show-word-limit />
        </el-form-item>
      </el-form>

      <!-- 附件回显 -->
      <div class="fixed-fields-section">
        <div class="fixed-fields-content"> 
          <div class="field-group">
            <label class="field-label">相关文件</label>
            <div class="file-upload-section"> 
              <CustomButton @click="triggerFileSelector" :height="32">
                <i class="jt-20-upload"></i>选择文件
              </CustomButton>
               
              <div v-if="relatedFiles.length > 0" class="file-list">
                <!-- <div class="file-list-header">
                  <span>已选择 {{ relatedFiles.length }} 个文件</span>
                </div> -->
                <div 
                  v-for="(file, fileIndex) in relatedFiles" 
                  :key="file.id"
                  class="file-item">
                  <!-- <i :class="getFileTypeIcon(file.type || '')" class="file-icon"></i> -->
                  <span class="file-name" :title="file.name">{{ file.name }}</span>
                  <!-- <span class="file-size">{{ formatFileSize(file.size || 0) }}</span> -->
                  <i 
                    class="jt-20-delete file-remove" 
                    @click="removeRelatedFile(fileIndex)"
                    title="删除文件"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 固定字段区域 -->
      <!-- <div class="fixed-fields-section">
        <div class="fixed-fields-grid">=
          <div class="field-group">
            <label class="field-label">案件状态：</label>
            <el-select v-model="caseStatus" placeholder="请选择案件状态" style="width: 200px;">
              <el-option
                v-for="option in caseStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value">
                {{ option.label }}
              </el-option>
            </el-select>
          </div>
          
        </div>
        
        <div class="field-group full-width">
          <label class="field-label">备注：</label>
          <el-input 
            v-model="remark" 
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="500"
            show-word-limit />
        </div>
      </div> -->

      <!-- 动态字段配置区域 -->
      <div class="dynamic-fields-section">
        <!-- 区域头部：标题和添加按钮 -->
        <div class="section-header">
          <h3>调解信息配置</h3>
          <CustomButton @click="addField" :height="34" btn-type="blue">
            <i class="jt-20-addition"></i>添加字段
          </CustomButton>
        </div>

        <!-- 字段列表区域 -->
        <div class="fields-list" v-if="dynamicFields.length > 0">
          <div 
            v-for="(field, index) in dynamicFields" 
            :key="field.id"
            class="field-item">
            
            <!-- 字段头部：显示序号和删除按钮 -->
            <div class="field-header">
              <span class="field-index">字段 {{ index + 1 }}</span>
              <CustomButton 
                @click="removeField(index)" 
                :height="32" 
                btn-type="red">
                <i class="jt-20-remove"></i>删除
              </CustomButton>
            </div>

            <!-- 字段基础配置区域 -->
            <div class="field-config">
              <!-- 字段类型选择 -->
              <div class="config-row">
                <label class="config-label">字段类型：</label>
                <el-select 
                  v-model="field.type" 
                  @change="onFieldTypeChange(field)"
                  style="width: 280px;">
                  <el-option
                    v-for="option in fieldTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                    <span style="display: flex; align-items: center;">
                      <i :class="option.icon" style="margin-right: 8px;"></i>
                      {{ option.label }}
                    </span>
                  </el-option>
                </el-select>
              </div>

              <!-- 字段标题配置 -->
              <div class="config-row">
                <label class="config-label">字段标题：</label>
                <!-- 多行文本类型且有可选项时显示选择器 -->
                <el-select 
                  v-if="shouldShowTitleSelector(field)"
                  v-model="field.title"
                  @change="onFieldTitleSelect(field, $event)"
                  allow-create
                  filterable
                  placeholder="选择或输入字段标题"
                  style="width: 280px;">
                  <el-option
                    v-for="option in availableFieldOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value" />
                </el-select>
                <!-- 其他情况显示普通输入框 -->
                <el-input 
                  v-else
                  v-model="field.title" 
                  placeholder="请输入字段标题"
                  style="width: 280px;" />
              </div>

              <!-- 必填状态显示（已创建字段默认必填） -->
              <!-- <div class="config-row">
                <label class="config-label">是否必填：</label>
                <el-switch v-model="field.required" />
              </div> -->

              <!-- 占位符配置（文件类型不显示） -->
              <!-- <div class="config-row" v-if="field.type !== FieldTypeEnum.FILE">
                <label class="config-label">占位符：</label>
                <el-input 
                  v-model="field.placeholder" 
                  placeholder="请输入占位符文本"
                  style="width: 280px;" />
              </div> -->
            </div>

            <!-- 字段内容预览和编辑区域 -->
            <div class="field-preview">
              <label class="config-label">
                内容预览：
                <!-- 显示字段类型和必填状态 -->
                <el-tag size="small" :type="field.required ? 'danger' : 'info'">
                  {{ getFieldTypeLabel(field.type) }}{{ field.required ? ' (必填)' : '' }}
                </el-tag>
              </label>
              
              <div class="preview-content">
                <el-input 
                  v-if="field.type === FieldTypeEnum.TEXTAREA"
                  v-model="field.value"
                  type="textarea"
                  :rows="3"
                  :disabled="isFieldValueDisabled(field)"
                  :placeholder="field.placeholder || '请输入内容'" />
                
                <!-- 日期选择器 -->
                <el-date-picker
                  v-else-if="field.type === FieldTypeEnum.DATE"
                  v-model="field.value"
                  type="date"
                  :disabled="isFieldValueDisabled(field)"
                  :placeholder="field.placeholder || '选择日期'"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />
                
                <!-- 金额输入框 -->
                <el-input-number
                  v-else-if="field.type === FieldTypeEnum.AMOUNT"
                  v-model="field.value"
                  :min="0"
                  :precision="2"
                  :disabled="isFieldValueDisabled(field)"
                  :placeholder="field.placeholder || '请输入金额'"
                  style="width: 200px;" />
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-fields">
          <p>还没有添加任何字段，点击"添加字段"开始配置方案内容</p>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="btns-group">
        <CustomButton @click="ensureEdit" :loading="loading" :height="34" btn-type="blue">
          <i class="jt-20-ensure"></i>确认
        </CustomButton>
        <CustomButton @click="close" :height="34">
          <i class="jt-20-delete"></i>取消
        </CustomButton>
      </div>
    </div>
  </CustomDialog>

  <!-- 资产包选择器 -->
  <AssetPackageSelector 
    :visible="showAssetSelector"
    @update:visible="showAssetSelector = false"
    @confirm="handleAssetSelectionConfirm" />
</template>

<style lang="scss" scoped>
.edit-plan-content {
  .dynamic-fields-section {
    margin-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .empty-fields {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
      border-radius: 8px;
      border: 1px dashed #ddd;
    }
  }
  
  // 固定字段区域样式
  .fixed-fields-section {
    margin: 20px 0;
    
    .section-header {
      margin-bottom: 16px;
      padding: 5px 16px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 8px;
      border-left: 4px solid #1377C4;
      
      h3 {
        margin: 0;
        color: #0369a1;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .fixed-fields-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }
  .field-group{
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 36px;
  }
  .fields-list {
    .field-item {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 15px 20px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .field-index {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
          padding: 4px 12px;
          background-color: #ecf5ff;
          border-radius: 16px;
          border: 1px solid #b3d8ff;
        }
      }
      
      .field-config {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 10px;
        
        .config-row {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        
        .config-label {
          min-width: 90px;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
      
      .field-preview {
        .config-label {
          display: block;
          margin-bottom: 12px;
          font-weight: bold;
          color: #333;
          font-size: 15px;
        }
      }
    }
  }
  .file-upload-section{
    display: inline-block;
    width: 93%;
  }
  // 通用文件列表样式
  .file-list {
    margin-top: 12px;
    max-height: 250px;
    overflow-y: auto;
    // border: 1px solid #e6e6e6;
    border-radius: 6px;
    background-color: #f0f0f0;
    
    /* .file-list-header {
      padding: 8px 12px;
      background-color: #f0f0f0;
      border-bottom: 1px solid #e6e6e6;
      font-size: 13px;
      color: #666;
      font-weight: 500;
    } */
    
    .file-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 5px 12px;
      background-color: #e9edf7;
      transition: all 0.2s ease;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: #f9f9f9;
      }
      
     /*  .file-icon {
        color: #666;
        font-size: 16px;
        flex-shrink: 0;
      } */
      
      .file-name {
        flex: 1;
        color: #333;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .file-size {
        color: #999;
        font-size: 12px;
        background-color: #f0f0f0;
        padding: 2px 6px;
        border-radius: 10px;
        flex-shrink: 0;
      }
      
      .file-remove {
        color: #f56c6c;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;
        flex-shrink: 0;
        
        &:hover {
          color: #fff;
          background-color: #f56c6c;
        }
      }
    }
  }
  
  .btns-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 32px;
    padding: 24px 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .edit-plan-content {
    .dynamic-fields-section {
      .fields-list {
        .field-item {
          .field-config {
            grid-template-columns: 1fr;
          }
        }
      }
    }
    
    .btns-group {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>