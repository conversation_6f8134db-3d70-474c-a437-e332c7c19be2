{"name": "user-management", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.4.0", "element-plus": "^2.4.0", "pinia": "^2.1.4", "vue": "^3.3.4", "vue-expression-editor": "^1.1.3", "vue-router": "^4.2.4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.2", "@tsconfig/node18": "^18.2.0", "@types/node": "^18.17.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "npm-run-all": "^4.1.5", "prettier": "^3.0.0", "sass": "^1.64.1", "typescript": "~5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.6", "vue-tsc": "^1.8.6"}}