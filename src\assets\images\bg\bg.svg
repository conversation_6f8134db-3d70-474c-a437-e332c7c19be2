<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1920" height="970" viewBox="0 0 1920 970"><defs><style>.a{fill:#288edc;}.an,.b,.ct,.cw,.dx,.g,.l,.n,.o,.p,.r,.s{fill:none;}.c,.e{fill:#f3f7fc;}.d{clip-path:url(#a);}.e,.f{opacity:0.55;}.al,.f{fill:#fff;}.ct,.cw,.dx,.g{stroke:#1377c4;}.an,.ct,.cw,.dx,.g,.l,.n,.o,.p,.r,.s{stroke-miterlimit:10;}.g{stroke-width:1.322px;}.h,.p{opacity:0.5;}.h{fill:url(#h);}.ao,.i{fill:url(#i);}.j{fill:url(#j);}.k{fill:url(#k);}.an,.l,.o,.p,.r{stroke:#fff;}.an,.l,.n{stroke-linecap:round;}.an,.l{stroke-linejoin:round;}.l,.n,.o,.p,.r,.s{stroke-width:0.6px;}.m{fill:url(#l);}.n{stroke:#ffac27;}.o{opacity:0.4;}.q{fill:url(#m);}.s{stroke:#bb92f8;}.t{fill:url(#n);}.u{fill:#da7601;}.v{fill:url(#o);}.w{fill:url(#p);}.x{fill:url(#q);}.y{fill:url(#r);}.z{fill:url(#s);}.aa{fill:url(#t);}.ab{fill:url(#u);}.ac{fill:url(#v);}.ad{fill:#70c7ed;}.ae{fill:url(#w);}.af{fill:url(#x);}.ag{fill:url(#y);}.ah{fill:url(#z);}.ai{fill:url(#aa);}.aj{fill:url(#ab);}.ak{fill:#9bbce9;}.am{fill:#d6f4f9;}.an{stroke-width:0.82px;}.ao,.ax,.bg,.bk,.bo,.bs,.bw{opacity:0.3;}.ap,.aq{opacity:0.7;}.ap{fill:url(#ad);}.aq{fill:url(#ae);}.ar{fill:url(#af);}.as{fill:url(#ag);}.at{fill:url(#ah);}.au{fill:url(#ai);}.av{fill:url(#aj);}.aw{fill:url(#ak);}.ay,.et{fill:#1377c4;}.az{fill:#185294;}.ba{fill:url(#bo);}.bb,.dr,.ds,.dt,.du{opacity:0.2;}.bb{fill:url(#bp);}.bc{fill:url(#bq);}.bd{fill:url(#br);}.be{fill:url(#bs);}.bf{fill:url(#bt);}.bg{fill:url(#bu);}.bh{fill:url(#bv);}.bi{fill:url(#bw);}.bj{fill:url(#bx);}.bk{fill:url(#by);}.bl{fill:url(#bz);}.bm{fill:url(#ca);}.bn{fill:url(#cb);}.bo{fill:url(#cc);}.bp{fill:url(#cd);}.bq{fill:url(#ce);}.br{fill:url(#cf);}.bs{fill:url(#cg);}.bt{fill:url(#ch);}.bu{fill:url(#ci);}.bv{fill:url(#cj);}.bw{fill:url(#ck);}.bx{fill:url(#cl);}.by{fill:url(#cm);}.bz{fill:url(#cn);}.ca{fill:url(#co);}.cb{fill:url(#cp);}.cc{fill:url(#cq);}.cd{fill:url(#cr);}.ce{fill:url(#cs);}.cf{fill:url(#ct);}.cg{fill:url(#cu);}.ch{fill:url(#cv);}.ci{opacity:0.21;fill:url(#de);}.cj{fill:url(#df);}.ck{fill:url(#dg);}.cl{fill:#ffdedd;}.cm{fill:url(#dh);}.cn{fill:url(#di);}.co{fill:url(#dj);}.cp{fill:url(#dk);}.cq{fill:url(#dl);}.cr{fill:url(#dm);}.cs{fill:url(#do);}.ct{stroke-width:0.607px;}.ct,.cw{opacity:0.8;}.cu{fill:url(#dp);}.cv{fill:url(#dq);}.cw{stroke-width:0.556px;}.cx{opacity:0.47;fill:url(#dr);}.cy{fill:url(#ds);}.cz{fill:url(#dt);}.da{opacity:0.6;fill:url(#du);}.db{fill:url(#dv);}.dc{fill:url(#dw);}.dd{fill:url(#dx);}.de{fill:url(#eb);}.df{fill:#dfcaff;}.dg{fill:url(#ec);}.dh{fill:url(#ed);}.di{fill:url(#ef);}.dj{fill:url(#eg);}.dk{fill:url(#eh);}.dl{fill:url(#ej);}.dm{fill:url(#el);}.dn{fill:url(#en);}.do{fill:url(#ep);}.dp{fill:url(#er);}.dq{fill:url(#es);}.dr{fill:url(#et);}.ds{fill:url(#eu);}.dt{fill:url(#ev);}.du{fill:url(#ew);}.dv{fill:url(#ex);}.dw{fill:url(#ey);}.dx{stroke-width:1.02px;}.dy{fill:url(#ez);}.dz{fill:url(#fa);}.ea{fill:url(#fb);}.eb{fill:url(#fc);}.ec{fill:url(#fd);}.ed{fill:url(#fe);}.ee{fill:url(#ff);}.ef{fill:url(#fg);}.eg{fill:#dccaff;}.eh{fill:url(#fh);}.ei{fill:url(#fi);}.ej{fill:url(#fj);}.ek{fill:url(#fk);}.el,.em{fill:url(#fl);}.em,.eo,.eq,.es,.et,.ex,.ey,.fa{opacity:0.14;}.en,.eo{fill:url(#fn);}.ep,.eq{fill:url(#fp);}.er,.es{fill:url(#fr);}.eu{fill:url(#ft);}.ev{fill:url(#fv);}.ew,.ex{fill:url(#fw);}.ey{fill:url(#fy);}.ez,.fa{fill:url(#fz);}.fb{fill:url(#gb);}.fc{fill:url(#gc);}.fd{clip-path:url(#gd);}.fe{fill:url(#ge);}.ff{fill:url(#gf);}.fg{opacity:0.18;fill:url(#gg);}.fh{fill:#f3ffff;}.fi{fill:url(#gh);}.fj{fill:url(#gk);}.fk{fill:url(#gl);}.fl{fill:#b9d0ff;}.fm{fill:#bcd3ff;}.fn{filter:url(#gi);}.fo{filter:url(#e);}.fp{filter:url(#b);}</style><clipPath id="a"><rect class="a" width="1920" height="970"/></clipPath><filter id="b"><feOffset input="SourceAlpha"/><feGaussianBlur stdDeviation="30" result="c"/><feFlood flood-color="#d4e7ff" result="d"/><feComposite operator="out" in="SourceGraphic" in2="c"/><feComposite operator="in" in="d"/><feComposite operator="in" in2="SourceGraphic"/></filter><filter id="e"><feOffset input="SourceAlpha"/><feGaussianBlur stdDeviation="30" result="f"/><feFlood flood-color="#d4e7ff" result="g"/><feComposite operator="out" in="SourceGraphic" in2="f"/><feComposite operator="in" in="g"/><feComposite operator="in" in2="SourceGraphic"/></filter><linearGradient id="h" x1="0.238" y1="0.5" x2="1.129" y2="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#1f3278"/><stop offset="1" stop-color="#3d7add" stop-opacity="0"/></linearGradient><linearGradient id="i" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#95cdf5"/><stop offset="1" stop-color="#1377c4"/></linearGradient><linearGradient id="j" x1="0.888" y1="0.32" x2="-0.094" y2="0.674" gradientUnits="objectBoundingBox"><stop offset="0.007" stop-color="#8fd7ff"/><stop offset="0.253" stop-color="#a1e3ff"/><stop offset="0.731" stop-color="#c0f7ff"/><stop offset="1" stop-color="#cbffff"/></linearGradient><linearGradient id="k" x1="-0.46" y1="-0.592" x2="0.908" y2="0.964" xlink:href="#i"/><linearGradient id="l" x1="0.345" y1="0.107" x2="1.155" y2="2.318" xlink:href="#i"/><linearGradient id="m" x1="0.377" y1="-0.48" x2="0.585" y2="1.177" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#dee7ff"/><stop offset="1" stop-color="#b5ceff"/></linearGradient><linearGradient id="n" x1="0.514" y1="0.442" x2="0.279" y2="1.691" gradientUnits="objectBoundingBox"><stop offset="0.007" stop-color="#6fc6ff"/><stop offset="0.014" stop-color="#70c7ff"/><stop offset="0.465" stop-color="#a1e5ff"/><stop offset="0.808" stop-color="#c0f8ff"/><stop offset="1" stop-color="#cbffff"/></linearGradient><linearGradient id="o" x1="0.456" y1="0.797" x2="0.331" y2="1.683" xlink:href="#n"/><linearGradient id="p" x1="-0.057" y1="0.715" x2="1.263" y2="0.205" xlink:href="#n"/><linearGradient id="q" x1="0.47" y1="0.084" x2="0.505" y2="0.539" xlink:href="#n"/><linearGradient id="r" x1="1.223" y1="0.265" x2="-0.113" y2="0.698" xlink:href="#n"/><linearGradient id="s" x1="0.206" y1="1.082" x2="0.793" y2="-0.082" xlink:href="#n"/><linearGradient id="t" x1="0.517" y1="0.688" x2="0.404" y2="-0.163" xlink:href="#n"/><linearGradient id="u" x1="0.224" y1="1.15" x2="0.737" y2="-0.661" xlink:href="#n"/><linearGradient id="v" x1="0.402" y1="1.511" x2="0.615" y2="-0.45" xlink:href="#n"/><linearGradient id="w" x1="-0.146" y1="1.58" x2="0.277" y2="0.688" xlink:href="#n"/><linearGradient id="x" x1="-0.04" y1="2.034" x2="0.537" y2="0.523" xlink:href="#n"/><linearGradient id="y" x1="-0.127" y1="2.953" x2="0.446" y2="0.852" xlink:href="#n"/><linearGradient id="z" x1="-0.207" y1="1.188" x2="0.496" y2="0.629" xlink:href="#n"/><linearGradient id="aa" x1="0.562" y1="1.487" x2="0.47" y2="0.176" xlink:href="#n"/><linearGradient id="ab" x1="0.627" y1="1.634" x2="0.488" y2="0.272" xlink:href="#n"/><linearGradient id="ad" x1="1.214" y1="-0.262" x2="-0.231" y2="1.51" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="ae" x1="1.26" y1="-0.167" x2="-0.241" y2="1.474" xlink:href="#ad"/><linearGradient id="af" x1="0.069" y1="0.119" x2="0.565" y2="0.888" xlink:href="#i"/><linearGradient id="ag" x1="-0.018" y1="0.042" x2="1.392" y2="1.289" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#c5d7ff"/><stop offset="1" stop-color="#e6f1ff" stop-opacity="0.349"/></linearGradient><linearGradient id="ah" x1="-0.018" y1="0.042" x2="1.392" y2="1.289" xlink:href="#m"/><linearGradient id="ai" x1="0.189" y1="0.474" x2="2.306" y2="0.651" xlink:href="#i"/><linearGradient id="aj" x1="-0.351" y1="-0.152" x2="1.091" y2="0.953" xlink:href="#i"/><linearGradient id="ak" x1="0.144" y1="0.072" x2="0.808" y2="0.85" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#dee7ff"/><stop offset="0.18" stop-color="#f2f6ff"/><stop offset="1" stop-color="#b5ceff"/></linearGradient><linearGradient id="bo" x1="0.12" y1="0.162" x2="0.927" y2="0.884" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#dee7ff"/><stop offset="0.109" stop-color="#ebf0ff"/><stop offset="1" stop-color="#b5ceff"/></linearGradient><linearGradient id="bp" x1="0.209" y1="0.486" x2="1.029" y2="0.755" gradientUnits="objectBoundingBox"><stop offset="0.021" stop-color="#001a92"/><stop offset="0.159" stop-color="#001f96" stop-opacity="0.859"/><stop offset="0.323" stop-color="#002da0" stop-opacity="0.69"/><stop offset="0.498" stop-color="#0045b1" stop-opacity="0.514"/><stop offset="0.683" stop-color="#0066c9" stop-opacity="0.325"/><stop offset="0.873" stop-color="#0090e7" stop-opacity="0.129"/><stop offset="1" stop-color="#00b1ff" stop-opacity="0"/></linearGradient><linearGradient id="bq" x1="0.155" y1="0.386" x2="1.189" y2="0.721" xlink:href="#m"/><linearGradient id="br" x1="-0.067" y1="0.21" x2="1.226" y2="1.069" xlink:href="#ag"/><linearGradient id="bs" x1="0.561" y1="0.033" x2="0.387" y2="0.978" xlink:href="#m"/><linearGradient id="bt" x1="-0.231" y1="0.293" x2="0.963" y2="0.632" xlink:href="#m"/><linearGradient id="bu" x1="0.49" y1="0.531" x2="1.23" y2="0.727" gradientUnits="objectBoundingBox"><stop offset="0.001" stop-color="#2441a8"/><stop offset="1" stop-color="#0f57c2" stop-opacity="0"/></linearGradient><linearGradient id="bv" x1="0.19" y1="0.288" x2="1.48" y2="1.169" xlink:href="#i"/><linearGradient id="bw" x1="0.704" y1="0.404" x2="-0.581" y2="1.007" xlink:href="#i"/><linearGradient id="bx" x1="1.266" y1="1.169" x2="0.161" y2="0.204" xlink:href="#i"/><linearGradient id="by" x1="0.109" y1="0.462" x2="1.869" y2="0.763" xlink:href="#bu"/><linearGradient id="bz" x1="0.233" y1="0.257" x2="1.876" y2="1.753" xlink:href="#i"/><linearGradient id="ca" x1="0.844" y1="0.382" x2="-1.013" y2="1.019" xlink:href="#i"/><linearGradient id="cb" x1="1.075" y1="0.951" x2="-0.461" y2="-0.253" xlink:href="#i"/><linearGradient id="cc" x1="0.476" y1="0.513" x2="1.202" y2="0.86" xlink:href="#bu"/><linearGradient id="cd" x1="0.211" y1="0.178" x2="1.343" y2="1.439" xlink:href="#i"/><linearGradient id="ce" x1="1.32" y1="0.402" x2="-0.855" y2="0.662" xlink:href="#i"/><linearGradient id="cf" x1="0.884" y1="0.831" x2="-0.096" y2="-0.014" xlink:href="#i"/><linearGradient id="cg" x1="-0.772" y1="0.418" x2="2.058" y2="0.644" xlink:href="#bu"/><linearGradient id="ch" x1="0.248" y1="0.208" x2="1.77" y2="1.968" xlink:href="#i"/><linearGradient id="ci" x1="0.781" y1="0.34" x2="-0.827" y2="1.255" xlink:href="#i"/><linearGradient id="cj" x1="1.04" y1="0.832" x2="-0.136" y2="0.11" xlink:href="#i"/><linearGradient id="ck" x1="0.456" y1="0.505" x2="1.333" y2="0.676" xlink:href="#bu"/><linearGradient id="cl" x1="0.16" y1="0.157" x2="1.731" y2="1.745" xlink:href="#i"/><linearGradient id="cm" x1="0.657" y1="0.375" x2="-0.387" y2="1.204" xlink:href="#i"/><linearGradient id="cn" x1="0.912" y1="0.906" x2="-0.047" y2="-0.04" xlink:href="#i"/><linearGradient id="co" x1="0.407" y1="1.209" x2="0.573" y2="-0.059" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#fff7c9"/><stop offset="1" stop-color="#fa0"/></linearGradient><linearGradient id="cp" x1="0.5" y1="0.824" x2="0.5" y2="-0.528" xlink:href="#co"/><linearGradient id="cq" x1="-0.75" y1="0.267" x2="1.462" y2="0.68" xlink:href="#co"/><linearGradient id="cr" x1="-0.824" y1="1.068" x2="0.226" y2="0.538" xlink:href="#co"/><linearGradient id="cs" x1="0.583" y1="1.12" x2="0.369" y2="-0.484" xlink:href="#co"/><linearGradient id="ct" x1="-2.229" y1="-0.452" x2="2.32" y2="1.078" xlink:href="#i"/><linearGradient id="cu" x1="-0.527" y1="0.513" x2="0.57" y2="0.501" xlink:href="#i"/><linearGradient id="cv" x1="0.334" y1="0.5" x2="1.358" y2="0.5" xlink:href="#i"/><linearGradient id="de" x1="-0.287" y1="0.5" x2="0.815" y2="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#00327f"/><stop offset="1" stop-color="#003a7f" stop-opacity="0"/></linearGradient><linearGradient id="df" x1="1.223" y1="0.544" x2="-0.896" y2="0.382" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#1e5475"/><stop offset="1" stop-color="#0064bc"/></linearGradient><linearGradient id="dg" x1="1.514" y1="1.129" x2="-1.629" y2="-0.726" xlink:href="#i"/><linearGradient id="dh" x1="1.058" y1="0.8" x2="0.404" y2="0.375" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#40a6ef"/><stop offset="1" stop-color="#064e85"/></linearGradient><linearGradient id="di" x1="0.916" y1="0.913" x2="0.191" y2="-0.324" xlink:href="#i"/><linearGradient id="dj" x1="0.457" y1="0.435" x2="-1.073" y2="-2.453" xlink:href="#i"/><linearGradient id="dk" x1="0.67" y1="-0.183" x2="0.187" y2="1.699" xlink:href="#i"/><linearGradient id="dl" x1="1.288" y1="1.654" x2="0.213" y2="0.08" xlink:href="#m"/><linearGradient id="dm" x1="0.159" y1="0.13" x2="0.894" y2="0.861" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#001fd4"/><stop offset="1" stop-color="#86d3ff" stop-opacity="0"/></linearGradient><linearGradient id="do" x1="1.194" y1="1.516" x2="0.119" y2="-0.058" xlink:href="#m"/><linearGradient id="dp" x1="-0.351" y1="-1.796" x2="1.412" y2="2.961" xlink:href="#co"/><linearGradient id="dq" x1="0.439" y1="0.636" x2="-1.393" y2="4.488" xlink:href="#co"/><linearGradient id="dr" x1="0.078" y1="0.007" x2="2.544" y2="2.755" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#ff0007"/><stop offset="1" stop-color="#ffaaac"/></linearGradient><linearGradient id="ds" x1="-0.516" y1="-1.755" x2="1.588" y2="2.903" xlink:href="#co"/><linearGradient id="dt" x1="-0.44" y1="-2.165" x2="1.5" y2="3.342" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#d4fff1"/><stop offset="0.319" stop-color="#e5c6c0"/><stop offset="0.631" stop-color="#f39496"/><stop offset="0.868" stop-color="#fc757c"/><stop offset="1" stop-color="#ff6a72"/></linearGradient><linearGradient id="du" x1="-0.13" y1="0.391" x2="0.646" y2="0.792" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#8031e9"/><stop offset="1" stop-color="#3f95d8" stop-opacity="0"/></linearGradient><linearGradient id="dv" x1="0.783" y1="1.269" x2="0.289" y2="-0.014" xlink:href="#m"/><linearGradient id="dw" x1="-0.406" y1="0.345" x2="1.312" y2="0.634" xlink:href="#m"/><linearGradient id="dx" x1="-0.085" y1="0.518" x2="1.38" y2="0.474" xlink:href="#i"/><linearGradient id="eb" x1="-1.136" y1="0.55" x2="0.329" y2="0.505" xlink:href="#ag"/><linearGradient id="ec" x1="-0.014" y1="-0.017" x2="0.862" y2="1.01" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#1377c4"/><stop offset="0.995" stop-color="#00c6fb"/><stop offset="1" stop-color="#00c6fb"/></linearGradient><linearGradient id="ed" x1="-0.711" y1="0.202" x2="1.485" y2="0.992" gradientUnits="objectBoundingBox"><stop offset="0.007" stop-color="#ff9ffc"/><stop offset="0.031" stop-color="#f7a0fc"/><stop offset="0.31" stop-color="#a1adfc"/><stop offset="0.556" stop-color="#5cb8fb"/><stop offset="0.76" stop-color="#2ac0fb"/><stop offset="0.914" stop-color="#0bc4fb"/><stop offset="1" stop-color="#00c6fb"/></linearGradient><linearGradient id="ef" x1="-0.711" y1="0.202" x2="1.485" y2="0.992" xlink:href="#ed"/><linearGradient id="eg" x1="1.014" y1="-0.017" x2="-0.098" y2="1.286" xlink:href="#ec"/><linearGradient id="eh" x1="1.711" y1="0.202" x2="-0.485" y2="0.992" xlink:href="#ed"/><linearGradient id="ej" x1="1.711" y1="0.202" x2="-0.485" y2="0.992" xlink:href="#ed"/><linearGradient id="el" x1="-0.711" y1="0.202" x2="1.485" y2="0.992" xlink:href="#ed"/><linearGradient id="en" x1="-0.711" y1="0.202" x2="1.485" y2="0.992" xlink:href="#ed"/><linearGradient id="ep" x1="1.711" y1="0.202" x2="-0.485" y2="0.992" xlink:href="#ed"/><linearGradient id="er" x1="1.711" y1="0.202" x2="-0.485" y2="0.992" xlink:href="#ed"/><linearGradient id="es" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#co"/><linearGradient id="et" x1="-2.697" y1="0.5" x2="6.253" y2="0.5" xlink:href="#m"/><linearGradient id="eu" x1="-2.697" y1="0.5" x2="6.253" y2="0.5" xlink:href="#m"/><linearGradient id="ev" x1="-2.697" y1="0.5" x2="6.253" y2="0.5" xlink:href="#m"/><linearGradient id="ew" x1="-2.697" y1="0.5" x2="6.253" y2="0.5" xlink:href="#m"/><linearGradient id="ex" x1="0.785" y1="0.633" x2="0.518" y2="0.203" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#dee7ff" stop-opacity="0"/><stop offset="1" stop-color="#b5ceff"/></linearGradient><linearGradient id="ey" x1="0.237" y1="0.626" x2="0.618" y2="0.156" xlink:href="#ex"/><linearGradient id="ez" x1="-0.078" x2="0.601" y2="0.821" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#240ea8"/><stop offset="0.209" stop-color="#1811b7" stop-opacity="0.522"/><stop offset="1" stop-color="#0f13c2" stop-opacity="0"/></linearGradient><linearGradient id="fa" x1="0.056" y1="0.38" x2="0.871" y2="0.878" xlink:href="#m"/><linearGradient id="fb" x1="0.071" y1="0.781" x2="1.312" y2="0.634" xlink:href="#m"/><linearGradient id="fc" x1="-0.085" y1="0.518" x2="1.38" y2="0.474" xlink:href="#i"/><linearGradient id="fd" x1="0" y1="0.211" x2="0.955" y2="0.783" xlink:href="#m"/><linearGradient id="fe" x1="0.011" y1="0.785" x2="0.98" y2="0.208" xlink:href="#m"/><linearGradient id="ff" x1="0.5" y1="0" x2="0.5" y2="1" xlink:href="#i"/><linearGradient id="fg" x1="0.5" y1="0" x2="0.5" y2="1" xlink:href="#m"/><linearGradient id="fh" x1="-0.014" y1="-0.017" x2="0.862" y2="1.01" xlink:href="#i"/><linearGradient id="fi" x1="-0.014" y1="-0.017" x2="0.862" y2="1.01" xlink:href="#i"/><linearGradient id="fj" x1="1.014" y1="-0.017" x2="-0.098" y2="1.286" xlink:href="#i"/><linearGradient id="fk" x1="1.014" y1="-0.017" x2="-0.14" y2="1.336" xlink:href="#i"/><linearGradient id="fl" x1="-0.014" y1="-0.017" x2="0.862" y2="1.01" xlink:href="#i"/><linearGradient id="fn" x1="-0.014" y1="-0.017" x2="0.862" y2="1.01" xlink:href="#i"/><linearGradient id="fp" x1="1.014" y1="-0.017" x2="-0.057" y2="1.239" xlink:href="#i"/><linearGradient id="fr" x1="1.014" y1="-0.017" x2="-0.015" y2="1.189" xlink:href="#i"/><linearGradient id="ft" x1="0.114" y1="-0.213" x2="1.178" y2="1.784" xlink:href="#co"/><linearGradient id="fv" x1="0.114" y1="-0.213" x2="1.178" y2="1.784" xlink:href="#co"/><linearGradient id="fw" x1="0.114" y1="-0.212" x2="1.179" y2="1.784" xlink:href="#i"/><linearGradient id="fy" x1="0.114" y1="-0.212" x2="1.178" y2="1.784" xlink:href="#i"/><linearGradient id="fz" x1="0.5" y1="0" x2="0.5" y2="1" xlink:href="#co"/><linearGradient id="gb" x1="0.543" y1="1.086" x2="0.341" y2="-1.477" gradientUnits="objectBoundingBox"><stop offset="0.007" stop-color="#52e5e7"/><stop offset="1" stop-color="#130cb7"/></linearGradient><linearGradient id="gc" x1="0.955" y1="1.012" x2="-0.605" y2="-0.714" xlink:href="#i"/><clipPath id="gd"><path class="b" d="M28.006,48.926,0,32.583.244,0,28.25,16.343Z"/></clipPath><linearGradient id="ge" x1="0.543" y1="1.086" x2="0.341" y2="-1.477" xlink:href="#i"/><linearGradient id="gf" x1="0.077" y1="1.273" x2="1.199" y2="-0.799" xlink:href="#i"/><linearGradient id="gg" x1="0.487" y1="0.923" x2="0.51" y2="-0.008" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#0defff"/><stop offset="0.145" stop-color="#6ad2f9" stop-opacity="0.973"/><stop offset="1" stop-color="#5ab1c6" stop-opacity="0"/></linearGradient><linearGradient id="gh" x1="-0.434" y1="0.5" x2="0.566" y2="0.5" xlink:href="#i"/><filter id="gi" x="489.252" y="374.693" width="179.321" height="207.784" filterUnits="userSpaceOnUse"><feOffset dx="3" dy="20" input="SourceAlpha"/><feGaussianBlur stdDeviation="10" result="gj"/><feFlood flood-color="#185294" flood-opacity="0.42"/><feComposite operator="in" in2="gj"/><feComposite in="SourceGraphic"/></filter><linearGradient id="gk" x1="-0.199" y1="0.829" x2="0.596" y2="0.357" xlink:href="#i"/><linearGradient id="gl" x1="0.106" y1="0.326" x2="1.095" y2="0.739" xlink:href="#i"/></defs><rect class="c" width="1920" height="970"/><g class="d"><g data-type="innerShadowGroup"><rect class="e" width="1444" height="551" rx="21" transform="matrix(0.891, -0.454, 0.454, 0.891, -432.381, 51.809)"/><g class="fp" transform="matrix(1, 0, 0, 1, 0, 0)"><rect class="f" width="1444" height="551" rx="21" transform="matrix(0.89, -0.45, 0.45, 0.89, -432.38, 51.81)"/></g></g><g data-type="innerShadowGroup"><path class="e" d="M0,0,748.146,26.812l441.262,850.915H0Z" transform="matrix(0.891, -0.454, 0.454, 0.891, -120.381, 921.809)"/><g class="fo" transform="matrix(1, 0, 0, 1, 0, 0)"><path class="f" d="M0,0,748.146,26.812l441.262,850.915H0Z" transform="matrix(0.89, -0.45, 0.45, 0.89, -120.38, 921.81)"/></g></g></g><g transform="translate(-6748.652 159.348)"><rect class="b" width="652" height="652" transform="translate(7016.652 -0.348)"/><g transform="translate(7069.844 109.775)"><g transform="translate(378.948 219.025)"><g transform="translate(0 0)"><path class="g" d="M0,49.112,47.633,20.358a1.737,1.737,0,0,0,.839-1.487v-5.2a1.505,1.505,0,0,1,.819-1.339l8.617-4.409a1.247,1.247,0,0,0,.679-1.11V0"/></g></g><g transform="translate(118.299 187.866)"><path class="g" d="M4.245-18.744,22.535-4.462C24.154-5.4,35.408-9.582,40.781-13.8L75.543,14.12a5.108,5.108,0,0,0,5.487-.1l7.52,27.2" transform="translate(-4.245 18.744)"/></g><g transform="translate(325.792 260.018)"><g transform="translate(0 0)"><path class="g" d="M0,0,57.786,34.908" transform="translate(33.159 50.511)"/><path class="g" d="M33.159,50.511,12.014,37.795a1.246,1.246,0,0,1,.011-2.139l22.852-13.5A1.247,1.247,0,0,0,34.855,20L0,0"/></g></g><g transform="translate(82.269 312.34)"><g transform="translate(0 0)"><path class="g" d="M7.184,48.935l16.291-9.849a1.246,1.246,0,0,0-.011-2.139L.612,23.45A1.247,1.247,0,0,1,.635,21.29L38.087,0" transform="translate(55.336)"/><path class="g" d="M0,34.908,62.52,0" transform="translate(0 48.935)"/></g></g><g transform="translate(263.32 129.636)"><g transform="translate(0 0)"><path class="g" d="M7.184,48.935l16.291-9.849a1.246,1.246,0,0,0-.011-2.139L.612,23.45A1.247,1.247,0,0,1,.635,21.29L38.087,0" transform="translate(55.336)"/><path class="g" d="M0,34.908,62.52,0" transform="translate(0 48.935)"/></g></g><g transform="translate(0 51.111)"><g transform="translate(0 191.659)"><g transform="translate(0 0)"><ellipse class="h" cx="54.353" cy="23.605" rx="54.353" ry="23.605" transform="translate(0.381 141.773)"/><path class="i" d="M.039,0,0,29.963a12.509,12.509,0,0,0,4.283,9.454C10.229,44.6,22.822,52.354,44.89,52.354c20.039,0,30.852-7.817,35.816-13A12.537,12.537,0,0,0,84.194,30.7L84.234,0H.039Z" transform="translate(0 135.366)"/><path class="j" d="M68.087.212C75,4.2,79.3,9.757,79.3,15.912c0,12.113-16.636,21.933-37.157,21.933S4.986,28.025,4.986,15.912C4.986,9.647,9.436,4,16.571,0,6.5,4.605,0,11.869,0,20.042c0,13.92,18.848,25.2,42.1,25.2s42.1-11.285,42.1-25.2C84.2,11.995,77.895,4.828,68.087.212Z" transform="translate(0.04 115.323)"/><ellipse class="k" cx="37.157" cy="21.933" rx="37.157" ry="21.933" transform="translate(5.026 109.302)"/><path class="l" d="M0,0S3.868,12.918,27.371,17.791" transform="translate(3.059 137.948)"/><path class="l" d="M15.246,0S23.011,16.968,0,26.541" transform="translate(64.61 126.351)"/><g transform="translate(39.425 154.928) rotate(-8)"><path class="m" d="M1.812,0,0,4.568s9.595,2.478,17.842.354L16.477.343A47.784,47.784,0,0,1,1.812,0Z" transform="translate(0 0)"/><path class="n" d="M0,2.03S2.571-.116,4.278,1.357c3.017,2.6,4.824.382,6.035-.479,1.693-1.2,2.468-1.062,3.717-.235" transform="translate(1.72 1.821)"/><path class="o" d="M1.116,0,0,3.761" transform="translate(3.534 0.934)"/><path class="o" d="M.573,0,0,3.72" transform="translate(7.072 1.262)"/><path class="p" d="M0,0,.255,3.975" transform="translate(10.547 1.262)"/><path class="p" d="M0,0,.925,3.861" transform="translate(13.702 1.121)"/></g><ellipse class="q" cx="32.278" cy="19.053" rx="32.278" ry="19.053" transform="translate(9.813 112.183)"/><path class="r" d="M84.235,0S79.8,19.542,43.828,19.521C8.619,19.5,0,0,0,0" transform="translate(0 146.843)"/><path class="r" d="M84.235,0S79.8,19.542,43.828,19.521C8.619,19.5,0,0,0,0" transform="translate(0.02 152.692)"/><path class="s" d="M59.09,0A64.063,64.063,0,0,1,0,0" transform="translate(12.381 153.282)"/><g transform="translate(16.418 90.53)"><g transform="translate(0 0)"><path class="t" d="M17.029,33.475l-.107,0q-.41-.011-.816-.031l-.131-.006q-.424-.023-.843-.055l-.084-.006-.079-.008q-.475-.039-.943-.092l-.14-.016c-.316-.037-.629-.078-.94-.125l-.113-.018q-.478-.073-.946-.161l-.07-.014c-.325-.063-.648-.13-.966-.2-5.6-1.3-8.932-4.1-9.8-7.6L0,20.883c.643,2.6,2.649,4.811,5.939,6.311a6.986,6.986,0,0,1-1.606-2.977h0L3.857,22.3l-.579-2.337h0c-.028-.112-.051-.226-.074-.34-.006-.035-.012-.07-.018-.105-.014-.082-.028-.164-.039-.246-.006-.039-.011-.078-.016-.117-.01-.087-.019-.173-.026-.26,0-.032-.006-.064-.008-.1-.008-.118-.013-.237-.015-.356,0-.021,0-.041,0-.063,0-.1,0-.2,0-.3,0-.04,0-.08.006-.119,0-.084.009-.169.016-.254,0-.04.007-.082.011-.122.009-.1.021-.193.034-.29,0-.026.007-.053.011-.08q.027-.184.063-.371c0-.008,0-.014,0-.022s.01-.043.014-.064c.021-.105.045-.212.071-.318.009-.039.019-.08.029-.119.028-.111.06-.223.093-.334.007-.024.013-.047.021-.07q.062-.2.134-.4l.039-.106q.057-.155.12-.312l.048-.119c.055-.132.113-.263.174-.395l.006-.014c.064-.137.132-.274.2-.411l.062-.118c.055-.105.113-.21.172-.315.021-.038.041-.075.063-.112.08-.138.162-.276.248-.414l.037-.058q.115-.18.236-.361c.028-.041.056-.082.085-.124.075-.11.153-.219.232-.329l.07-.1c.1-.139.208-.277.317-.415l.075-.094q.132-.166.271-.331l.105-.124c.1-.119.205-.237.311-.356.02-.021.039-.044.058-.065.125-.139.255-.277.387-.416l.107-.11q.155-.16.317-.32l.121-.119c.143-.139.287-.278.437-.416q.293-.272.6-.537c.068-.06.139-.117.209-.176.137-.117.275-.234.415-.349l.03-.025c.074-.06.151-.119.226-.179.13-.1.259-.207.391-.308.093-.072.189-.142.284-.213.128-.1.256-.191.386-.286.1-.072.2-.143.3-.214.129-.091.257-.181.387-.271.105-.072.212-.142.319-.213.13-.087.26-.174.392-.259q.164-.105.33-.209c.132-.083.265-.166.4-.248l.341-.2c.135-.08.27-.16.406-.238l.35-.2c.137-.076.275-.154.414-.229l.357-.192c.14-.075.282-.149.423-.222l.364-.186c.144-.071.287-.143.431-.213l.37-.178c.145-.069.293-.137.44-.205.124-.057.248-.114.374-.17.149-.066.3-.132.448-.2l.377-.162c.152-.065.305-.127.459-.19.125-.051.251-.1.377-.153.157-.062.314-.122.471-.183l.375-.143.094-.036q.333-.123.669-.241l.114-.039c.227-.08.455-.156.683-.232l.234-.076q.317-.1.636-.2l.175-.054q.4-.121.8-.234l.187-.052q.317-.088.635-.172l.237-.062q.361-.092.724-.179l.09-.022q.4-.095.809-.182l.223-.048c.208-.044.416-.087.623-.127L26.5.705c.27-.052.541-.1.811-.147L27.4.544q.364-.06.727-.115l.239-.034c.212-.031.424-.06.636-.087l.19-.024q.4-.05.8-.091l.176-.017c.214-.021.428-.04.64-.057L31.05.1c.229-.017.458-.033.687-.045l.117-.007c.264-.014.526-.024.788-.032l.211-.005c.2-.005.4-.008.6-.009h.22c.26,0,.519,0,.776.008l.051,0c.241.006.48.016.717.027l.223.013c.2.011.393.024.588.039L36.162.1l.05.005q.415.034.823.08L37.215.2q.374.045.743.1l.1.014q.4.062.8.136l.184.035c.265.052.529.106.789.166a14.119,14.119,0,0,1,6.2,2.914c.046.04.1.079.141.12.09.082.171.168.256.252.069.069.142.135.208.206.134.143.26.29.381.439a.416.416,0,0,0,.036.039c.1.122.186.247.274.372.03.042.064.083.092.125.076.113.143.229.211.344.034.057.073.113.105.171.041.074.075.152.113.227.051.1.107.2.153.306.011.024.019.05.029.074.068.157.132.315.187.475.024.07.041.143.063.213.036.117.075.233.1.351l.583,2.357.47,1.9a6.667,6.667,0,0,1,.158.894c.011.1.019.2.026.3.013.2.017.411.012.619s-.019.418-.042.63a8.188,8.188,0,0,1-.164.965l0,.014c2.161-3.027,2.882-6.007,2.24-8.6l1.053,4.258A7.807,7.807,0,0,1,52.923,12c.008.117.013.234.014.352a8.638,8.638,0,0,1-.1,1.443q-.055.367-.14.739a10.906,10.906,0,0,1-.467,1.507,14.622,14.622,0,0,1-1.69,3.092q-.275.39-.582.782a23.579,23.579,0,0,1-2.542,2.736c-.206.191-.418.379-.633.567l-.121.106q-.312.269-.635.533l-.047.039-.076.061q-.33.267-.67.528l-.019.014c-.231.177-.467.35-.705.524l-.136.1q-.348.249-.705.493l-.133.09c-.24.162-.481.322-.727.479l-.034.022q-.379.242-.766.477l-.15.092q-.378.228-.764.448l-.14.08q-.384.217-.773.428l-.049.027q-.4.217-.816.427l-.163.083q-.4.2-.813.4l-.144.068q-.4.191-.809.377l-.066.03c-.283.129-.569.253-.855.376l-.172.073q-.423.18-.851.352L35.3,30q-.417.165-.836.324l-.082.031q-.44.165-.885.321l-.179.063q-.438.153-.88.3l-.146.047q-.425.138-.853.27l-.094.029q-.45.137-.9.265l-.186.052c-.3.084-.6.166-.9.243l-.142.036q-.432.111-.864.214l-.105.026c-.3.072-.608.14-.911.206l-.189.04c-.3.064-.6.126-.906.184l-.137.026c-.287.055-.576.107-.863.156l-.117.021q-.455.077-.909.144l-.188.028c-.3.044-.6.085-.9.123L23,33.164q-.428.053-.855.1l-.124.013q-.45.045-.9.08l-.185.014c-.3.023-.6.043-.893.058l-.118.006q-.421.022-.84.034l-.129,0q-.439.011-.875.013H17.9C17.608,33.485,17.318,33.482,17.029,33.475Z" transform="translate(0.227 1.981)"/><g transform="translate(3.322 2.625)"><path class="u" d="M0,0H0Z" transform="translate(1.237 23.573)"/><path class="v" d="M13.931,32.835l-.107,0q-.41-.01-.816-.031l-.131-.006q-.424-.023-.843-.056l-.084-.006-.079-.008q-.475-.039-.943-.092l-.14-.016c-.316-.038-.629-.078-.94-.125l-.113-.018q-.478-.073-.946-.161l-.07-.014c-.325-.062-.648-.13-.966-.2q-1.005-.233-1.91-.53c-1.152-1.69-2.293-3.4-3.3-5.156.1.048.2.1.3.143a7.53,7.53,0,0,1-.91-1.26c-.1-.181-.192-.361-.284-.543a6.543,6.543,0,0,1-.413-1.174h0L.76,21.656.181,19.318h0C.154,19.2.13,19.091.107,18.977.1,18.941.1,18.906.089,18.871c-.014-.082-.028-.164-.039-.246-.006-.04-.011-.078-.016-.118-.01-.086-.019-.172-.026-.26,0-.031-.006-.063-.008-.1,0-.007,0-.014,0-.021L2.7,16.8l-.01.015c-.087.138-.169.276-.248.414-.022.038-.042.075-.063.112-.06.105-.117.21-.172.315l-.062.118c-.071.138-.139.275-.2.411L1.93,18.2q-.092.2-.174.394l-.048.119q-.064.156-.12.312c-.013.035-.026.07-.039.105-.048.135-.093.268-.134.4-.007.024-.013.047-.021.071q-.05.167-.093.334l-.03.119c-.026.107-.049.213-.071.319-.006.028-.013.057-.018.085-.024.124-.045.248-.063.371,0,.027-.007.053-.011.08-.013.1-.024.194-.034.29,0,.041-.007.082-.01.122,0,.006,0,.014,0,.02a7.59,7.59,0,0,0,.866,1.115,9.415,9.415,0,0,0,1.539,1.309,12.017,12.017,0,0,0,1.267.757,17.062,17.062,0,0,0,4,1.428q.451.1.915.192c.1.019.208.034.312.053.208.036.415.073.626.1.126.019.255.034.382.051.191.026.382.052.576.073.137.016.276.028.415.041.19.018.38.037.572.052l.326.021c.209.013.418.025.628.034l.355.013q.314.01.632.014c.114,0,.228,0,.342,0,.243,0,.488,0,.734-.008.081,0,.162,0,.243,0,.326-.009.655-.022.984-.04.077,0,.155-.01.233-.015.254-.015.508-.033.764-.053.117-.009.234-.021.351-.031.22-.02.44-.041.66-.065l.374-.042c.221-.026.443-.055.665-.085l.353-.048q.391-.056.785-.121l.226-.036q.5-.086,1.01-.184l.26-.053c.251-.05.5-.1.754-.157l.359-.082q.331-.075.661-.155l.376-.093q.335-.085.668-.176l.344-.093c.271-.075.54-.154.81-.234l.19-.056q.5-.151.99-.314l.271-.092q.358-.12.714-.246c.117-.041.233-.085.35-.127q.318-.117.634-.238l.358-.139c.215-.085.429-.172.642-.26l.32-.132q.4-.17.8-.349l.14-.061c.31-.14.619-.285.924-.431.09-.043.177-.088.266-.132q.324-.159.645-.323c.108-.056.216-.113.324-.17q.291-.153.577-.31l.324-.18c.149-.083.295-.169.441-.254l-2.13-.572h0q-.155.13-.308.255l-.112.089c-.08.065-.16.128-.24.191s-.178.139-.266.206l-.236.178-.261.192-.231.166c-.085.061-.17.12-.254.178l-.223.153-.247.166-.213.14-.237.152-.2.127-.226.139-.19.115-.212.125-.177.1-.2.112-.16.09-.183.1-.142.076-.164.086-.124.065-.144.073-.1.051-.127.062-.076.037-.1.049-.053.025-.08.036-.027.013-.053.024-4.275-1.388-.73-2.948-.011,0-.075.031c-.145.061-.293.12-.44.179l-.055.021q-.229.091-.46.177l-.011,0c-.154.057-.31.113-.465.167l-.066.023c-.149.052-.3.1-.451.15l-.081.026q-.226.071-.454.137l-.073.021c-.158.044-.315.087-.474.126l-.034.008q-.236.058-.474.109l-.047.01q-.237.049-.476.09l-.081.014c-.155.026-.312.049-.469.069l-.085.01q-.239.029-.48.049l-.063,0c-.168.013-.336.023-.5.027H16.8c-.169,0-.338,0-.508,0h-.069q-.244-.007-.488-.026l-.046,0-.064-.006-.2-.019-.138-.016c-.068-.008-.137-.018-.206-.028l-.131-.019c-.075-.012-.15-.025-.226-.039l-.112-.02c-.112-.023-.225-.046-.338-.072-1.389-.322-2.134-.924-2.328-1.707l-.286-1.156-.767-3.1c0-.014,0-.028-.008-.041a1.592,1.592,0,0,1-.029-.162s0-.006,0-.009a1.79,1.79,0,0,1-.013-.2c0-.02,0-.04,0-.06,0-.053,0-.1.006-.159,0-.018.006-.035.008-.053.006-.056.014-.113.024-.171,0-.011.006-.023.008-.033a3.026,3.026,0,0,1,.123-.434c0-.011.009-.023.014-.034.021-.057.045-.114.07-.172l-.164-.664,10.59-5.233,1.378.434.693,2.8.05-.022.451-.2.1-.043.357-.154.159-.066.307-.126.175-.07.3-.119.173-.066q.169-.063.34-.124l.138-.05c.159-.055.319-.109.479-.161l.084-.026q.2-.064.4-.122l.165-.046c.108-.031.216-.06.325-.088l.183-.046.148-.037c.089-.021.179-.041.268-.061l.019,0c.161-.034.322-.066.483-.095l.069-.013c.155-.026.311-.05.468-.071l.083-.011c.155-.02.312-.036.468-.05l.078-.006c.161-.013.321-.022.483-.028H30.4q.244-.008.487,0h.034c.162,0,.325.013.489.025l.047,0,.041,0,.182.018.135.014c.063.008.127.017.191.026l.126.018.211.036.106.019c.105.021.211.043.317.068a6.543,6.543,0,0,1,.7.2c.116.041.228.083.334.128a3.81,3.81,0,0,1,.578.306L34.4,9.7l.067.048c.034.024.069.047.1.071a2.5,2.5,0,0,1,.355.33,2.006,2.006,0,0,1,.3.446,1.8,1.8,0,0,1,.073.167c.006.018.014.034.02.051s.011.039.018.059c.011.034.022.068.031.1l1.053,4.258a2.643,2.643,0,0,1-.176,1.609l.7,2.819-1.505,1.3.056-.033.282-.166q.384-.23.759-.469l.078-.049q.412-.264.811-.537c.082-.055.161-.113.241-.169.184-.129.367-.258.547-.389.094-.068.187-.138.28-.208q.248-.185.491-.375l.273-.215c.168-.135.332-.271.5-.408.076-.064.153-.127.228-.191q.352-.3.688-.613c3.016-2.794,4.586-5.666,4.794-8.265-.029-.046-.055-.093-.085-.139-.043-.064-.093-.125-.139-.188-.074-.1-.146-.208-.227-.309-.066-.083-.141-.162-.211-.243s-.132-.159-.2-.235c-.105-.112-.219-.219-.332-.327-.045-.043-.085-.088-.132-.13-.07-.065-.148-.125-.222-.189a9.839,9.839,0,0,0-.852-.662L43,6.418a15.466,15.466,0,0,0-5.213-2.149c-.26-.06-.524-.115-.789-.167l-.183-.035c-.263-.049-.528-.095-.8-.135l-.1-.014q-.369-.055-.744-.1L34.994,3.8c-.273-.031-.547-.057-.824-.08L33.989,3.7c-.2-.015-.391-.028-.588-.039l-.223-.012c-.238-.012-.477-.021-.718-.028h-.051q-.386-.009-.775-.009h-.22q-.3,0-.6.009l-.21.005c-.262.008-.525.018-.789.032L29.7,3.67c-.167.009-.334.021-.5.033L36.686,0l.045.009a14.122,14.122,0,0,1,6.2,2.914c.046.04.1.079.141.12.09.082.171.168.255.252.069.069.143.135.209.206.134.143.26.29.381.439l.034.039c.1.122.186.247.274.372.03.042.064.083.092.125.076.113.142.229.211.344.034.058.073.113.105.172.041.074.075.151.113.226.052.1.107.2.154.307.011.024.019.049.029.074.068.157.132.315.187.475.024.07.041.142.063.213.036.117.075.232.1.351L45.866,9l.471,1.9a6.729,6.729,0,0,1,.157.894q.017.151.026.3.019.307.011.619t-.041.63a8.3,8.3,0,0,1-.164.965l0,.014c.055-.077.1-.154.157-.231-.056.362-.115.725-.179,1.09a61.321,61.321,0,0,1-1.468,6.332c-.167.164-.334.327-.51.49-.206.191-.418.38-.633.567l-.121.106q-.312.27-.635.534l-.047.039-.076.061q-.33.267-.67.529l-.019.014q-.347.265-.705.524l-.136.1q-.348.25-.705.493l-.133.09c-.24.162-.481.322-.727.479l-.034.022c-.253.162-.508.32-.766.476l-.15.092q-.378.228-.764.449l-.14.079q-.384.217-.773.429l-.049.026c-.27.145-.542.287-.816.428l-.163.083q-.4.205-.813.4l-.144.068q-.4.192-.809.377l-.066.031c-.283.128-.569.253-.855.376l-.172.073q-.423.18-.851.352l-.145.058q-.417.165-.836.324l-.082.031q-.44.165-.885.322l-.179.063q-.438.154-.88.3l-.146.047q-.425.139-.853.27l-.094.029q-.45.137-.9.265l-.186.053c-.3.084-.6.165-.9.243l-.142.036q-.432.111-.864.214l-.105.025q-.455.108-.911.206l-.189.041q-.453.1-.906.184l-.137.025c-.287.055-.576.107-.863.157l-.117.02q-.455.077-.909.144l-.188.028c-.3.044-.6.085-.9.123l-.129.016q-.428.053-.855.1l-.124.013q-.45.045-.9.08l-.185.014c-.3.023-.6.043-.893.058l-.118.006c-.281.014-.561.026-.84.034l-.129,0c-.293.008-.584.012-.875.013H14.8C14.511,32.845,14.22,32.842,13.931,32.835Z" transform="translate(0 0)"/></g><g transform="translate(3.308 1.98)"><path class="w" d="M4.816,9.464C12.547,2.3,26.842-1.643,36.745.655s11.661,9.967,3.93,17.131S18.654,28.892,8.752,26.594-2.913,16.625,4.816,9.464Z" transform="translate(0 0)"/></g><g transform="translate(3.308 1.98)"><path class="x" d="M4.815,9.465C-.963,14.819-1.436,20.458,2.833,23.923c-3.7-3.491-3.03-8.877,2.5-14,7.731-7.163,22.026-11.107,31.93-8.81a14.536,14.536,0,0,1,5.917,2.67A13.809,13.809,0,0,0,36.745.655C26.842-1.643,12.546,2.3,4.815,9.465Z" transform="translate(0 0)"/></g><path class="y" d="M10.023,30.46c-11.343-2.632-13.36-11.416-4.5-19.62S30.745-1.882,42.087.75s13.36,11.417,4.5,19.621C39.448,26.991,27.408,31.21,17.067,31.21A31.3,31.3,0,0,1,10.023,30.46Z" transform="translate(0 0)"/><path class="z" d="M9.885,30.092c-7.941-1.843-11.538-6.9-9.164-12.88a18.233,18.233,0,0,1,4.727-6.453C14.225,2.627,30.511-1.867,41.753.742c7.941,1.843,11.538,6.9,9.164,12.88a18.229,18.229,0,0,1-4.727,6.453C39.1,26.64,27.124,30.834,16.855,30.834A30.993,30.993,0,0,1,9.885,30.092Z" transform="translate(0.236 0.189)"/><path class="aa" d="M40.517,0q-1.2.664-2.349,1.389C45,3.133,48.048,7.618,45.947,12.911A16.334,16.334,0,0,1,41.714,18.7c-7.781,7.21-22.22,11.194-32.188,8.88a14.247,14.247,0,0,1-6.36-3.028A30.321,30.321,0,0,1,0,25.141,15.035,15.035,0,0,0,7.686,29.1c11.242,2.609,27.528-1.885,36.3-10.017a18.23,18.23,0,0,0,4.727-6.453C50.993,6.9,47.78,2.018,40.517,0Z" transform="translate(2.436 1.178)"/><path class="ab" d="M50.691,2.832q-.275.391-.581.782A23.651,23.651,0,0,1,47.567,6.35c-.206.191-.418.38-.632.567l-.121.106q-.312.27-.635.533l-.047.039-.077.061c-.22.178-.443.354-.67.528l-.018.014c-.231.177-.467.351-.706.523l-.136.1q-.348.25-.705.493l-.133.089q-.359.242-.726.479l-.034.022c-.252.161-.508.32-.766.476l-.15.092q-.378.228-.764.448l-.14.079q-.383.218-.773.428l-.049.027q-.4.218-.816.427l-.163.083q-.4.2-.813.4l-.144.069q-.4.192-.808.377l-.066.031q-.424.192-.855.376l-.173.074q-.423.179-.851.352l-.145.058q-.416.166-.836.324l-.082.031q-.44.165-.885.321l-.179.063q-.438.153-.879.3l-.146.047q-.426.139-.854.27l-.094.029q-.45.137-.9.265l-.186.052q-.448.125-.9.243l-.142.036q-.431.111-.864.214l-.105.025q-.455.108-.911.206l-.189.041q-.453.1-.906.184l-.136.025q-.431.082-.863.156l-.117.02q-.455.077-.909.144l-.188.028q-.453.066-.9.123l-.129.015q-.428.053-.855.1l-.125.013q-.45.045-.9.08l-.185.014c-.3.023-.6.043-.893.059l-.118.006q-.421.021-.84.034l-.129,0q-.44.012-.875.013h-.181c-.292,0-.582,0-.871-.009l-.107,0q-.41-.011-.816-.031l-.131-.006q-.424-.023-.843-.056l-.084-.005-.079-.008q-.475-.039-.943-.092l-.14-.016q-.474-.056-.94-.125l-.113-.018q-.478-.073-.946-.161l-.07-.014q-.488-.094-.966-.2c-5.6-1.3-8.932-4.1-9.8-7.6L.668,6.691A7.642,7.642,0,0,1,0,4.955L1.054,9.213c.864,3.493,4.192,6.3,9.8,7.6q.477.111.966.2l.07.014q.468.088.946.161l.113.018c.311.047.624.088.94.125l.14.016q.468.053.943.092l.079.008.084.005q.419.033.843.056l.131.006q.406.02.816.031l.107,0c.289.007.579.009.871.009h.181q.436,0,.875-.013l.129,0q.418-.013.84-.034l.118-.006c.3-.016.594-.036.893-.059l.185-.014q.448-.036.9-.08l.125-.013q.427-.044.855-.1l.129-.015q.451-.056.9-.123l.188-.028q.454-.068.909-.144l.117-.02q.431-.074.863-.156l.136-.025q.453-.088.906-.184l.189-.041q.456-.1.911-.206l.105-.025q.433-.1.864-.214l.142-.036q.45-.117.9-.243l.186-.052q.453-.129.9-.265l.094-.029q.428-.131.854-.27l.146-.047q.441-.145.879-.3l.179-.063q.444-.157.885-.321l.082-.031q.419-.159.836-.324l.145-.058q.428-.172.851-.352l.173-.074c.287-.123.572-.247.855-.376l.066-.031q.407-.185.808-.377l.144-.069q.409-.2.813-.4l.163-.083c.274-.14.547-.282.816-.427l.049-.027q.39-.211.773-.428l.14-.079q.386-.221.764-.448l.15-.092q.387-.235.766-.476l.034-.022q.368-.236.726-.479l.133-.089q.357-.243.705-.493l.136-.1q.357-.259.706-.523l.018-.014q.34-.261.67-.528l.077-.061.047-.039q.323-.264.635-.533l.121-.106c.214-.187.426-.376.632-.567a23.583,23.583,0,0,0,2.542-2.736q.307-.391.581-.782A14.607,14.607,0,0,0,52.234.11c.014-.037.027-.073.041-.11A14.92,14.92,0,0,1,50.691,2.832Z" transform="translate(0.226 17.911)"/><path class="ac" d="M4.752,25.177A16.086,16.086,0,0,1,0,23.326a35.492,35.492,0,0,0,7.148-2.586,53.358,53.358,0,0,0,8.672-5.423l8.967-7.1c-.149.026-.3.053-.447.083A90.811,90.811,0,0,1,35.257,0C42.89,2.936,43.7,9.862,36.676,16.37,30.44,22.148,19.931,25.833,10.9,25.832A27.346,27.346,0,0,1,4.752,25.177Z" transform="translate(7.307 3.396)"/><g transform="translate(10.617 6.178)"><g transform="translate(0 0)"><g transform="translate(0.493 1.008)"><path class="ad" d="M0,0,8.755,10.86l12.99-6.085Z" transform="translate(0 4.956)"/><path class="ad" d="M0,4.956,24.238,0,21.745,9.73" transform="translate(0 0)"/><path class="ad" d="M0,6.154l21.437-4.23L12.677,0Z" transform="translate(10.789 10.108)"/><path class="ad" d="M8.76,11.585,2.806,0,0,9.662Z" transform="translate(23.466 0.446)"/></g><path class="ae" d="M0,0,8.755,10.86l12.99-6.085Z" transform="translate(0 4.956)"/><path class="af" d="M0,4.956,24.238,0,21.745,9.731"/><path class="ag" d="M0,6.154l21.437-4.23L12.677,0Z" transform="translate(10.789 10.108)"/><path class="ah" d="M8.76,11.585,2.806,0,0,9.662Z" transform="translate(23.466 0.447)"/></g><path class="ai" d="M0,7.925,4.609,0,12.99,1.84Z" transform="translate(8.755 7.891)"/><path class="aj" d="M0,7.891,10.874,0,8.381,9.731Z" transform="translate(13.364 0)"/></g></g></g><g transform="translate(10.541 79.632)"><ellipse class="ak" cx="0.515" cy="0.515" rx="0.515" ry="0.515" transform="translate(35.506 10.256)"/><ellipse class="al" cx="0.862" cy="0.862" rx="0.862" ry="0.862" transform="translate(46.397 43.101)"/><ellipse class="ak" cx="0.457" cy="0.463" rx="0.457" ry="0.463" transform="translate(20.757 27.923)"/><ellipse class="ak" cx="0.444" cy="0.444" rx="0.444" ry="0.444" transform="translate(16.835 50.984)"/><ellipse class="am" cx="0.502" cy="0.502" rx="0.502" ry="0.502" transform="translate(21.898 0)"/><ellipse class="ak" cx="0.369" cy="0.369" rx="0.369" ry="0.369" transform="translate(54.225 6.846)"/><ellipse class="ak" cx="0.635" cy="0.635" rx="0.635" ry="0.635" transform="translate(0 26.034)"/><ellipse class="al" cx="0.635" cy="0.635" rx="0.635" ry="0.635" transform="translate(48.67 23.725)"/><ellipse class="al" cx="0.635" cy="0.635" rx="0.635" ry="0.635" transform="translate(4.141 1.887)"/></g><path class="an" d="M0,0V23.6" transform="translate(22.849 112.04)"/><path class="an" d="M0,0V15.717" transform="translate(45.371 115.736)"/><path class="l" d="M0,0V15.717" transform="translate(36.17 89.888)"/><path class="l" d="M0,0V15.717" transform="translate(55.31 79.526)"/><path class="l" d="M0,0V15.717" transform="translate(57.8 127.786)"/><path class="l" d="M0,0V23.6" transform="translate(75.459 96.215)"/><path class="ao" d="M.456,82.78,0,0,74.769,1.889V85.121s-3.163,19.116-37.385,19.592C.456,103.733.456,82.78.456,82.78Z" transform="translate(4.571 48.368)"/><path class="ap" d="M74.428,0S51.8,37.179,0,33.582L.061,44.665S56.876,49.158,74.4,16.772h0V2.46" transform="translate(4.912 82.362)"/><path class="aq" d="M74.264,7.28S56.582,43.492,0,46.148l.023,4.117S46.569,53.082,74.4,16.772h0L74.43,0" transform="translate(4.875 63.154)"/><ellipse class="ar" cx="40.509" cy="23.911" rx="40.509" ry="23.911" transform="translate(1.417 31.218)"/><ellipse class="as" cx="40.509" cy="23.911" rx="40.509" ry="23.911" transform="translate(1.417 27.563)"/><ellipse class="at" cx="36.401" cy="21.486" rx="36.401" ry="21.486" transform="translate(5.525 29.988)"/><g transform="translate(15.496)"><g transform="translate(7.412 33.09)"><path class="au" d="M0,11H0c.031-2.8,1.926-5.606,5.682-7.751C13.228-1.063,25.42-1.085,32.917,3.2c3.764,2.149,5.628,4.975,5.6,7.8h0l.034,4.243h0c.032,3.052-1.611,6.132-5.683,8.457-7.544,4.308-19.737,4.33-27.234.049C1.555,21.419.094,18.316.035,15.241h0Z" transform="translate(0 0)"/><path class="av" d="M5.6,18.8c7.5,4.281,19.689,4.259,27.234-.049S40.413,7.478,32.917,3.2,13.227-1.063,5.683,3.246-1.9,14.517,5.6,18.8Z" transform="translate(0 0)"/></g><ellipse class="aw" cx="26.686" cy="26.686" rx="26.686" ry="26.686" transform="translate(0)"/><g class="ax" transform="translate(0.041 12.846)"><g transform="translate(3.163 0)"><path class="ay" d="M43.657,4.12A22.383,22.383,0,0,0,47.2,1.612q-.3-.58-.626-1.143A23.154,23.154,0,0,1,43.7,2.422C32.779,8.726,15.021,8.694,4.034,2.35A23.1,23.1,0,0,1,.66,0Q.316.567,0,1.152a22.322,22.322,0,0,0,3.993,2.9C14.98,10.392,32.738,10.424,43.657,4.12Z" transform="translate(0)"/><path class="ay" d="M43.657,4.12A22.383,22.383,0,0,0,47.2,1.612q-.3-.58-.626-1.143A23.154,23.154,0,0,1,43.7,2.422C32.779,8.726,15.021,8.694,4.034,2.35A23.1,23.1,0,0,1,.66,0Q.316.567,0,1.152a22.322,22.322,0,0,0,3.993,2.9C14.98,10.392,32.738,10.424,43.657,4.12Z" transform="translate(0)"/></g><g transform="translate(0 10.796)"><path class="ay" d="M6.762,7.859c10.987,6.343,28.745,6.375,39.663.072A17.534,17.534,0,0,0,53.282,1.4c-.029-.471-.072-.939-.125-1.4a17.825,17.825,0,0,1-6.69,6.233C35.548,12.537,17.79,12.5,6.8,6.161A18.339,18.339,0,0,1,.13.04Q.043.814,0,1.6A18.213,18.213,0,0,0,6.762,7.859Z" transform="translate(0 0)"/><path class="ay" d="M6.762,7.859c10.987,6.343,28.745,6.375,39.663.072A17.534,17.534,0,0,0,53.282,1.4c-.029-.471-.072-.939-.125-1.4a17.825,17.825,0,0,1-6.69,6.233C35.548,12.537,17.79,12.5,6.8,6.161A18.339,18.339,0,0,1,.13.04Q.043.814,0,1.6A18.213,18.213,0,0,0,6.762,7.859Z" transform="translate(0 0)"/></g><g transform="translate(2.7 24.659)"><path class="ay" d="M43.485,3.781c-10.919,6.3-28.676,6.272-39.664-.072A22.484,22.484,0,0,1,0,.965,26.592,26.592,0,0,0,1.885,4.2q.887.625,1.9,1.21c10.987,6.343,28.745,6.375,39.664.072a22.877,22.877,0,0,0,3.134-2.163A26.566,26.566,0,0,0,48.34,0,20.6,20.6,0,0,1,43.485,3.781Z" transform="translate(0 0)"/><path class="ay" d="M43.485,3.781c-10.919,6.3-28.676,6.272-39.664-.072A22.484,22.484,0,0,1,0,.965,26.592,26.592,0,0,0,1.885,4.2q.887.625,1.9,1.21c10.987,6.343,28.745,6.375,39.664.072a22.877,22.877,0,0,0,3.134-2.163A26.566,26.566,0,0,0,48.34,0,20.6,20.6,0,0,1,43.485,3.781Z" transform="translate(0 0)"/></g></g></g></g><path class="az" d="M0,5.592V0S2.675,1.866,4.507,2V7.559S.469,5.967,0,5.592Z" transform="translate(14.226 175.708)"/></g><g transform="translate(424.943 10.174)"><g transform="translate(0 0)"><ellipse class="h" cx="47.545" cy="20.649" rx="47.545" ry="20.649" transform="translate(0.333 124.017)"/><path class="i" d="M.034,0,0,26.21a10.943,10.943,0,0,0,3.746,8.27C8.948,39.01,19.964,45.8,39.268,45.8c17.529,0,26.988-6.838,31.33-11.367a10.966,10.966,0,0,0,3.051-7.577L73.684,0H.034Z" transform="translate(0 118.412)"/><path class="j" d="M59.56.186c6.05,3.485,9.809,8.35,9.809,13.733,0,10.6-14.553,19.186-32.5,19.186s-32.5-8.59-32.5-19.186C4.362,8.439,8.254,3.5,14.5,0,5.686,4.029,0,10.382,0,17.532,0,29.709,16.487,39.58,36.825,39.58S73.65,29.709,73.65,17.532C73.65,10.493,68.139,4.223,59.56.186Z" transform="translate(0.035 100.88)"/><ellipse class="k" cx="32.503" cy="19.186" rx="32.503" ry="19.186" transform="translate(4.396 95.613)"/><path class="l" d="M0,0S3.384,11.3,23.943,15.563" transform="translate(2.676 120.671)"/><path class="l" d="M13.336,0S20.129,14.842,0,23.217" transform="translate(56.518 110.526)"/><g transform="translate(34.487 135.524) rotate(-8)"><path class="m" d="M1.585,0,0,4S8.393,6.163,15.607,4.3L14.414.3A41.8,41.8,0,0,1,1.585,0Z" transform="translate(0 0)"/><path class="n" d="M0,1.775S2.249-.1,3.742,1.187c2.639,2.278,4.219.334,5.279-.419C10.5-.283,11.18-.16,12.272.563" transform="translate(1.505 1.593)"/><path class="o" d="M.976,0,0,3.29" transform="translate(3.092 0.817)"/><path class="o" d="M.5,0,0,3.254" transform="translate(6.187 1.104)"/><path class="p" d="M0,0,.223,3.477" transform="translate(9.226 1.104)"/><path class="p" d="M0,0,.809,3.377" transform="translate(11.986 0.981)"/></g><ellipse class="q" cx="28.236" cy="16.667" rx="28.236" ry="16.667" transform="translate(8.584 98.133)"/><path class="r" d="M73.685,0S69.8,17.095,38.339,17.076C7.54,17.059,0,0,0,0" transform="translate(0 128.452)"/><path class="r" d="M73.685,0S69.8,17.095,38.339,17.076C7.54,17.059,0,0,0,0" transform="translate(0.017 133.569)"/><path class="s" d="M51.689,0A56.039,56.039,0,0,1,0,0" transform="translate(10.83 134.084)"/><g transform="translate(14.362 79.191)"><g transform="translate(0 0)"><path class="t" d="M14.9,29.283l-.093,0q-.359-.01-.714-.027l-.115-.005q-.371-.02-.738-.048l-.073,0-.069-.007q-.415-.035-.825-.081l-.123-.014c-.276-.032-.551-.068-.822-.109l-.1-.015q-.418-.064-.828-.141l-.061-.012c-.285-.055-.567-.114-.845-.178-4.9-1.138-7.814-3.59-8.569-6.644L0,18.267c.562,2.272,2.317,4.208,5.2,5.521a6.111,6.111,0,0,1-1.405-2.6h0L3.374,19.5l-.506-2.045h0c-.024-.1-.045-.2-.065-.3-.005-.031-.01-.061-.016-.092-.013-.072-.024-.144-.034-.216,0-.035-.009-.069-.014-.1-.009-.076-.016-.151-.022-.227,0-.028,0-.056-.007-.083-.007-.1-.012-.207-.013-.312,0-.018,0-.036,0-.055,0-.087,0-.173,0-.261,0-.035,0-.07,0-.1,0-.073.008-.148.014-.222,0-.035.006-.071.01-.106.008-.084.018-.169.03-.254,0-.023.006-.047.009-.07q.024-.161.055-.324c0-.007,0-.013,0-.019s.009-.037.012-.056c.019-.092.039-.185.063-.278.008-.035.016-.07.026-.1.025-.1.052-.195.081-.292.006-.021.012-.041.018-.061q.054-.175.117-.352l.034-.093q.05-.136.105-.273l.042-.1c.048-.115.1-.23.152-.346l.005-.013c.056-.12.116-.24.178-.36l.054-.1c.048-.092.1-.184.151-.276.019-.033.036-.065.055-.1.07-.121.141-.241.217-.362l.032-.05q.1-.157.207-.315l.074-.108c.066-.1.134-.191.2-.287l.061-.084c.09-.121.182-.242.278-.363l.066-.082q.115-.145.237-.29l.092-.109c.089-.1.179-.207.272-.312l.051-.057c.11-.122.223-.242.339-.364l.094-.1q.136-.14.277-.28l.106-.1c.125-.122.251-.243.382-.364q.257-.238.524-.47c.06-.052.122-.1.183-.154.12-.1.24-.2.363-.3L8,7.326c.065-.053.132-.1.2-.156.114-.09.227-.181.342-.27.082-.063.166-.124.248-.186.112-.084.224-.167.337-.25.087-.063.176-.125.265-.188.112-.08.225-.159.339-.237.092-.063.185-.124.279-.186.114-.076.228-.152.343-.227l.289-.183c.116-.072.232-.145.349-.217l.3-.179c.118-.07.236-.14.355-.208l.307-.174c.12-.067.241-.134.363-.2l.313-.168c.123-.066.246-.13.37-.194l.319-.162.377-.186L14.02,3.6c.127-.06.256-.12.385-.179.109-.05.217-.1.327-.149.13-.058.261-.115.392-.172l.33-.142c.133-.056.267-.111.4-.166l.33-.134c.137-.054.275-.107.412-.16l.328-.125.082-.031q.291-.108.585-.211l.1-.035c.2-.07.4-.137.6-.2l.2-.067q.277-.09.556-.176L19.2,1.6q.348-.106.7-.205l.164-.046q.277-.077.556-.15l.207-.054q.316-.081.633-.157L21.54.967q.354-.083.708-.16l.2-.042c.182-.038.364-.076.545-.111l.2-.038c.236-.046.473-.089.709-.129L23.97.476q.318-.053.636-.1l.209-.03c.185-.027.371-.052.557-.076l.166-.021q.351-.044.7-.08l.154-.015c.187-.018.374-.035.56-.05l.207-.016c.2-.015.4-.029.6-.039l.1-.006c.231-.012.46-.021.689-.028l.184,0C28.915,0,29.09,0,29.266,0h.193c.227,0,.454,0,.678.007l.045,0c.211.005.42.014.627.024L31,.043c.173.009.344.021.514.034l.115.008.044,0q.363.03.72.07l.156.019q.327.039.65.087l.088.012q.351.054.7.119l.161.031c.232.046.463.093.69.145a12.351,12.351,0,0,1,5.423,2.549c.041.035.084.069.123.105.079.072.149.147.224.22s.125.118.182.18c.117.125.228.253.333.384a.365.365,0,0,0,.031.034c.086.106.162.216.24.326.026.037.056.072.081.11.066.1.125.2.185.3.03.05.064.1.092.15s.065.133.1.2c.045.089.093.177.134.268.01.021.016.043.026.065.059.137.116.275.164.416.021.061.036.125.055.186.032.1.065.2.091.307l.51,2.062.411,1.663a5.833,5.833,0,0,1,.138.782c.009.089.017.178.022.267.011.179.015.36.01.542s-.016.366-.037.551a7.161,7.161,0,0,1-.143.844l0,.012c1.89-2.648,2.521-5.255,1.959-7.527l.921,3.725a6.829,6.829,0,0,1,.184,1.2c.007.1.011.205.013.308a7.556,7.556,0,0,1-.084,1.263q-.048.321-.123.647a9.54,9.54,0,0,1-.409,1.318,12.79,12.79,0,0,1-1.478,2.7q-.24.341-.509.684a20.626,20.626,0,0,1-2.223,2.393c-.18.167-.366.332-.553.5l-.106.093q-.273.235-.556.466l-.041.035-.067.053q-.289.234-.586.462l-.016.013c-.2.155-.409.307-.617.458l-.119.086q-.3.218-.617.431l-.116.078c-.21.141-.421.281-.636.419l-.03.019q-.332.211-.67.417l-.132.08q-.331.2-.668.392l-.123.07q-.336.19-.676.375l-.043.024q-.354.19-.714.373l-.143.073q-.353.179-.711.352l-.126.06q-.351.167-.707.33l-.058.026c-.247.112-.5.222-.748.329l-.151.064q-.37.157-.745.308l-.127.05q-.364.144-.731.283l-.071.027q-.385.144-.774.281l-.157.055q-.383.134-.769.262l-.128.041q-.372.121-.746.236l-.082.025q-.394.12-.79.232l-.162.046c-.262.073-.523.145-.785.213l-.124.032q-.378.1-.756.188l-.092.022c-.265.063-.531.122-.8.18l-.165.035c-.264.056-.528.11-.793.161l-.12.022c-.251.048-.5.094-.755.137l-.1.018q-.4.067-.8.126l-.165.024c-.264.038-.528.075-.791.107l-.113.014q-.374.046-.748.084l-.109.012q-.393.039-.785.07l-.162.013c-.262.02-.522.037-.782.051l-.1,0q-.369.019-.735.03l-.113,0q-.384.01-.766.012h-.158C15.4,29.291,15.149,29.289,14.9,29.283Z" transform="translate(0.198 1.732)"/><g transform="translate(2.906 2.297)"><path class="u" d="M0,0H0Z" transform="translate(1.082 20.621)"/><path class="v" d="M12.187,28.723l-.093,0q-.359-.009-.714-.027l-.115-.005q-.371-.02-.738-.049l-.073,0-.069-.007q-.415-.035-.825-.08l-.123-.014c-.276-.033-.551-.069-.822-.11l-.1-.015q-.418-.064-.828-.141l-.061-.012c-.285-.054-.567-.114-.845-.178q-.879-.2-1.671-.464C4.1,26.134,3.105,24.64,2.226,23.1c.087.042.171.084.26.125a6.587,6.587,0,0,1-.8-1.1c-.084-.158-.168-.316-.248-.475a5.724,5.724,0,0,1-.361-1.027h0l-.416-1.68L.159,16.9h0c-.024-.1-.045-.2-.065-.3-.005-.031-.01-.061-.016-.092-.013-.071-.024-.143-.034-.215,0-.035-.009-.069-.014-.1-.009-.075-.016-.151-.022-.227,0-.027,0-.055-.007-.083,0-.006,0-.012,0-.018L2.358,14.7l-.009.013c-.076.121-.148.241-.217.362-.019.033-.037.065-.055.1-.052.092-.1.184-.151.276l-.054.1c-.062.121-.122.24-.178.36l-.005.013q-.081.173-.152.345l-.042.1q-.056.137-.105.273l-.034.092c-.042.118-.081.235-.117.352-.006.021-.012.041-.018.062q-.044.146-.081.292l-.026.1c-.022.093-.043.186-.062.279,0,.025-.011.05-.016.075-.021.109-.039.217-.055.324,0,.024-.006.047-.009.07-.012.085-.021.17-.03.254,0,.036-.006.071-.009.107,0,.005,0,.012,0,.018a6.639,6.639,0,0,0,.757.976,8.236,8.236,0,0,0,1.346,1.145,10.511,10.511,0,0,0,1.108.662,14.925,14.925,0,0,0,3.5,1.249q.395.091.8.168c.09.017.182.03.273.047.182.032.363.064.547.091.11.016.223.03.334.044.167.022.335.046.5.064.12.014.242.024.363.036.166.016.332.032.5.046l.285.019c.183.012.365.022.55.03l.311.012q.275.009.553.012c.1,0,.2,0,.3,0,.213,0,.427,0,.642-.007.071,0,.142,0,.213,0,.285-.008.573-.019.861-.035.067,0,.136-.009.2-.013.222-.013.444-.029.668-.047l.307-.027c.193-.018.385-.036.578-.057l.327-.037c.194-.023.388-.048.581-.074l.309-.042q.342-.049.687-.106l.2-.031q.441-.075.884-.161l.228-.047c.219-.044.439-.089.659-.137l.314-.071q.29-.066.579-.136l.329-.081q.293-.074.585-.154l.3-.081c.237-.066.473-.135.709-.205l.166-.049q.435-.132.866-.275l.237-.081q.313-.105.625-.216c.1-.036.2-.074.306-.111q.278-.1.555-.208l.313-.122c.188-.074.375-.15.562-.227l.28-.116q.354-.149.7-.305l.122-.054c.271-.122.541-.249.808-.377.078-.038.155-.077.233-.115q.284-.139.564-.282l.284-.149q.254-.134.505-.271l.283-.157c.131-.073.258-.148.386-.222l-1.863-.5h0q-.136.114-.27.223l-.1.078-.21.167-.233.18-.207.156-.228.168-.2.145-.222.156-.2.134-.216.145-.186.122-.207.133L26.4,19.8l-.2.121-.166.1-.185.109-.155.09-.172.1-.14.078-.16.087-.124.067-.143.075-.109.056-.126.064-.089.045-.111.054-.067.032-.091.043-.047.022-.07.032-.024.011-.047.021-3.739-1.214L19.8,17.213l-.01,0-.066.027c-.127.053-.256.105-.385.156l-.048.019q-.2.08-.4.155l-.01,0c-.135.05-.271.1-.407.146l-.058.02c-.131.046-.262.089-.394.131l-.071.022q-.2.063-.4.12l-.064.018c-.138.038-.276.076-.415.11l-.03.007q-.206.051-.415.1l-.041.009q-.207.043-.416.079l-.071.012c-.136.022-.273.043-.41.06l-.074.009q-.209.026-.42.043l-.055,0c-.147.011-.294.02-.441.024h-.005c-.148,0-.3,0-.444,0h-.06q-.213-.006-.427-.022l-.041,0-.056-.005-.173-.017-.121-.014c-.06-.007-.12-.015-.18-.024l-.115-.016-.2-.034-.1-.018c-.1-.02-.2-.04-.3-.063-1.215-.282-1.867-.808-2.036-1.493l-.25-1.011L9.53,13.053c0-.012,0-.024-.007-.036a1.393,1.393,0,0,1-.026-.141s0-.005,0-.008a1.566,1.566,0,0,1-.012-.177c0-.018,0-.035,0-.052,0-.046,0-.092.005-.139,0-.015,0-.031.007-.047.005-.049.012-.1.021-.149,0-.009,0-.02.007-.029a2.647,2.647,0,0,1,.107-.38c0-.01.008-.02.012-.03.019-.05.039-.1.061-.151l-.144-.581,9.264-4.578,1.205.38.607,2.451.043-.019.395-.176.088-.038.313-.134.139-.058.269-.11.153-.061.267-.1.151-.058.3-.108.121-.044c.139-.048.279-.1.419-.141l.073-.022q.174-.056.349-.107l.144-.041c.095-.027.189-.053.285-.077l.16-.041.129-.032.235-.053.017,0c.141-.03.281-.058.423-.083l.06-.011c.136-.023.272-.044.409-.062l.072-.009c.136-.018.273-.032.409-.044l.068,0c.14-.011.281-.019.422-.024h.038q.213-.007.426,0h.03c.142,0,.285.012.428.022l.041,0,.036,0,.159.015.118.013.167.022.11.015.185.032.093.016c.092.018.184.037.277.059a5.723,5.723,0,0,1,.61.176c.1.036.2.072.292.112a3.333,3.333,0,0,1,.506.268l.02.012.059.042c.03.021.06.041.088.063a2.185,2.185,0,0,1,.31.288,1.754,1.754,0,0,1,.264.39,1.576,1.576,0,0,1,.064.146c.005.015.013.03.018.045s.01.035.015.052.019.06.027.09l.921,3.725a2.312,2.312,0,0,1-.154,1.408l.61,2.466-1.317,1.135.049-.029.247-.145q.336-.2.664-.41l.069-.043q.36-.231.71-.47c.071-.048.14-.1.211-.148.161-.112.321-.225.479-.341.082-.06.163-.121.245-.182q.217-.162.429-.328l.239-.188c.147-.118.291-.237.433-.357.066-.056.134-.111.2-.167q.308-.265.6-.536c2.638-2.444,4.012-4.956,4.193-7.23-.026-.041-.048-.082-.075-.122-.038-.056-.082-.109-.121-.165-.065-.09-.128-.182-.2-.27-.058-.072-.123-.142-.185-.213s-.116-.139-.178-.206c-.092-.1-.191-.191-.291-.286-.039-.037-.075-.077-.115-.114-.061-.056-.129-.11-.194-.165a8.606,8.606,0,0,0-.745-.579l-.047-.03a13.529,13.529,0,0,0-4.56-1.88c-.227-.053-.458-.1-.69-.146l-.16-.031c-.23-.043-.462-.083-.7-.118l-.088-.013q-.322-.049-.65-.087l-.156-.019c-.239-.027-.478-.05-.721-.07l-.159-.012c-.171-.013-.342-.025-.514-.034l-.195-.01c-.208-.01-.417-.019-.628-.024H28.35q-.337-.008-.678-.008h-.193q-.262,0-.528.008l-.184,0c-.229.007-.459.016-.69.028l-.1.007c-.146.008-.292.018-.438.029L32.091,0l.039.008a12.353,12.353,0,0,1,5.423,2.549c.041.035.084.069.123.105.078.072.149.147.223.22s.125.118.183.18c.117.125.228.253.333.384l.03.035c.085.107.162.216.24.326.026.037.056.072.081.11.066.1.124.2.185.3.03.05.064.1.092.15s.065.132.1.2c.046.089.094.177.134.268.009.021.016.043.026.065.059.137.115.275.163.416.021.061.036.124.055.186.031.1.065.2.09.307l.51,2.061.412,1.664a5.888,5.888,0,0,1,.138.782q.015.132.023.267.016.268.01.541t-.036.551a7.256,7.256,0,0,1-.143.844l0,.013c.048-.067.092-.134.138-.2-.049.317-.1.634-.157.954a53.641,53.641,0,0,1-1.284,5.539c-.146.143-.292.286-.446.428-.18.167-.366.332-.553.5l-.106.093q-.273.236-.556.467l-.041.034-.067.053q-.289.234-.586.462l-.016.012q-.3.232-.617.458l-.119.086q-.3.219-.617.431l-.116.078c-.21.141-.421.281-.636.419l-.03.019c-.221.141-.445.28-.67.417l-.132.08q-.331.2-.668.393l-.123.069q-.336.19-.676.375l-.043.023c-.236.127-.474.251-.714.374l-.143.072q-.353.179-.711.352l-.126.06q-.351.168-.708.33l-.058.027c-.247.112-.5.221-.748.329l-.151.064q-.37.157-.745.308l-.127.05q-.364.144-.731.284l-.071.027q-.385.144-.774.281l-.157.055q-.383.135-.769.262l-.128.041q-.372.122-.746.236l-.082.026q-.394.12-.79.231l-.162.046c-.262.073-.523.144-.785.213l-.124.032q-.378.1-.756.188L22,27.625q-.4.095-.8.18l-.165.036q-.4.084-.793.161l-.12.022c-.251.048-.5.094-.755.137l-.1.018q-.4.067-.8.126l-.165.024c-.264.038-.528.075-.791.107l-.113.014q-.374.046-.748.084l-.109.012q-.393.039-.785.07l-.162.013c-.262.02-.522.037-.782.051l-.1.005c-.246.012-.491.022-.735.03l-.113,0c-.256.007-.511.01-.766.012h-.158C12.693,28.731,12.439,28.729,12.187,28.723Z" transform="translate(0 0)"/></g><g transform="translate(2.894 1.732)"><path class="w" d="M4.213,8.279C10.975,2.013,23.48-1.437,32.143.573s10.2,8.719,3.438,14.985-19.263,9.715-27.925,7.7S-2.548,14.543,4.213,8.279Z" transform="translate(0 0)"/></g><g transform="translate(2.894 1.732)"><path class="x" d="M4.212,8.279C-.843,12.963-1.256,17.9,2.478,20.927-.757,17.873-.173,13.161,4.667,8.677,11.43,2.411,23.934-1.04,32.6.97a12.716,12.716,0,0,1,5.176,2.336A12.08,12.08,0,0,0,32.143.573C23.48-1.437,10.975,2.013,4.212,8.279Z" transform="translate(0 0)"/></g><path class="y" d="M8.768,26.645c-9.922-2.3-11.686-9.986-3.94-17.163S26.894-1.646,36.816.656,48.5,10.643,40.757,17.82C34.507,23.61,23.976,27.3,14.929,27.3A27.375,27.375,0,0,1,8.768,26.645Z" transform="translate(0 0)"/><path class="z" d="M8.647,26.323C1.7,24.711-1.446,20.289.631,15.057A15.95,15.95,0,0,1,4.766,9.411C12.443,2.3,26.69-1.633,36.524.649,43.47,2.261,46.617,6.683,44.54,11.916a15.946,15.946,0,0,1-4.135,5.645c-6.2,5.743-16.678,9.412-25.661,9.412A27.111,27.111,0,0,1,8.647,26.323Z" transform="translate(0.207 0.165)"/><path class="aa" d="M35.443,0q-1.049.581-2.055,1.215c5.974,1.526,8.643,5.449,6.8,10.079a14.288,14.288,0,0,1-3.7,5.06c-6.806,6.307-19.437,9.792-28.157,7.768a12.463,12.463,0,0,1-5.563-2.649A26.523,26.523,0,0,1,0,21.992a13.152,13.152,0,0,0,6.723,3.465C16.557,27.74,30.8,23.809,38.481,16.7a15.946,15.946,0,0,0,4.135-5.645C44.607,6.035,41.8,1.765,35.443,0Z" transform="translate(2.13 1.03)"/><path class="ab" d="M44.342,2.477q-.24.342-.509.684A20.688,20.688,0,0,1,41.61,5.555c-.181.167-.366.332-.553.5l-.106.093q-.273.236-.555.467l-.041.034-.067.053c-.193.156-.387.31-.586.462l-.016.012c-.2.155-.409.307-.617.458l-.119.086q-.3.218-.617.431l-.116.078q-.314.212-.635.419l-.03.019c-.22.141-.445.28-.67.417l-.132.08q-.331.2-.668.392l-.123.069q-.335.19-.676.375l-.043.023q-.354.19-.714.374l-.143.073q-.353.179-.711.351l-.126.06q-.352.168-.707.329l-.058.027q-.371.168-.748.329l-.151.064q-.37.157-.745.307l-.127.05q-.364.145-.731.283l-.071.027q-.385.144-.774.281l-.157.055q-.383.134-.769.261l-.128.041q-.372.121-.747.236l-.082.026q-.394.12-.79.232l-.162.046q-.392.11-.785.213l-.124.032q-.377.1-.756.188l-.092.022q-.4.094-.8.18l-.165.035q-.4.084-.793.161l-.119.022q-.377.072-.755.137l-.1.018q-.4.067-.8.126l-.165.024q-.4.058-.791.107l-.113.014q-.375.046-.748.084l-.109.012q-.394.039-.785.07l-.162.012c-.261.02-.522.037-.781.051l-.1.005q-.369.019-.735.03l-.113,0q-.384.01-.766.012h-.158c-.255,0-.509,0-.762-.008l-.093,0q-.359-.009-.714-.027l-.114-.005q-.371-.02-.738-.049l-.073,0-.069-.007q-.416-.035-.825-.08l-.122-.014q-.415-.049-.822-.109l-.1-.015q-.418-.064-.828-.141l-.061-.012q-.427-.082-.845-.178c-4.9-1.138-7.814-3.589-8.57-6.645L.584,5.853A6.685,6.685,0,0,1,0,4.334L.922,8.059c.756,3.055,3.667,5.507,8.57,6.645q.417.1.845.178l.061.012q.41.077.828.141l.1.015c.272.041.546.077.822.109l.122.014q.409.046.825.08l.069.007.073,0q.367.029.738.049l.114.005q.355.018.714.027l.093,0c.253.006.507.008.762.008h.158q.381,0,.766-.012l.113,0q.366-.011.735-.03l.1-.005c.26-.014.52-.031.781-.051l.162-.012q.392-.031.785-.07l.109-.012q.373-.038.748-.084l.113-.014q.395-.049.791-.107l.165-.024q.4-.059.8-.126l.1-.018q.377-.065.755-.137l.119-.022q.4-.077.793-.161l.165-.035q.4-.087.8-.18l.092-.022q.378-.09.756-.188l.124-.032q.394-.1.785-.213l.162-.046q.4-.112.79-.232l.082-.026q.375-.115.747-.236l.128-.041q.386-.127.769-.261l.157-.055q.388-.138.774-.281l.071-.027q.367-.139.731-.283l.127-.05q.375-.15.745-.307l.151-.064c.251-.107.5-.216.748-.329l.058-.027q.356-.162.707-.329l.126-.06q.358-.172.711-.351l.143-.073c.24-.123.478-.247.714-.374l.043-.023q.341-.184.676-.375l.123-.069q.337-.193.668-.392l.132-.08q.338-.206.67-.417l.03-.019q.322-.206.635-.419L38.2,8.47q.313-.213.617-.431l.119-.086q.313-.227.617-.458l.016-.012q.3-.228.586-.462l.067-.053.041-.034q.282-.231.555-.467l.106-.093c.188-.164.373-.329.553-.5a20.629,20.629,0,0,0,2.224-2.393q.268-.342.509-.684A12.777,12.777,0,0,0,45.692.1c.012-.032.023-.064.035-.1A13.051,13.051,0,0,1,44.342,2.477Z" transform="translate(0.197 15.667)"/><path class="ac" d="M4.157,22.024A14.072,14.072,0,0,1,0,20.405a31.047,31.047,0,0,0,6.253-2.262A46.675,46.675,0,0,0,13.838,13.4l7.844-6.208c-.131.022-.261.046-.391.073A79.438,79.438,0,0,1,30.841,0c6.677,2.568,7.385,8.627,1.241,14.319C26.627,19.374,17.435,22.6,9.536,22.6A23.921,23.921,0,0,1,4.157,22.024Z" transform="translate(6.392 2.971)"/><g transform="translate(9.288 5.405)"><g transform="translate(0 0)"><g transform="translate(0.431 0.882)"><path class="ad" d="M0,0,7.659,9.5,19.022,4.176Z" transform="translate(0 4.335)"/><path class="ad" d="M0,4.335,21.2,0,19.022,8.512" transform="translate(0 0)"/><path class="ad" d="M0,5.383l18.752-3.7L11.089,0Z" transform="translate(9.438 8.842)"/><path class="ad" d="M7.663,10.134,2.454,0,0,8.452Z" transform="translate(20.527 0.39)"/></g><path class="ae" d="M0,0,7.659,9.5,19.022,4.176Z" transform="translate(0 4.336)"/><path class="af" d="M0,4.336,21.2,0,19.022,8.512"/><path class="ag" d="M0,5.383l18.752-3.7L11.089,0Z" transform="translate(9.438 8.842)"/><path class="ah" d="M7.663,10.134,2.454,0,0,8.452Z" transform="translate(20.527 0.391)"/></g><path class="ai" d="M0,6.933,4.031,0l7.331,1.61Z" transform="translate(7.659 6.902)"/><path class="aj" d="M0,6.9,9.512,0,7.331,8.512Z" transform="translate(11.69 0)"/></g></g></g><g transform="translate(9.221 69.658)"><ellipse class="ak" cx="0.45" cy="0.45" rx="0.45" ry="0.45" transform="translate(31.059 8.972)"/><ellipse class="al" cx="0.754" cy="0.754" rx="0.754" ry="0.754" transform="translate(40.586 37.703)"/><ellipse class="ak" cx="0.4" cy="0.405" rx="0.4" ry="0.405" transform="translate(18.158 24.426)"/><ellipse class="ak" cx="0.388" cy="0.388" rx="0.388" ry="0.388" transform="translate(14.727 44.598)"/><ellipse class="am" cx="0.439" cy="0.439" rx="0.439" ry="0.439" transform="translate(19.155 0)"/><ellipse class="ak" cx="0.323" cy="0.323" rx="0.323" ry="0.323" transform="translate(47.434 5.989)"/><ellipse class="ak" cx="0.556" cy="0.556" rx="0.556" ry="0.556" transform="translate(0 22.773)"/><ellipse class="al" cx="0.556" cy="0.556" rx="0.556" ry="0.556" transform="translate(42.575 20.754)"/><ellipse class="al" cx="0.556" cy="0.556" rx="0.556" ry="0.556" transform="translate(3.623 1.65)"/></g><path class="an" d="M0,0V20.648" transform="translate(19.987 98.008)"/><path class="an" d="M0,0V13.749" transform="translate(39.689 101.241)"/><path class="l" d="M0,0V13.748" transform="translate(31.64 78.63)"/><path class="l" d="M0,0V13.748" transform="translate(48.383 69.566)"/><path class="l" d="M0,0V13.748" transform="translate(50.561 111.782)"/><path class="l" d="M0,0V20.648" transform="translate(66.009 84.164)"/><path class="ao" d="M.4,72.412,0,0,65.4,1.653V74.46S62.638,91.182,32.7,91.6C.4,90.741.4,72.412.4,72.412Z" transform="translate(3.998 42.311)"/><path class="ap" d="M65.106,0S45.316,32.523,0,29.376l.053,9.7s49.7,3.929,65.027-24.4h0V2.152" transform="translate(4.297 72.047)"/><path class="aq" d="M64.963,6.368S49.5,38.045,0,40.369l.02,3.6s40.717,2.463,65.063-29.3h0L65.108,0" transform="translate(4.265 55.244)"/><ellipse class="ar" cx="35.435" cy="20.916" rx="35.435" ry="20.916" transform="translate(1.24 27.308)"/><ellipse class="as" cx="35.435" cy="20.916" rx="35.435" ry="20.916" transform="translate(1.24 24.111)"/><ellipse class="at" cx="31.842" cy="18.795" rx="31.842" ry="18.795" transform="translate(4.833 26.232)"/><g transform="translate(13.555)"><g transform="translate(6.483 28.945)"><path class="au" d="M0,9.62H0c.027-2.452,1.684-4.9,4.97-6.781C11.571-.929,22.237-.949,28.795,2.8c3.292,1.88,4.923,4.352,4.9,6.824h0l.03,3.712h0c.028,2.669-1.41,5.364-4.971,7.4-6.6,3.769-17.265,3.788-23.823.043C1.361,18.736.082,16.022.03,13.332h0Z" transform="translate(0 0)"/><path class="av" d="M4.9,16.444c6.558,3.745,17.223,3.726,23.823-.043s6.633-9.86.075-13.6S11.571-.929,4.971,2.839-1.661,12.7,4.9,16.444Z" transform="translate(0)"/></g><ellipse class="ba" cx="23.344" cy="23.344" rx="23.344" ry="23.344" transform="translate(0)"/><g class="ax" transform="translate(0.036 11.237)"><g transform="translate(2.767 0)"><path class="ay" d="M38.189,3.6a19.58,19.58,0,0,0,3.1-2.194Q41.026.9,40.74.41a20.255,20.255,0,0,1-2.515,1.708C28.674,7.633,13.14,7.6,3.529,2.056A20.208,20.208,0,0,1,.578,0Q.277.5,0,1.008A19.526,19.526,0,0,0,3.493,3.541C13.1,9.09,28.638,9.118,38.189,3.6Z" transform="translate(0)"/><path class="ay" d="M38.189,3.6a19.58,19.58,0,0,0,3.1-2.194Q41.026.9,40.74.41a20.255,20.255,0,0,1-2.515,1.708C28.674,7.633,13.14,7.6,3.529,2.056A20.208,20.208,0,0,1,.578,0Q.277.5,0,1.008A19.526,19.526,0,0,0,3.493,3.541C13.1,9.09,28.638,9.118,38.189,3.6Z" transform="translate(0)"/></g><g transform="translate(0 9.444)"><path class="ay" d="M5.915,6.875c9.611,5.549,25.145,5.577,34.7.063a15.338,15.338,0,0,0,6-5.71C46.583.815,46.545.406,46.5,0a15.593,15.593,0,0,1-5.852,5.452c-9.551,5.514-25.085,5.486-34.7-.063A16.042,16.042,0,0,1,.113.035Q.038.712,0,1.4A15.932,15.932,0,0,0,5.915,6.875Z" transform="translate(0 0)"/><path class="ay" d="M5.915,6.875c9.611,5.549,25.145,5.577,34.7.063a15.338,15.338,0,0,0,6-5.71C46.583.815,46.545.406,46.5,0a15.593,15.593,0,0,1-5.852,5.452c-9.551,5.514-25.085,5.486-34.7-.063A16.042,16.042,0,0,1,.113.035Q.038.712,0,1.4A15.932,15.932,0,0,0,5.915,6.875Z" transform="translate(0 0)"/></g><g transform="translate(2.362 21.571)"><path class="ay" d="M38.039,3.308c-9.551,5.514-25.085,5.486-34.7-.063A19.668,19.668,0,0,1,0,.844,23.262,23.262,0,0,0,1.649,3.672q.776.547,1.658,1.059c9.611,5.549,25.145,5.577,34.7.063A20.012,20.012,0,0,0,40.745,2.9,23.239,23.239,0,0,0,42.286,0,18.022,18.022,0,0,1,38.039,3.308Z" transform="translate(0 0)"/><path class="ay" d="M38.039,3.308c-9.551,5.514-25.085,5.486-34.7-.063A19.668,19.668,0,0,1,0,.844,23.262,23.262,0,0,0,1.649,3.672q.776.547,1.658,1.059c9.611,5.549,25.145,5.577,34.7.063A20.012,20.012,0,0,0,40.745,2.9,23.239,23.239,0,0,0,42.286,0,18.022,18.022,0,0,1,38.039,3.308Z" transform="translate(0 0)"/></g></g></g></g><path class="az" d="M0,4.892V0A10.8,10.8,0,0,0,3.942,1.753V6.612S.41,5.219,0,4.892Z" transform="translate(12.445 153.702)"/></g><g transform="translate(394.562 247.854)"><g transform="translate(0 0)"><path class="bb" d="M0,70.018H33.082a29.2,29.2,0,0,0,14.731-3.989l59.476-33.877L53.83,0Z" transform="translate(43.132 43.593)"/><path class="bc" d="M13.262,81.012l61-35.2A6.484,6.484,0,0,0,77.5,40.2V7.876L70.043,0,0,39.251,6.141,80.92h0A6.969,6.969,0,0,0,13.262,81.012Z" transform="translate(33.458 31.666)"/><path class="bd" d="M0,0V44.293A8.1,8.1,0,0,0,3.872,51.2L39.6,73.045a2.322,2.322,0,0,0,3.534-1.981V38.73Z" transform="translate(0 39.542)"/><path class="be" d="M46.088,78.448l63.673-36.94a2.406,2.406,0,0,0,.4-3.872L69.372,1.068a4.181,4.181,0,0,0-4.888-.5L1.714,36.98a3.441,3.441,0,0,0-.57,5.538L40.588,77.882A4.7,4.7,0,0,0,46.088,78.448Z" transform="translate(0 0)"/><path class="bf" d="M41.659,71.431l58.451-33.91a2.208,2.208,0,0,0,.366-3.555L63.682.98A3.837,3.837,0,0,0,59.195.518L1.574,33.947a3.159,3.159,0,0,0-.523,5.084L36.611,70.912A4.317,4.317,0,0,0,41.659,71.431Z" transform="translate(5.199 3.225)"/><g transform="translate(39.505 7.307)"><g transform="translate(0 0)"><path class="bg" d="M10.808,9.562h2.562l3.346-1.156L1.685,0,0,.381Z" transform="translate(2.535 12.164)"/><g transform="translate(0 11.734)"><path class="bh" d="M15.824,7.68,13.343,9.122,0,1.442,2.482,0Z" transform="translate(0.002 0)"/><path class="bi" d="M2.484,0l0,.87L0,2.312l0-.87Z" transform="translate(13.343 7.68)"/><path class="bj" d="M13.345,7.68l0,.87L0,.87,0,0Z" transform="translate(0 1.442)"/></g><path class="bk" d="M6.52,7.4H8.448l3.375-1.166L1.352,0,0,.335Z" transform="translate(8.032 8.584)"/><g transform="translate(5.623 8.499)"><path class="bl" d="M11.391,5.162,8.909,6.6,0,1.442,2.481,0Z" transform="translate(0.002 0)"/><path class="bm" d="M2.484,0l0,.87L0,2.312l0-.87Z" transform="translate(8.909 5.162)"/><path class="bn" d="M8.912,5.162l0,.87L0,.87,0,0Z" transform="translate(0 1.442)"/></g><path class="bo" d="M20.056,14.887h1.676l3.35-1.793L2.4,0,0,.584Z" transform="translate(12.893 5.486)"/><g transform="translate(11.036 5.385)"><path class="bp" d="M24.395,12.676l-2.481,1.442L0,1.442,2.481,0Z" transform="translate(0.002 0)"/><path class="bq" d="M2.484,0l0,.87L0,2.312l0-.87Z" transform="translate(21.913 12.676)"/><path class="br" d="M21.916,12.676l0,.87L0,.87,0,0Z" transform="translate(0 1.442)"/></g><path class="bs" d="M2.389,4.915H4.317L7.27,3.5,1.352,0,0,.335Z" transform="translate(22.837 0.175)"/><g transform="translate(20.446)"><path class="bt" d="M7.262,2.778,4.78,4.22,0,1.442,2.481,0Z" transform="translate(0.003)"/><path class="bu" d="M2.484,0l0,.87L0,2.312l0-.87Z" transform="translate(4.781 2.778)"/><path class="bv" d="M4.783,2.778l0,.87L0,.87,0,0Z" transform="translate(0 1.442)"/></g><path class="bw" d="M14.563,11.61h1.676L19.59,9.817,2.4,0,0,.584Z" transform="translate(17.589 2.778)"/><g transform="translate(15.824 2.655)"><path class="bx" d="M18.8,9.421l-2.482,1.442L0,1.442,2.481,0Z" transform="translate(0.003)"/><path class="by" d="M2.484,0l0,.87L0,2.312l0-.87Z" transform="translate(16.318 9.421)"/><path class="bz" d="M16.32,9.421l0,.87L0,.87,0,0Z" transform="translate(0 1.442)"/></g></g></g><g transform="translate(21.566 28.615)"><g transform="translate(0 0)"><path class="ca" d="M4.1,0V6.607L0,8.99V2.384Z" transform="translate(17.781 1.744)"/><path class="cb" d="M0,11.028V4.422A3.809,3.809,0,0,1,2.228,1.3,12.117,12.117,0,0,1,12.015.789V7.395a12.117,12.117,0,0,0-9.786.513A3.81,3.81,0,0,0,0,11.028Z" transform="translate(5.766 3.339)"/><path class="cc" d="M0,6.606V0C0,1.755,1.009,3.513,3.04,4.95v6.606C1.009,10.119,0,8.361,0,6.606Z" transform="translate(0 7.745)"/><path class="cd" d="M21.883,1.744l-4.1,2.384a12.117,12.117,0,0,0-9.786.513c-2.653,1.542-2.936,3.936-.854,5.67l-4.1,2.383C-1.282,9.637-1,5.129,3.9,2.28S16.589-.748,21.883,1.744Z" transform="translate(0 0)"/><path class="ce" d="M4.1,0V6.606L0,8.989V2.383Z" transform="translate(3.04 10.312)"/></g><g transform="translate(4.994 4.961)"><path class="cf" d="M1.4,2.55,1.376,8.888A3.351,3.351,0,0,0,0,6.338L.024,0A3.35,3.35,0,0,1,1.4,2.55Z" transform="translate(14.717 2.384)"/><path class="cg" d="M21.881,0V4.2c-.006,1.979-1.307,3.956-3.905,5.466C13.074,12.512,5.292,12.691,0,10.2V6c5.292,2.493,13.074.225,17.976-2.624C20.575,1.866,21.876,1.979,21.881,0Z" transform="translate(0 4.95)"/><path class="ch" d="M18.843,0c4.32,3.059,4.036,7.566-.867,10.416S5.292,13.444,0,10.95L4.1,8.567a12.122,12.122,0,0,0,9.786-.513c2.656-1.543,2.938-3.938.854-5.67Z" transform="translate(0 0)"/></g></g><g transform="translate(43.132 50.469)"><g transform="translate(0 0)"><path class="ca" d="M2.674,0V4.305L0,5.859v-4.3Z" transform="translate(11.587 1.136)"/><path class="cb" d="M0,7.186v-4.3A2.482,2.482,0,0,1,1.452.848,7.9,7.9,0,0,1,7.829.514v4.3a7.9,7.9,0,0,0-6.377.334A2.482,2.482,0,0,0,0,7.186Z" transform="translate(3.757 2.176)"/><path class="cc" d="M0,4.3V0A4.05,4.05,0,0,0,1.981,3.225v4.3A4.051,4.051,0,0,1,0,4.3Z" transform="translate(0 5.047)"/><path class="cd" d="M14.26,1.136,11.587,2.69a7.9,7.9,0,0,0-6.377.334c-1.729,1-1.913,2.565-.556,3.695L1.981,8.272C-.835,6.28-.65,3.342,2.545,1.486A13.788,13.788,0,0,1,14.26,1.136Z" transform="translate(0)"/><path class="ce" d="M2.672,0V4.3L0,5.858v-4.3Z" transform="translate(1.981 6.719)"/></g><g transform="translate(3.254 3.233)"><path class="cf" d="M.912,1.662.9,5.792A2.184,2.184,0,0,0,0,4.13L.015,0A2.183,2.183,0,0,1,.912,1.662Z" transform="translate(9.59 1.554)"/><path class="cg" d="M14.259,0V2.735c0,1.29-.852,2.578-2.545,3.562A13.78,13.78,0,0,1,0,6.645V3.91c3.449,1.625,8.519.147,11.714-1.71C13.407,1.216,14.255,1.29,14.259,0Z" transform="translate(0 3.225)"/><path class="ch" d="M12.279,0c2.815,1.993,2.63,4.931-.565,6.787A13.78,13.78,0,0,1,0,7.136L2.672,5.583a7.9,7.9,0,0,0,6.377-.334c1.73-1.006,1.915-2.566.556-3.695Z" transform="translate(0 0)"/></g></g><g transform="translate(63.634 59.683)"><ellipse class="ci" cx="25.368" cy="6.377" rx="25.368" ry="6.377" transform="translate(0 37.818)"/><path class="cj" d="M10.487,2.353S5.8,16.927,5.573,20.85s0,22.756,0,22.756a7.628,7.628,0,0,1-4.26,0c-2.354-.672-1.345,2.018,1.457,2.578S9.5,47.978,9.5,46.969a52.071,52.071,0,0,0-.112-5.941c-.224-.9,3.475-17.824,3.475-17.824L20.077,9.3l2.534,32.733s-6.674-1.368-6.562-.134,6.926,3.927,8.7,3.59S27.207,26.68,27.32,22.532,29.359,0,29.359,0Z" transform="translate(3.562 0)"/></g><path class="ck" d="M0,0S5.6,1.9,8.744,11.321c2.412,7.232-1.573,18.16-3.141,17.936S0,0,0,0Z" transform="translate(91.489 21.458)"/><path class="az" d="M1.242,0S4.518,4.608,2.507,12.783C2.507,12.783-2.16,6.619,1.242,0Z" transform="translate(94.187 31.419)"/><path class="cl" d="M.221,2.139s-1.289,7.118,2.354,6.5a25.41,25.41,0,0,1,.5,3.755c-.056.953,4.876-1.121,4.932-2.746s.336-5.661-1.738-7.959S1.286-.663.221,2.139Z" transform="translate(79.217 12.255)"/><path class="cm" d="M6.343.571S3.615,1.034,2.381.232-.454.726.121,3.83,1.446,7.3,2.107,7.157A3.4,3.4,0,0,1,4.681,8.728c.418.811.626,1.51.826.221s1.108-1.383,1.461-.2.21,2.862,1.37,2.633,3.215.386,4.09-2.565S10.8.234,6.343.571Z" transform="translate(76.097 8.506)"/><path class="cl" d="M3.434,5.627a9.6,9.6,0,0,1-.262-1.56c-.109-1.046.538-1.945.4-2.046s-.563-.08-.91.377S1.353.123.859.005s.055,1.662.2,2.087S.207,2.81.019,4.064A3.576,3.576,0,0,0,1.133,6.59Z" transform="translate(55.183 23.665)"/><path class="cn" d="M3,7.679s4.858,7.535,7.8,7.255c2.866-.273,4.651-4.56,8.316-7.184C22.305,5.463,25.329,4.68,26.4,4A54.6,54.6,0,0,1,32.693.3C34.582-.538,36.6.471,37.945,2.1s4.505,31.5,2.712,36.432-4.169,2.13-11.978,4.409c-7.21,2.1-6.99.747-6.858-4.185s2.387-12.107,1.5-16.441-.177-8.355-3.12-6.294c-2.128,1.49-5.632,3.871-9.32,4.134C6.955,20.433,2.6,16.009,0,9.185Z" transform="translate(55.898 21.155)"/><path class="co" d="M6.129,0a16.727,16.727,0,0,1-4.6,1.738C-.118,1.875-.093,3.867.075,4.372S5.008,3.083,5.736,2.186,6.521.784,6.129,0Z" transform="translate(82.221 20.785)"/></g></g><g transform="translate(132.657 125.995)"><path class="cp" d="M268.165,77.828q.018-.884,0-1.768V61.987h-2.5C260.743,47.505,248.6,33.7,229.2,22.5,177-7.635,92.1-7.481,39.571,22.847,20.269,33.991,8.033,47.659,2.854,61.986H0V78.848H.018c.408,19.414,13.38,38.715,38.96,53.483,52.2,30.139,137.1,29.986,189.634-.343,25.525-14.737,38.707-33.887,39.528-53.14h.026v-1.02Z" transform="translate(0.001 23.831)"/><path class="cq" d="M229.206,22.5c52.2,30.139,51.936,79.157-.594,109.485s-137.432,30.482-189.634.343-51.936-79.157.594-109.485S177-7.635,229.206,22.5Z" transform="translate(0 8.18)"/><path class="cr" d="M242.282,51.749c0,27.974-30.7,49.553-105.977,49.553S0,78.625,0,50.651,61.026,0,136.3,0,242.282,23.775,242.282,51.749Z" transform="translate(32.655 44.334)"/><path class="cp" d="M221.741,64.354q.015-.731,0-1.462V51.256h-2.068c-4.069-11.975-14.111-23.389-30.148-32.647C146.361-6.313,76.157-6.186,32.72,18.891,16.759,28.106,6.642,39.408,2.36,51.256H0V65.2H.015C.352,81.252,11.079,97.212,32.23,109.423c43.165,24.921,113.369,24.795,156.806-.284C210.142,96.954,221.041,81.118,221.72,65.2h.022v-.844Z" transform="translate(22.114 12.942)"/><path class="cs" d="M189.527,18.608c43.165,24.921,42.946,65.454-.491,90.532s-113.64,25.2-156.806.284-42.946-65.454.491-90.532S146.363-6.313,189.527,18.608Z" transform="translate(22.111 0)"/><path class="ct" d="M-21.59-18.911l62.333,34.5a2.4,2.4,0,0,1,1.4,2.184v4.942a2.127,2.127,0,0,1-1.206,1.919C36.374,26.829,19.4,34.47,0,36.74" transform="translate(163.769 95.683)"/><path class="cu" d="M0,.982S6.14.758,13.047,0V4.209A96.106,96.106,0,0,1,0,5.112Z" transform="translate(138.626 131.576)"/><path class="cv" d="M0,1.929S8.085.93,13.047,0V4.209A116.823,116.823,0,0,1,0,6.059Z" transform="translate(154.409 129.471)"/><path class="cw" d="M51.342,0V6.312a1.792,1.792,0,0,1-1.129,1.665L1.154,27.465A1.831,1.831,0,0,0,0,29.167v3.555" transform="translate(55.756 77.63)"/><path class="cx" d="M0,0A74.656,74.656,0,0,0,9.049,6.313V11.5S1.789,6.8,0,4.664Z" transform="translate(42.341 105.265)"/><path class="cy" d="M0,0A68.8,68.8,0,0,0,11.955,1.86V5.717A48.965,48.965,0,0,1,0,3.784Z" transform="translate(93.453 166.575)"/><path class="cz" d="M0,0S5.236.568,12.2.626V4.482A70.132,70.132,0,0,1,0,3.953Z" transform="translate(107.104 168.494)"/><path class="cw" d="M16.466,37.5c0,.015,10.857.011,10.857.011v-6.72a2.58,2.58,0,0,0-.75-1.819L.839,6.154A2.491,2.491,0,0,1,0,4.29V1.509A1.508,1.508,0,0,1,1.716.015C6.877.736,22.707,2.693,35.791,1.639" transform="translate(102.834 133.268)"/></g><g transform="translate(294.484 0)"><path class="da" d="M0,50.749H27.827A24.571,24.571,0,0,0,40.22,47.394l36.6-20.349L31.853,0Z" transform="translate(47.102 45.638)"/><g transform="translate(0.001 50.516)"><path class="db" d="M48.371,26.872l2.175,18.518h0a4.305,4.305,0,0,1-4.357.014L2.016,19.694A4.058,4.058,0,0,1,0,16.188V0Z" transform="translate(0 0)"/></g><g transform="translate(48.372 51.967)"><path class="dc" d="M45.307,0V16.146a4.057,4.057,0,0,1-1.994,3.493L2.1,43.98a1.4,1.4,0,0,1-2.1-1.2V24.072Z" transform="translate(0 0)"/></g><path class="dd" d="M93.006,28.414,50.989,53.289a6.063,6.063,0,0,1-6.158.013L.919,27.557a1.86,1.86,0,0,1,.042-3.232L43.76.756a6.067,6.067,0,0,1,6.162.175L93.04,26.071A1.373,1.373,0,0,1,93.006,28.414Z" transform="translate(0 24.9)"/><g transform="translate(0.001 25.619)"><path class="db" d="M48.371,26.872l2.175,18.518h0a4.309,4.309,0,0,1-4.357.014L2.016,19.694A4.054,4.054,0,0,1,0,16.188V0Z" transform="translate(0 0)"/></g><g transform="translate(48.372 27.068)"><path class="dc" d="M45.307,0V16.146a4.057,4.057,0,0,1-1.994,3.493L2.1,43.98a1.4,1.4,0,0,1-2.1-1.2V24.072Z" transform="translate(0 0)"/></g><path class="dd" d="M93.006,28.415,50.989,53.29a6.067,6.067,0,0,1-6.158.013L.919,27.557a1.86,1.86,0,0,1,.042-3.232L43.76.756a6.063,6.063,0,0,1,6.162.175L93.04,26.071A1.375,1.375,0,0,1,93.006,28.415Z" transform="translate(0 0)"/><path class="de" d="M79.351,24.244,43.5,45.466a5.175,5.175,0,0,1-5.253.011L.784,23.511A1.587,1.587,0,0,1,.82,20.754L37.334.645a5.175,5.175,0,0,1,5.257.15L79.377,22.244A1.172,1.172,0,0,1,79.351,24.244Z" transform="translate(6.877 3.401)"/><path class="df" d="M0,.5,3.839,2.782c1.152.684,2.5,1.48,3.936,2.336A17.55,17.55,0,0,0,10.043,6.36a6.176,6.176,0,0,0,2.64.4,6.673,6.673,0,0,0,1.326-.253c.425-.14.817-.367,1.223-.544.767-.431,1.5-.891,2.22-1.316l3.926-2.352L25.208,0,21.354,2.254,17.4,4.565c-.724.417-1.464.87-2.233,1.292-.4.169-.787.386-1.2.52a6.736,6.736,0,0,1-1.3.233,6.026,6.026,0,0,1-2.586-.362A17.476,17.476,0,0,1,7.823,5.036L3.863,2.743C1.546,1.4,0,.5,0,.5Z" transform="translate(35.676 47.433)"/><path class="dg" d="M0,5.885A13.551,13.551,0,0,0,4.693,2.771c.665-.806.352-2.915-1.236-2.763A10.13,10.13,0,0,0,0,.988Z" transform="translate(52.893 54.239)"/><path class="dh" d="M.112,1.138V0s2.811,2.449,0,4.9Z" transform="translate(52.781 55.225)"/><path class="dg" d="M0,5.887A13.54,13.54,0,0,0,4.693,2.77c.665-.806.352-2.914-1.236-2.763A10.169,10.169,0,0,0,0,.988Z" transform="translate(52.893 60.212)"/><path class="di" d="M.112,1.138V0s2.811,2.449,0,4.9Z" transform="translate(52.781 61.201)"/><path class="dj" d="M5.022,5.887A13.54,13.54,0,0,1,.328,2.77C-.336,1.964-.024-.144,1.564.008a10.13,10.13,0,0,1,3.458.98Z" transform="translate(37.97 54.283)"/><path class="dk" d="M1.361,1.138V0s-2.811,2.449,0,4.9Z" transform="translate(41.63 55.27)"/><path class="dj" d="M5.022,5.885A13.551,13.551,0,0,1,.328,2.771C-.336,1.964-.024-.145,1.564.008a10.091,10.091,0,0,1,3.458.98Z" transform="translate(37.97 60.258)"/><path class="dl" d="M1.361,1.138V0s-2.811,2.449,0,4.9Z" transform="translate(41.63 61.244)"/><path class="az" d="M.631.023l1.9,1.11A6.067,6.067,0,0,0,8.684,1.12L10.576,0l.6.274L8.989,2.335l-4.542.684-2.2-.893L0,.288Z" transform="translate(42.316 62.409)"/><path class="ay" d="M11.171,0l-2.5,1.475a6.067,6.067,0,0,1-6.158.013L0,.016v1.9L2.515,3.39a6.067,6.067,0,0,0,6.158-.013l2.5-1.479Z" transform="translate(42.316 62.684)"/><path class="az" d="M.631.023l1.9,1.11A6.067,6.067,0,0,0,8.684,1.12L10.576,0l.6.274L8.989,2.335l-4.542.684-2.2-.893L0,.288Z" transform="translate(42.316 56.405)"/><path class="ay" d="M11.171,0l-2.5,1.475a6.067,6.067,0,0,1-6.158.013L0,.016v1.9L2.515,3.39a6.067,6.067,0,0,0,6.158-.013l2.5-1.479Z" transform="translate(42.316 56.68)"/><path class="dg" d="M0,5.887A13.54,13.54,0,0,0,4.693,2.77c.665-.806.352-2.914-1.236-2.763A10.169,10.169,0,0,0,0,.988Z" transform="translate(53.361 79.452)"/><path class="dm" d="M.112,1.138V0s2.811,2.449,0,4.9Z" transform="translate(53.249 80.44)"/><path class="dg" d="M0,5.885A13.551,13.551,0,0,0,4.693,2.771c.665-.806.352-2.915-1.236-2.763A10.13,10.13,0,0,0,0,.988Z" transform="translate(53.361 85.428)"/><path class="dn" d="M.112,1.138V0s2.811,2.449,0,4.9Z" transform="translate(53.249 86.415)"/><path class="dj" d="M5.022,5.885A13.551,13.551,0,0,1,.328,2.771C-.336,1.964-.024-.145,1.564.008a10.091,10.091,0,0,1,3.458.98Z" transform="translate(38.407 79.497)"/><path class="do" d="M1.361,1.138V0s-2.811,2.449,0,4.9Z" transform="translate(42.068 80.484)"/><path class="dj" d="M5.022,5.887A13.54,13.54,0,0,1,.328,2.77C-.336,1.964-.024-.144,1.564.008a10.13,10.13,0,0,1,3.458.98Z" transform="translate(38.407 85.47)"/><path class="dp" d="M1.361,1.138V0s-2.811,2.449,0,4.9Z" transform="translate(42.068 86.458)"/><path class="az" d="M.631.021l1.9,1.112A6.067,6.067,0,0,0,8.684,1.12L10.576,0l.6.274L8.989,2.336,4.447,3.02l-2.2-.893L0,.29Z" transform="translate(42.786 87.543)"/><path class="ay" d="M11.171,0l-2.5,1.475a6.063,6.063,0,0,1-6.158.013L0,.016v1.9L2.515,3.39a6.067,6.067,0,0,0,6.158-.013l2.5-1.479Z" transform="translate(42.786 87.817)"/><path class="az" d="M.631.021l1.9,1.112A6.067,6.067,0,0,0,8.684,1.12L10.576,0l.6.274L8.989,2.335l-4.542.684-2.2-.893L0,.288Z" transform="translate(42.786 81.54)"/><path class="ay" d="M11.171,0l-2.5,1.475a6.063,6.063,0,0,1-6.158.013L0,.016v1.9L2.515,3.39a6.067,6.067,0,0,0,6.158-.013l2.5-1.479Z" transform="translate(42.786 81.812)"/><path class="ay" d="M3.817,3.554c0,1.21-.854,1.7-1.908,1.089A4.2,4.2,0,0,1,0,1.351C0,.141.854-.346,1.908.262A4.2,4.2,0,0,1,3.817,3.554Z" transform="translate(14.334 43.255)"/><path class="dq" d="M3.817,3.555c0,1.21-.854,1.7-1.908,1.088A4.2,4.2,0,0,1,0,1.35C0,.142.854-.347,1.908.263A4.2,4.2,0,0,1,3.817,3.555Z" transform="translate(20.973 47.164)"/><path class="ay" d="M3.817,3.554c0,1.21-.854,1.7-1.908,1.089A4.207,4.207,0,0,1,0,1.351C0,.141.854-.346,1.908.262A4.2,4.2,0,0,1,3.817,3.554Z" transform="translate(27.55 51.02)"/><path class="ay" d="M12.05,2.924,0,9.881V6.957L12.05,0Z" transform="translate(67.568 65.807)"/><path class="ay" d="M12.05,2.924,0,9.879V6.957L12.05,0Z" transform="translate(67.568 71.384)"/><path class="dr" d="M2.33,2.924,0,1.345,2.33,0Z" transform="translate(77.288 65.807)"/><path class="ds" d="M2.33,2.924,0,1.345,2.33,0Z" transform="translate(77.288 71.384)"/><path class="ay" d="M12.051,2.924,0,9.879V6.957L12.051,0Z" transform="translate(67.112 41.428)"/><path class="ay" d="M12.051,2.924,0,9.881V6.959L12.051,0Z" transform="translate(67.112 47.004)"/><path class="dt" d="M2.33,2.924,0,1.345,2.33,0Z" transform="translate(76.833 41.428)"/><path class="du" d="M2.33,2.924,0,1.345,2.33,0Z" transform="translate(76.833 47.004)"/></g></g><g transform="translate(0.633)"><path class="dv" d="M58.445,0V49.641L0,86.948.6,34Z" transform="translate(78.842 160.881)"/><path class="dw" d="M0,0V49.641L61.157,88.749l.6-57.552Z" transform="translate(17.685 159.081)"/><path class="dx" d="M154.526,47.207,84.716,88.537a10.077,10.077,0,0,1-10.231.022L1.527,45.784a3.09,3.09,0,0,1,.069-5.37L72.7,1.255a10.078,10.078,0,0,1,10.238.292l71.638,41.768A2.281,2.281,0,0,1,154.526,47.207Z" transform="translate(0 118.897)"/><path class="dy" d="M0,64.794H35.529A31.355,31.355,0,0,0,51.35,60.51L98.082,34.53,40.668,0Z" transform="translate(77.822 132.184)"/><g transform="translate(17.685 138.414)"><path class="dz" d="M61.757,34.309,64.533,57.95h0a5.5,5.5,0,0,1-5.564.018L2.574,25.143A5.179,5.179,0,0,1,0,20.667V0Z" transform="translate(0 0)"/></g><g transform="translate(79.441 140.268)"><path class="ea" d="M57.846,0V20.614a5.181,5.181,0,0,1-2.545,4.46L2.688,56.15A1.782,1.782,0,0,1,0,54.617V30.733Z" transform="translate(0 0)"/></g><path class="eb" d="M118.744,36.277,65.1,68.036a7.743,7.743,0,0,1-7.862.016L1.174,35.183a2.375,2.375,0,0,1,.053-4.127L55.868.965a7.744,7.744,0,0,1,7.867.224l55.049,32.1A1.753,1.753,0,0,1,118.744,36.277Z" transform="translate(17.684 105.71)"/><g transform="translate(17.685 106.625)"><path class="ec" d="M61.757,34.309,64.533,57.95h0a5.5,5.5,0,0,1-5.564.018L2.574,25.143A5.179,5.179,0,0,1,0,20.667V0Z" transform="translate(0 0)"/></g><g transform="translate(79.441 108.479)"><path class="ed" d="M57.846,0V20.614a5.181,5.181,0,0,1-2.545,4.46L2.688,56.149A1.782,1.782,0,0,1,0,54.616V30.732Z" transform="translate(0 0)"/></g><path class="ee" d="M118.744,36.277,65.1,68.036a7.743,7.743,0,0,1-7.862.016L1.174,35.183a2.375,2.375,0,0,1,.053-4.127L55.868.965a7.744,7.744,0,0,1,7.867.224l55.049,32.1A1.753,1.753,0,0,1,118.744,36.277Z" transform="translate(17.684 73.92)"/><path class="ef" d="M101.309,30.95l-45.768,27.1a6.608,6.608,0,0,1-6.708.014L1,30.017A2.026,2.026,0,0,1,1.047,26.5L47.665.823a6.606,6.606,0,0,1,6.712.192L101.344,28.4A1.5,1.5,0,0,1,101.309,30.95Z" transform="translate(26.465 78.265)"/><path class="eg" d="M0,.644,4.945,3.482,10,6.42l1.407.818a8,8,0,0,0,1.478.737,7.672,7.672,0,0,0,3.3.479,7.521,7.521,0,0,0,3.187-.975l2.845-1.661,5.039-2.959L32.186,0,27.307,2.949,22.289,5.944,19.453,7.627A7.667,7.667,0,0,1,16.2,8.647a7.847,7.847,0,0,1-3.375-.514,8.206,8.206,0,0,1-1.5-.76l-1.4-.828L4.893,3.572Z" transform="translate(63.233 134.477)"/><path class="eh" d="M0,7.514S4,5.952,5.991,3.536c.849-1.03.45-3.72-1.577-3.526A12.9,12.9,0,0,0,0,1.262V7.514Z" transform="translate(85.214 143.168)"/><path class="az" d="M0,1.451V0S3.59,3.127,0,6.253Z" transform="translate(85.214 144.43)"/><path class="ei" d="M0,7.514S4,5.952,5.991,3.536c.849-1.03.45-3.72-1.577-3.526A12.9,12.9,0,0,0,0,1.262V7.514Z" transform="translate(85.214 150.796)"/><path class="az" d="M0,1.451V0S3.59,3.127,0,6.253Z" transform="translate(85.214 152.058)"/><path class="ej" d="M6.41,7.514s-4-1.563-5.991-3.978C-.43,2.506-.031-.184,2,.01A12.9,12.9,0,0,1,6.41,1.262V7.514Z" transform="translate(66.162 143.225)"/><path class="az" d="M1.6,1.451V0s-3.59,3.127,0,6.253Z" transform="translate(70.977 144.486)"/><path class="ek" d="M6.41,7.514s-4-1.563-5.991-3.978C-.43,2.506-.031-.184,2,.01A12.9,12.9,0,0,1,6.41,1.262V7.514Z" transform="translate(66.162 150.852)"/><path class="az" d="M1.6,1.451V0s-3.59,3.127,0,6.253Z" transform="translate(70.977 152.114)"/><path class="az" d="M.806.029l2.42,1.418a7.745,7.745,0,0,0,7.862-.016L13.5,0l.759.35L11.476,2.982l-5.8.874L2.864,2.716,0,.371Z" transform="translate(71.711 153.599)"/><path class="ay" d="M14.262,0,11.072,1.884A7.743,7.743,0,0,1,3.21,1.9L0,.02V2.444L3.21,4.326a7.745,7.745,0,0,0,7.862-.016l3.191-1.889Z" transform="translate(71.711 153.95)"/><path class="az" d="M.806.029l2.42,1.418a7.745,7.745,0,0,0,7.862-.016L13.5,0l.759.35L11.476,2.982l-5.8.874L2.864,2.716,0,.371Z" transform="translate(71.711 145.934)"/><path class="ay" d="M14.262,0,11.072,1.884A7.743,7.743,0,0,1,3.21,1.9L0,.02V2.444L3.21,4.326a7.745,7.745,0,0,0,7.862-.016l3.191-1.889Z" transform="translate(71.711 146.284)"/><path class="el" d="M0,7.514S4,5.952,5.991,3.536c.849-1.03.45-3.72-1.577-3.526A12.9,12.9,0,0,0,0,1.262V7.514Z" transform="translate(85.812 175.359)"/><path class="em" d="M0,7.514S4,5.952,5.991,3.536c.849-1.03.45-3.72-1.577-3.526A12.9,12.9,0,0,0,0,1.262V7.514Z" transform="translate(85.812 204.131)"/><path class="az" d="M0,1.451V0S3.59,3.127,0,6.253Z" transform="translate(85.812 176.62)"/><path class="en" d="M0,7.514S4,5.952,5.991,3.536c.849-1.03.45-3.72-1.577-3.526A12.9,12.9,0,0,0,0,1.262V7.514Z" transform="translate(85.812 182.987)"/><path class="eo" d="M0,7.514S4,5.952,5.991,3.536c.849-1.03.45-3.72-1.577-3.526A12.9,12.9,0,0,0,0,1.262V7.514Z" transform="translate(85.812 211.759)"/><path class="az" d="M0,1.451V0S3.59,3.127,0,6.253Z" transform="translate(85.812 184.248)"/><path class="ep" d="M6.41,7.514s-4-1.563-5.991-3.978C-.43,2.506-.031-.184,2,.01A12.9,12.9,0,0,1,6.41,1.262V7.514Z" transform="translate(66.722 175.416)"/><path class="eq" d="M6.41,7.514s-4-1.563-5.991-3.978C-.43,2.506-.031-.184,2,.01A12.9,12.9,0,0,1,6.41,1.262V7.514Z" transform="translate(66.722 204.188)"/><path class="az" d="M1.6,1.451V0s-3.59,3.127,0,6.253Z" transform="translate(71.537 176.677)"/><path class="er" d="M6.41,7.514s-4-1.563-5.991-3.978C-.43,2.506-.031-.184,2,.01A12.9,12.9,0,0,1,6.41,1.262V7.514Z" transform="translate(66.722 183.044)"/><path class="es" d="M6.41,7.514s-4-1.563-5.991-3.978C-.43,2.506-.031-.184,2,.01A12.9,12.9,0,0,1,6.41,1.262V7.514Z" transform="translate(66.722 211.816)"/><path class="az" d="M1.6,1.451V0s-3.59,3.127,0,6.253Z" transform="translate(71.537 184.305)"/><path class="az" d="M.805.029l2.42,1.418a7.745,7.745,0,0,0,7.862-.016L13.5,0l.759.35L11.476,2.982l-5.8.874L2.864,2.716,0,.371Z" transform="translate(72.311 185.687)"/><path class="ay" d="M14.262,0,11.072,1.884A7.743,7.743,0,0,1,3.21,1.9L0,.02V2.444L3.21,4.326a7.745,7.745,0,0,0,7.862-.016l3.191-1.889Z" transform="translate(72.311 186.038)"/><path class="et" d="M14.262,0,11.072,1.884A7.743,7.743,0,0,1,3.21,1.9L0,.02V2.444L3.21,4.326a7.745,7.745,0,0,0,7.862-.016l3.191-1.889Z" transform="translate(72.311 214.81)"/><path class="az" d="M.805.029l2.42,1.418a7.745,7.745,0,0,0,7.862-.016L13.5,0l.759.35L11.476,2.982l-5.8.874L2.864,2.716,0,.371Z" transform="translate(72.311 178.021)"/><path class="ay" d="M14.262,0,11.072,1.884A7.743,7.743,0,0,1,3.21,1.9L0,.02V2.444L3.21,4.326a7.745,7.745,0,0,0,7.862-.016l3.191-1.889Z" transform="translate(72.311 178.371)"/><path class="et" d="M14.262,0,11.072,1.884A7.743,7.743,0,0,1,3.21,1.9L0,.02V2.444L3.21,4.326a7.745,7.745,0,0,0,7.862-.016l3.191-1.889Z" transform="translate(72.311 207.144)"/><path class="eu" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.37,5.37,0,0,1,4.874,4.538Z" transform="translate(35.985 129.144)"/><path class="ee" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.37,5.37,0,0,1,4.874,4.538Z" transform="translate(44.459 134.134)"/><path class="ev" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.372,5.372,0,0,1,4.874,4.538Z" transform="translate(52.858 139.057)"/><path class="ew" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.37,5.37,0,0,1,4.874,4.538Z" transform="translate(34.82 159.683)"/><path class="ex" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.37,5.37,0,0,1,4.874,4.538Z" transform="translate(34.82 188.455)"/><path class="ey" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.37,5.37,0,0,1,4.874,4.538Z" transform="translate(43.295 193.446)"/><path class="ez" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.37,5.37,0,0,1,4.874,4.538Z" transform="translate(51.693 169.597)"/><path class="fa" d="M4.874,4.538c0,1.545-1.091,2.167-2.437,1.39A5.37,5.37,0,0,1,0,1.724C0,.18,1.091-.442,2.437.335A5.37,5.37,0,0,1,4.874,4.538Z" transform="translate(51.693 198.37)"/><path class="ay" d="M15.385,3.731,0,12.614V8.883L15.385,0Z" transform="translate(103.951 157.938)"/><path class="et" d="M15.385,3.731,0,12.614V8.883L15.385,0Z" transform="translate(103.951 186.711)"/><path class="ay" d="M15.385,3.731,0,12.613V8.882L15.385,0Z" transform="translate(103.951 165.058)"/><path class="et" d="M15.385,3.731,0,12.613V8.882L15.385,0Z" transform="translate(103.951 193.83)"/><path class="az" d="M2.974,3.731,0,1.717,2.974,0Z" transform="translate(116.362 157.938)"/><path class="az" d="M2.974,3.731,0,1.717,2.974,0Z" transform="translate(116.362 165.058)"/><path class="ay" d="M15.385,3.731,0,12.614V8.882L15.385,0Z" transform="translate(103.369 126.812)"/><path class="ay" d="M15.385,3.731,0,12.614V8.883L15.385,0Z" transform="translate(103.369 133.931)"/><path class="az" d="M2.974,3.731,0,1.717,2.974,0Z" transform="translate(115.78 126.812)"/><path class="az" d="M2.974,3.731,0,1.718,2.974,0Z" transform="translate(115.78 133.931)"/><path class="fb" d="M0,14.854,26.352,0,54.358,16.343,28,31.2Z" transform="translate(50.307 93.839)"/><path class="fc" d="M28.006,48.926,0,32.583.244,0,28.25,16.343Z" transform="translate(50.185 49.4)"/><g transform="translate(50.185 49.4)"><path class="b" d="M28.006,48.926,0,32.583.244,0,28.25,16.343Z" transform="translate(0 0)"/></g><path class="fe" d="M0,14.854,26.353,0l28,16.343L28.006,31.2Z" transform="translate(50.429 34.547)"/><path class="ff" d="M.244,14.854,26.6,0l-.244,32.583L0,47.437Z" transform="translate(78.191 50.89)"/><path class="fg" d="M101.307,109.159l-45.768,27.1a6.252,6.252,0,0,1-6.708.014L1,108.225a1.87,1.87,0,0,1-1-1.655L0,0,102.038,1.105l0,106.587A1.281,1.281,0,0,1,101.307,109.159Z" transform="translate(26.447 0)"/><ellipse class="fh" cx="1.296" cy="1.285" rx="1.296" ry="1.285" transform="translate(111.486 91.743)"/><ellipse class="fh" cx="1.296" cy="1.285" rx="1.296" ry="1.285" transform="translate(48.655 81.261)"/><ellipse class="fh" cx="1.296" cy="1.285" rx="1.296" ry="1.285" transform="translate(88.72 62.065)"/><ellipse class="fh" cx="1.296" cy="1.285" rx="1.296" ry="1.285" transform="translate(72.202 114.845)"/><ellipse class="fh" cx="1.296" cy="1.285" rx="1.296" ry="1.285" transform="translate(59.159 55.865)"/><ellipse class="fh" cx="1.296" cy="1.285" rx="1.296" ry="1.285" transform="translate(98.802 29.638)"/><ellipse class="fh" cx="1.296" cy="1.285" rx="1.296" ry="1.285" transform="translate(71.277 89.636)"/></g><g transform="translate(195.06 64.864)"><path class="ay" d="M0,.6l.97,37,10.054,5.408S16.348,9.642,14.141,7.162,8.568-2.034,0,.6Z" transform="translate(87.502 18.088)"/><g class="fn" transform="matrix(1, 0, 0, 1, -516.25, -333.99)"><path class="fi" d="M119.321,21.581,79.868.359S41.971-7.557,11.05,50.973c-24.863,47.062,0,76.365,0,76.365l36.7,20.447Z" transform="translate(516.25 384.69)"/></g><path class="fj" d="M11.165,85.145,0,79.358V57.715S1.353,6.951,47.119,0c0,0,13.527,1.128,19.115,10.143,0,0-34.413,7.893-41.982,19.841S11.165,85.145,11.165,85.145Z" transform="translate(35.41)"/><path class="fk" d="M13.268,46.248C-5.858,81.49-4.1,119.428,17.2,130.985s54.064-7.644,73.19-42.886,17.365-73.18-3.931-84.737S32.392,11.006,13.268,46.248Z" transform="translate(32.865 68.927)"/><path class="fl" d="M9.7,72.325,0,78.193V48.324C1.339,35.265,10.928,16.27,29.869,5.139c23.629-13.886,32.389,3.6,32.389,14.4V59.7L51.951,54.152l.231-32.1S48.224,6.219,33.469,13.776,9.5,43.15,9.448,45.536,9.7,72.325,9.7,72.325Z" transform="translate(46.575 6.952)"/><path class="fm" d="M4.607,22.12S-6.357,12.583,10.332.663c0,0,17.858-5.473,6.225,15.038L21.743,48.61,0,61.06Z" transform="translate(73.097 108.898)"/></g></g></g></svg>