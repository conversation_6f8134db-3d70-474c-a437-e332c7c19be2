<script lang="ts" setup>
import { RouterView } from "vue-router"
import { computed, onMounted, watch } from 'vue'
import SidebarComp from "../common/SidebarComp.vue"
import type { SideMenuItem } from "../common/type"
import { useUserStore } from '@/stores/userStore'
import { hasMenuPermission } from '@/utils/menuPermissions'
import { operationLogger } from '@/utils/operationLogger'

const userStore = useUserStore()

// 原始菜单配置
const originalMenuList: SideMenuItem[] = [
  // 运营管理 - 三级菜单结构
  {
    link: '',
    isActive: true,
    isCollapse: true,
    parent: '运营管理',
    children: [
      {
        isChildActive: true,
        name: '资产包管理',
        link: '/home/<USER>/assetPackage',
        icon: 'jt-20-asset-package',
      },
      {
        isChildActive: false,
        isChildCollapse: false,
        name: '调解管理',
        icon: 'jt-20-mediation',
        children: [
          {
            isGrandChildActive: false,
            name: '案件跟踪',
            link: '/home/<USER>/caseTracking',
            icon: 'jt-20-disposal',
          },
          {
            isGrandChildActive: false,
            name: '调解信息',
            link: '/home/<USER>/mediationInformation',
            icon: 'jt-20-debtor-evaluation',
          },
          {
            isGrandChildActive: false,
            name: '调解方案',
            link: '/home/<USER>/disposalPlan',
            icon: 'jt-20-disposal-plan',
          },
          {
            isGrandChildActive: false,
            name: '人员调度',
            link: '/home/<USER>/personnelDispatch',
            icon: 'jt-20-personnel-dispatch',
          },
        ]
      },
      {
        isChildActive: false,
        name: '债权人管理',
        link: '/home/<USER>/creditor',
        icon: 'jt-20-creditor',
      },
      {
        isChildActive: false,
        name: '债务人管理',
        link: '/home/<USER>/debtor',
        icon: 'jt-20-debtor',
      },
      {
        isChildActive: false,
        name: '诉前保全',
        link: '/home/<USER>/preAction',
        icon: 'jt-20-pre-action',
      },
      {
        isChildActive: false,
        name: '信息修复',
        link: '/home/<USER>/informationRepair',
        icon: 'jt-20-information-repair',
      },
      {
        isChildActive: false,
        name: '案例展示',
        link: '/home/<USER>/caseShow',
        icon: 'jt-20-creditor',
      },
      {
        isChildActive: false,
        name: '投诉建议',
        link: '/home/<USER>/complaintFeedback',
        icon: 'jt-20-complaint',
      },
      {
        isChildActive: false,
        isChildCollapse: false,
        name: '外呼管理',
        icon: 'jt-20-outbound-call',
        children: [
          {
            isGrandChildActive: false,
            name: '语音外呼记录',
            link: '/home/<USER>/outboundCall',
            icon: 'jt-20-outbound-voice-call',
          },
          {
            isGrandChildActive: false,
            name: '短信发送记录',
            link: '/home/<USER>/messageRecord',
            icon: 'jt-20-message',
          },
        ]
      },
    ] 
  },
  {
    link: '',
    isActive: false, 
    isCollapse: false,
    parent: '数据管理',
    children: [
      {
        isChildActive: false,
        isChildCollapse: false,
        name: '数据治理',
        icon: 'jt-20-data-governance',
        children: [
          {
            isGrandChildActive: false,
            name: '字段配置',
            link: '/home/<USER>/fieldConfiguration',
            icon: 'jt-20-outbound-voice-call',
          },
          {
            isGrandChildActive: false,
            name: '数据导入',
            link: '/home/<USER>/dataImport',
            icon: 'jt-20-message',
          },
        ]
      },
      {
        isChildActive: false,
        name: '数据分析',
        link: '/home/<USER>/dataAnalysis',
        icon: 'jt-20-data-analysis',
      },
    ] 
  },
]

// 基于用户权限过滤的菜单列表
const menuList = computed(() => {
  // 权限未加载时显示所有菜单，避免页面空白
  // 后续权限加载完成后会自动过滤
  if (!userStore.permissionsLoaded) {
    console.log('权限未加载，显示完整菜单')
    return originalMenuList
  }
  
  console.log('权限已加载，进行菜单过滤', userStore.userPermissions)
  
  return originalMenuList.map(parent => ({
    ...parent,
    children: parent.children.filter(child => {
      // 如果是二级菜单有链接，检查权限
      if (child.link) {
        return hasMenuPermission(userStore.userPermissions, child.link)
      }
      
      // 如果是有三级菜单的二级菜单，过滤三级菜单
      if (child.children) {
        const filteredGrandChildren = child.children.filter(grandChild =>
          hasMenuPermission(userStore.userPermissions, grandChild.link)
        )
        // 如果三级菜单全部被过滤掉，则隐藏二级菜单
        if (filteredGrandChildren.length === 0) {
          return false
        }
        // 更新子菜单
        child.children = filteredGrandChildren
        return true
      }
      
      return true
    })
  })).filter(parent => parent.children.length > 0) // 过滤掉没有子菜单的父级菜单
})

// 监听菜单列表变化，更新操作日志器
watch(menuList, (newMenuList) => {
  operationLogger.setMenuList(newMenuList)
}, { immediate: true })

// 不再在此处获取用户权限，由App.vue统一处理
// 避免重复调用getUserInfo接口
</script>

<template>
  <div style="display: flex;">
    <!-- 始终显示菜单，无感地等待权限加载 -->
    <SidebarComp :menu-list="menuList"></SidebarComp>
    <div class="router-wrap">
      <RouterView></RouterView>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.router-wrap {
  background-color: #f2f2f2;
  display: block;
  width: calc(100vw - 200px);
}
</style>