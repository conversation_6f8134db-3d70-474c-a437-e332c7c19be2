# 常用模式和最佳实践

- 删除确认弹框已封装为通用组件DeleteConfirmDialog.vue：1) 位置：src/components/common/dialog/DeleteConfirmDialog.vue；2) 支持自定义删除提示文本、确认按钮文本、取消按钮文本；3) 通过事件回调处理确认删除和取消操作；4) 支持loading状态和响应式设计；5) mediationInformation.vue已更新使用新组件替换DeleteTips；6) 组件设计适用于系统中所有需要删除确认功能的列表页面
- 调解案件审批功能已完成：1) ApprovalDialog.vue审批弹框支持调解案件号和方案名称回显；2) 审批结果单选框（通过/不通过）和审批意见多行文本框；3) 调用mediationPlanApprove接口传递id、approval_status、approval_comment参数；4) 基于用户部门和角色的权限控制系统，审批权限部门包括：审批部、法务部、管理层、主管部门；审批角色包括：审批员、审批主管、法务专员、部门经理、总监；5) 支持权限名称检查mediation_plan_approve或审批调解方案；6) 审批按钮仅在调解案件tab显示且用户有权限时可见
