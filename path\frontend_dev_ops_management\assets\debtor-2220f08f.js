/* empty css             */import{C as $e,c as Ue,h as xe}from"./headerCellStyle-17161c7c.js";/* empty css                      *//* empty css                 */import{a as ne,r as p,s as ye,o as r,q as x,w as s,J as ie,f as C,h as a,F as z,A as O,g as i,i as d,D as W,E as T,Q as re,S as ue,k as ge,j as be,a4 as Ve,a5 as Ce,l as we,M as de,p as ce,m as pe,af as me,z as Ee,I as Ae,n as fe,a7 as he,ag as Pe,a9 as ze,ah as Oe,ai as Te,K as je,L as Se,t as ae,aj as Ie,ad as Be,ak as De,N as qe}from"./index-8a4876d8.js";import{C as te}from"./CustomButton-ea16d5c5.js";/* empty css                     *//* empty css                  *//* empty css                 */import{_ as ke}from"./CustomDialog.vue_vue_type_style_index_0_lang-5a1ac16b.js";import{_ as _e}from"./_plugin-vue_export-helper-c27b6911.js";const K=E=>(ce("data-v-4b853cf9"),E=E(),pe(),E),Le={class:"add-creditor-form"},We={class:"contact-list"},Fe={class:"contact-input-group"},Ne=K(()=>i("i",{class:"el-icon-plus"},null,-1)),Re={class:"contact-list"},Ke={class:"contact-input-group"},Me=K(()=>i("i",{class:"el-icon-plus"},null,-1)),Je={class:"contact-list"},Qe={class:"contact-input-group"},Ge={class:"contact-controls"},He=K(()=>i("i",{class:"el-icon-plus"},null,-1)),Xe={class:"contact-list"},Ye={class:"contact-input-group"},Ze=K(()=>i("i",{class:"el-icon-plus"},null,-1)),el={class:"dialog-footer"},ll=K(()=>i("i",{class:"jt-20-ensure"},null,-1)),al=K(()=>i("i",{class:"jt-20-delete"},null,-1)),tl=ne({__name:"AddDebtor",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm"],setup(E,{emit:q}){const L=E,P=p(),A=p(!1),b=p({debtor_type:"",debtor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]}),v=p([{phone:"",phone_type:"",is_primary:!0}]),f=p([{email:"",email_type:"",is_primary:!0}]),h=p([{address:"",address_type:"",is_primary:!0}]),V=p([{wechat:"",wechat_type:"",is_primary:!0}]),I=p([]),F=p([]),j=p(!1);async function N(){j.value=!0;const{data:e}=await me(),{state:t,msg:u}=e;if(t==="success"){const{debtor_types:k,id_types:o}=e.data;if(e.data){const w=Object.entries(k).map(([g,S])=>({label:S,value:g}));I.value=w;const c=Object.entries(o).map(([g,S])=>({label:S,value:g}));F.value=c}}else T.error(u);j.value=!1}const J={debtor_type:[{required:!0,message:"请选择债权人类型",trigger:"change"}],debtor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};ye(()=>L.visible,async e=>{e&&(await N(),B())});function B(){b.value={debtor_name:"",debtor_type:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[]},v.value=[{phone:"",phone_type:"",is_primary:!0}],f.value=[{email:"",email_type:"",is_primary:!0}],h.value=[{address:"",address_type:"",is_primary:!0}],V.value=[{wechat:"",wechat_type:"",is_primary:!0}],P.value&&P.value.clearValidate()}function D(){q("update:visible")}function Q(){v.value.push({phone:"",phone_type:"",is_primary:!1})}function G(e){v.value.length>1&&(v.value.splice(e,1),!v.value.find(t=>t.is_primary)&&v.value.length>0&&(v.value[0].is_primary=!0))}function H(e){v.value.forEach((t,u)=>{t.is_primary=u===e})}function X(){f.value.push({email:"",email_type:"",is_primary:!1})}function Y(e){f.value.length>1&&(f.value.splice(e,1),!f.value.find(t=>t.is_primary)&&f.value.length>0&&(f.value[0].is_primary=!0))}function Z(e){f.value.forEach((t,u)=>{t.is_primary=u===e})}function ee(){h.value.push({address:"",address_type:"",is_primary:!1})}function le(e){h.value.length>1&&(h.value.splice(e,1),!h.value.find(t=>t.is_primary)&&h.value.length>0&&(h.value[0].is_primary=!0))}function R(e){h.value.forEach((t,u)=>{t.is_primary=u===e})}function m(){V.value.push({wechat:"",wechat_type:"",is_primary:!1})}function y(e){V.value.length>1&&(V.value.splice(e,1),!V.value.find(t=>t.is_primary)&&V.value.length>0&&(V.value[0].is_primary=!0))}function $(e){V.value.forEach((t,u)=>{t.is_primary=u===e})}async function U(){if(P.value){A.value=!0;try{if(!await new Promise(c=>{P.value.validate(g=>{c(g)})}))return;const t=v.value.filter(c=>c.phone.trim()),u=f.value.filter(c=>c.email.trim()),k=h.value.filter(c=>c.address.trim()),o=V.value.filter(c=>c.wechat.trim()),w={...b.value,phones:t.length>0?t:void 0,emails:u.length>0?u:void 0,addresses:k.length>0?k:void 0,wechats:o.length>0?o:void 0};q("confirm",w)}catch(e){console.error("表单验证失败:",e),T.error(e)}finally{A.value=!1}}}return(e,t)=>{const u=re,k=ue,o=ge,w=be,c=Ve,g=Ce,S=we,se=de;return r(),x(ke,{visible:L.visible,title:"新增债务人",width:"600px","onUpdate:visible":D},{default:s(()=>[ie((r(),C("div",Le,[a(S,{ref_key:"formRef",ref:P,model:b.value,rules:J,"label-width":"120px","label-position":"right"},{default:s(()=>[a(o,{label:"类型",prop:"debtor_type"},{default:s(()=>[a(k,{modelValue:b.value.debtor_type,"onUpdate:modelValue":t[0]||(t[0]=l=>b.value.debtor_type=l),placeholder:"请选择债权人类型",loading:j.value,style:{width:"100%"}},{default:s(()=>[(r(!0),C(z,null,O(I.value,l=>(r(),x(u,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(o,{label:"姓名",prop:"debtor_name"},{default:s(()=>[a(w,{modelValue:b.value.debtor_name,"onUpdate:modelValue":t[1]||(t[1]=l=>b.value.debtor_name=l),placeholder:"请输入债权人姓名"},null,8,["modelValue"])]),_:1}),a(o,{label:"证件类型",prop:"id_type"},{default:s(()=>[a(k,{modelValue:b.value.id_type,"onUpdate:modelValue":t[2]||(t[2]=l=>b.value.id_type=l),placeholder:"请选择证件类型",loading:j.value,style:{width:"100%"}},{default:s(()=>[(r(!0),C(z,null,O(F.value,l=>(r(),x(u,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(o,{label:"证件号码",prop:"id_number"},{default:s(()=>[a(w,{modelValue:b.value.id_number,"onUpdate:modelValue":t[3]||(t[3]=l=>b.value.id_number=l),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),a(o,{label:"联系电话"},{default:s(()=>[i("div",We,[(r(!0),C(z,null,O(v.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",Fe,[a(w,{modelValue:l.phone,"onUpdate:modelValue":n=>l.phone=n,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>H(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),v.value.length>1?(r(),x(g,{key:0,onClick:n=>G(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])]))),128)),a(g,{onClick:Q,type:"primary",text:"",size:"small"},{default:s(()=>[Ne,d(" 添加电话 ")]),_:1})])]),_:1}),a(o,{label:"联系邮箱"},{default:s(()=>[i("div",Re,[(r(!0),C(z,null,O(f.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",Ke,[a(w,{modelValue:l.email,"onUpdate:modelValue":n=>l.email=n,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>Z(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),f.value.length>1?(r(),x(g,{key:0,onClick:n=>Y(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])]))),128)),a(g,{onClick:X,type:"primary",text:"",size:"small"},{default:s(()=>[Me,d(" 添加邮箱 ")]),_:1})])]),_:1}),a(o,{label:"联系地址"},{default:s(()=>[i("div",Je,[(r(!0),C(z,null,O(h.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",Qe,[a(w,{modelValue:l.address,"onUpdate:modelValue":n=>l.address=n,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),i("div",Ge,[a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>R(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),h.value.length>1?(r(),x(g,{key:0,onClick:n=>le(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])])]))),128)),a(g,{onClick:ee,type:"primary",text:"",size:"small"},{default:s(()=>[He,d(" 添加地址 ")]),_:1})])]),_:1}),a(o,{label:"联系微信"},{default:s(()=>[i("div",Xe,[(r(!0),C(z,null,O(V.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",Ye,[a(w,{modelValue:l.wechat,"onUpdate:modelValue":n=>l.wechat=n,placeholder:"请输入联系微信",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>$(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),V.value.length>1?(r(),x(g,{key:0,onClick:n=>y(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])]))),128)),a(g,{onClick:m,type:"primary",text:"",size:"small"},{default:s(()=>[Ze,d(" 添加微信 ")]),_:1})])]),_:1})]),_:1},8,["model"]),i("div",el,[a(te,{onClick:U,loading:A.value,height:34,"btn-type":"blue"},{default:s(()=>[ll,d("确认 ")]),_:1},8,["loading"]),a(te,{onClick:D,height:34},{default:s(()=>[al,d("取消 ")]),_:1})])])),[[se,A.value]])]),_:1},8,["visible"])}}});const sl=_e(tl,[["__scopeId","data-v-4b853cf9"]]),M=E=>(ce("data-v-62743bdf"),E=E(),pe(),E),ol={class:"edit-creditor-form"},nl={class:"contact-list"},il={class:"contact-input-group"},rl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),ul={class:"contact-list"},dl={class:"contact-input-group"},cl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),pl={class:"contact-list"},ml={class:"contact-input-group"},_l={class:"contact-controls"},vl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),fl={class:"contact-list"},hl={class:"contact-input-group"},yl=M(()=>i("i",{class:"el-icon-plus"},null,-1)),gl={class:"dialog-footer"},bl=M(()=>i("i",{class:"jt-20-ensure"},null,-1)),Vl=M(()=>i("i",{class:"jt-20-delete"},null,-1)),Cl=ne({__name:"EditDebtor",props:{visible:{type:Boolean,default:!1},creditorData:{default:null}},emits:["update:visible","confirm"],setup(E,{emit:q}){const L=E,P=p(),A=p(!1),b=p({id:0,debtor_type:"",debtor_name:"",id_type:"",id_number:"",phones:[],emails:[],addresses:[],wechat:[]}),v=p([{phone:"",phone_type:"",is_primary:!0}]),f=p([{email:"",email_type:"",is_primary:!0}]),h=p([{address:"",address_type:"",is_primary:!0}]),V=p([{wechat:"",is_primary:!0}]),I=p([]),F=p([]),j=p(!1);async function N(){j.value=!0;const{data:e}=await me(),{state:t,msg:u}=e;if(t==="success"){const{debtor_types:k,id_types:o}=e.data;if(e.data){const w=Object.entries(k).map(([g,S])=>({label:S,value:g}));I.value=w;const c=Object.entries(o).map(([g,S])=>({label:S,value:g}));F.value=c}}else T.error(u);j.value=!1}const J={debtor_type:[{required:!0,message:"请选择债权人类型",trigger:"change"}],debtor_name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:100,message:"姓名长度在2到100个字符",trigger:"blur"}],id_type:[{required:!0,message:"请选择证件类型",trigger:"change"}],id_number:[{required:!0,message:"请输入证件号码",trigger:"blur"}]};ye(()=>[L.visible,L.creditorData],async([e,t])=>{e&&t&&typeof t=="object"&&(await N(),B(t))},{immediate:!0});function B(e){b.value={id:e.id,debtor_type:e.debtor_type,debtor_name:e.debtor_name,id_type:e.id_type,id_number:e.id_number,phones:e.phones||[],emails:e.emails||[],addresses:e.addresses||[]},e.phones&&e.phones.length>0?v.value=[...e.phones]:e.phone?v.value=[{phone:e.phone,phone_type:"",is_primary:!0}]:v.value=[{phone:"",phone_type:"",is_primary:!0}],e.emails&&e.emails.length>0?f.value=[...e.emails]:e.email?f.value=[{email:e.email,email_type:"",is_primary:!0}]:f.value=[{email:"",email_type:"",is_primary:!0}],e.addresses&&e.addresses.length>0?h.value=[...e.addresses]:e.address?h.value=[{address:e.address,address_type:"",is_primary:!0}]:h.value=[{address:"",address_type:"",is_primary:!0}],e.wechat&&e.wechat.length>0?V.value=[...e.wechat]:V.value=[{wechat:"",is_primary:!0}],P.value&&P.value.clearValidate()}function D(){q("update:visible")}function Q(){v.value.push({phone:"",phone_type:"",is_primary:!1})}function G(e){v.value.length>1&&(v.value.splice(e,1),!v.value.find(t=>t.is_primary)&&v.value.length>0&&(v.value[0].is_primary=!0))}function H(e){v.value.forEach((t,u)=>{t.is_primary=u===e})}function X(){f.value.push({email:"",email_type:"",is_primary:!1})}function Y(e){f.value.length>1&&(f.value.splice(e,1),!f.value.find(t=>t.is_primary)&&f.value.length>0&&(f.value[0].is_primary=!0))}function Z(e){f.value.forEach((t,u)=>{t.is_primary=u===e})}function ee(){h.value.push({address:"",address_type:"",is_primary:!1})}function le(e){h.value.length>1&&(h.value.splice(e,1),!h.value.find(t=>t.is_primary)&&h.value.length>0&&(h.value[0].is_primary=!0))}function R(e){h.value.forEach((t,u)=>{t.is_primary=u===e})}function m(){V.value.push({wechat:"",is_primary:!1})}function y(e){V.value.length>1&&V.value.splice(e,1)}function $(e){V.value.forEach((t,u)=>{t.is_primary=u===e})}async function U(){if(P.value){A.value=!0;try{if(!await new Promise(c=>{P.value.validate(g=>{c(g)})}))return;const t=v.value.filter(c=>c.phone.trim()),u=f.value.filter(c=>c.email.trim()),k=h.value.filter(c=>c.address.trim()),o=V.value.filter(c=>c.wechat.trim()),w={...b.value,phones:t.length>0?t:void 0,emails:u.length>0?u:void 0,addresses:k.length>0?k:void 0,wechats:o.length>0?o:void 0};q("confirm",w)}catch(e){console.error("表单验证失败:",e),T.error(e)}finally{A.value=!1}}}return(e,t)=>{const u=re,k=ue,o=ge,w=be,c=Ve,g=Ce,S=we,se=de;return r(),x(ke,{visible:L.visible,title:"编辑债务人",width:"600px","onUpdate:visible":D},{default:s(()=>[ie((r(),C("div",ol,[a(S,{ref_key:"formRef",ref:P,model:b.value,rules:J,"label-width":"120px","label-position":"right"},{default:s(()=>[a(o,{label:"类型",prop:"debtor_type"},{default:s(()=>[a(k,{modelValue:b.value.debtor_type,"onUpdate:modelValue":t[0]||(t[0]=l=>b.value.debtor_type=l),placeholder:"请选择债权人类型",loading:j.value,style:{width:"100%"}},{default:s(()=>[(r(!0),C(z,null,O(I.value,l=>(r(),x(u,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(o,{label:"姓名",prop:"debtor_name"},{default:s(()=>[a(w,{modelValue:b.value.debtor_name,"onUpdate:modelValue":t[1]||(t[1]=l=>b.value.debtor_name=l),placeholder:"请输入债权人姓名"},null,8,["modelValue"])]),_:1}),a(o,{label:"证件类型",prop:"id_type"},{default:s(()=>[a(k,{modelValue:b.value.id_type,"onUpdate:modelValue":t[2]||(t[2]=l=>b.value.id_type=l),placeholder:"请选择证件类型",loading:j.value,style:{width:"100%"}},{default:s(()=>[(r(!0),C(z,null,O(F.value,l=>(r(),x(u,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),a(o,{label:"证件号码",prop:"id_number"},{default:s(()=>[a(w,{modelValue:b.value.id_number,"onUpdate:modelValue":t[3]||(t[3]=l=>b.value.id_number=l),placeholder:"请输入证件号码",maxlength:"20"},null,8,["modelValue"])]),_:1}),a(o,{label:"联系电话"},{default:s(()=>[i("div",nl,[(r(!0),C(z,null,O(v.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",il,[a(w,{modelValue:l.phone,"onUpdate:modelValue":n=>l.phone=n,placeholder:"请输入联系电话",maxlength:"11",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>H(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),v.value.length>1?(r(),x(g,{key:0,onClick:n=>G(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])]))),128)),a(g,{onClick:Q,type:"primary",text:"",size:"small"},{default:s(()=>[rl,d(" 添加电话 ")]),_:1})])]),_:1}),a(o,{label:"联系邮箱"},{default:s(()=>[i("div",ul,[(r(!0),C(z,null,O(f.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",dl,[a(w,{modelValue:l.email,"onUpdate:modelValue":n=>l.email=n,placeholder:"请输入联系邮箱",maxlength:"50",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>Z(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),f.value.length>1?(r(),x(g,{key:0,onClick:n=>Y(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])]))),128)),a(g,{onClick:X,type:"primary",text:"",size:"small"},{default:s(()=>[cl,d(" 添加邮箱 ")]),_:1})])]),_:1}),a(o,{label:"联系地址"},{default:s(()=>[i("div",pl,[(r(!0),C(z,null,O(h.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",ml,[a(w,{modelValue:l.address,"onUpdate:modelValue":n=>l.address=n,type:"textarea",rows:2,placeholder:"请输入联系地址",maxlength:"200",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),i("div",_l,[a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>R(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),h.value.length>1?(r(),x(g,{key:0,onClick:n=>le(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])])]))),128)),a(g,{onClick:ee,type:"primary",text:"",size:"small"},{default:s(()=>[vl,d(" 添加地址 ")]),_:1})])]),_:1}),a(o,{label:"微信号"},{default:s(()=>[i("div",fl,[(r(!0),C(z,null,O(V.value,(l,_)=>(r(),C("div",{key:_,class:"contact-item"},[i("div",hl,[a(w,{modelValue:l.wechat,"onUpdate:modelValue":n=>l.wechat=n,placeholder:"请输入微信号",maxlength:"20",style:{flex:"1"}},null,8,["modelValue","onUpdate:modelValue"]),a(c,{modelValue:l.is_primary,"onUpdate:modelValue":n=>l.is_primary=n,label:!0,onChange:n=>$(_),class:"primary-radio"},{default:s(()=>[d("主要")]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),V.value.length>1?(r(),x(g,{key:0,onClick:n=>y(_),type:"danger",text:"",size:"small"},{default:s(()=>[d("删除")]),_:2},1032,["onClick"])):W("",!0)])]))),128)),a(g,{onClick:m,type:"primary",text:"",size:"small"},{default:s(()=>[yl,d(" 添加微信号 ")]),_:1})])]),_:1})]),_:1},8,["model"]),i("div",gl,[a(te,{onClick:U,loading:A.value,height:34,"btn-type":"blue"},{default:s(()=>[bl,d("确认 ")]),_:1},8,["loading"]),a(te,{onClick:D,height:34},{default:s(()=>[Vl,d("取消 ")]),_:1})])])),[[se,A.value]])]),_:1},8,["visible"])}}});const wl=_e(Cl,[["__scopeId","data-v-62743bdf"]]),ve=E=>(ce("data-v-bfec2144"),E=E(),pe(),E),kl={class:"creditor-management-page"},$l={class:"search-header"},Ul={class:"search-row"},xl={class:"search-item"},El={class:"search-item"},Al=ve(()=>i("label",null,"债务人类型",-1)),Pl={class:"search-item"},zl=ve(()=>i("label",null,"证件类型",-1)),Ol={class:"search-item"},Tl=ve(()=>i("i",{class:"jt-20-add"},null,-1)),jl={class:"table-container"},Sl={class:"operation-buttons"},Il=["onClick"],Bl=["onClick"],Dl={key:0,class:"pagination-wrapper"},oe=10,ql=ne({__name:"debtor",setup(E){const q=p(!1),L=p([]),P=p(0),A=p(1),b=p(""),v=p(""),f=p(""),h=p(!1),V=p(!1),I=p(null),F=p([]),j=p([]),N=p(!1);async function J(){N.value=!0;const{data:m}=await me(),{state:y,msg:$}=m;if(y==="success"){console.log(m.data,"data.data");const{debtor_types:U,id_types:e}=m.data;if(m.data){const t=Object.entries(U).map(([k,o])=>({label:o,value:k}));F.value=t;const u=Object.entries(e).map(([k,o])=>({label:o,value:k}));j.value=u}}else T.error($);N.value=!1}async function B(){if(!he()){T.error("登录状态已失效，请重新登录");return}q.value=!0;const m={page:A.value,page_size:oe};b.value.trim()&&(m.search=b.value.trim()),v.value&&(m.debtor_type=v.value),f.value&&(m.id_type=f.value);const{data:y}=await Pe(m),{state:$,msg:U}=y;$==="success"?(L.value=y.data.results,P.value=y.data.count):T.error(U),q.value=!1}function D(){ze("搜索","债务人管理"),A.value=1,B()}function Q(m){A.value=m,B()}function G(){h.value=!0}function H(){h.value=!1}async function X(m){const{data:y}=await Oe(m),{state:$,msg:U}=y;$==="success"&&(T.success(U),h.value=!1,A.value=1,B())}async function Y(m){const{data:y}=await Ie(m.id),{state:$,msg:U}=y;$==="success"?(console.log(y.data,"data.data"),I.value=y.data,V.value=!0):T.error(U)}function Z(){V.value=!1,I.value=null}async function ee(m){const{data:y}=await Te(m,m.id),{state:$,msg:U}=y;$==="success"&&(T.success(U),V.value=!1,I.value=null,B())}async function le(m){try{if(!he()){T.error("登录状态已失效，请重新登录");return}await Be.confirm(`确定要删除债务人"${m.debtor_name}"吗？`,"删除确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}),await De(m.id),T.success("删除成功"),B()}catch(y){T.error(y)}}function R(m,y){if(!m||m.length===0)return"";const $=m.find(U=>U.is_primary);return $?$[y]||"":m[0][y]}return Ee(async()=>{await J(),await B()}),(m,y)=>{const $=re,U=ue,e=qe,t=je,u=Se,k=de;return r(),C("div",kl,[i("div",$l,[i("div",Ul,[i("div",xl,[a($e,{modelValue:b.value,"onUpdate:modelValue":y[0]||(y[0]=o=>b.value=o),placeholder:"搜索姓名、证件号码",onKeyup:Ae(D,["enter"]),onClick:D},null,8,["modelValue","onKeyup"])]),i("div",El,[Al,a(U,{modelValue:v.value,"onUpdate:modelValue":y[1]||(y[1]=o=>v.value=o),placeholder:"债务人类型",clearable:"",onChange:D,loading:N.value,style:{width:"200px"}},{default:s(()=>[(r(!0),C(z,null,O(F.value,o=>(r(),x($,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),i("div",Pl,[zl,a(U,{modelValue:f.value,"onUpdate:modelValue":y[2]||(y[2]=o=>f.value=o),placeholder:"证件类型",clearable:"",onChange:D,loading:N.value,style:{width:"140px"}},{default:s(()=>[(r(!0),C(z,null,O(j.value,o=>(r(),x($,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),i("div",Ol,[a(te,{onClick:G,height:34},{default:s(()=>[Tl,d("新增债务人 ")]),_:1})])])]),i("div",jl,[ie((r(),x(t,{data:L.value,border:"",style:{width:"100%"},"cell-style":fe(Ue),"header-cell-style":fe(xe)},{default:s(()=>[a(e,{type:"index",label:"序号",width:"80",align:"center"},{default:s(({$index:o})=>[d(ae(oe*(A.value-1)+o+1),1)]),_:1}),a(e,{label:"类型",width:"140",align:"center",prop:"debtor_type_cn"}),a(e,{align:"center",prop:"debtor_name",label:"姓名","min-width":"200"}),a(e,{label:"证件类型",width:"120",align:"center",prop:"id_type_cn"}),a(e,{align:"center",prop:"id_number",label:"证件号码","min-width":"200"}),a(e,{align:"center",label:"联系电话",width:"130"},{default:s(({row:o})=>[d(ae(R(o.phones,"phone")),1)]),_:1}),a(e,{align:"center",label:"联系邮箱",width:"180"},{default:s(({row:o})=>[d(ae(R(o.emails,"email")),1)]),_:1}),a(e,{align:"center",label:"联系地址","min-width":"200"},{default:s(({row:o})=>[d(ae(R(o.addresses,"address")),1)]),_:1}),a(e,{align:"center",label:"联系微信","min-width":"200"},{default:s(({row:o})=>[d(ae(R(o.wechats,"wechat")),1)]),_:1}),a(e,{label:"操作",width:"180",align:"center",fixed:"right"},{default:s(({row:o})=>[i("div",Sl,[i("div",{onClick:w=>Y(o),class:"operation-btn edit-btn"},"编辑",8,Il),i("div",{onClick:w=>le(o),class:"operation-btn delete-btn"},"删除",8,Bl)])]),_:1})]),_:1},8,["data","cell-style","header-cell-style"])),[[k,q.value]]),P.value>0?(r(),C("div",Dl,[a(u,{background:"",layout:"prev, pager, next",total:P.value,"current-page":A.value,"page-size":oe,onCurrentChange:Q},null,8,["total","current-page"])])):W("",!0)]),a(sl,{visible:h.value,"onUpdate:visible":H,onConfirm:X},null,8,["visible"]),a(wl,{visible:V.value,"creditor-data":I.value,"onUpdate:visible":Z,onConfirm:ee},null,8,["visible","creditor-data"])])}}});const Xl=_e(ql,[["__scopeId","data-v-bfec2144"]]);export{Xl as default};
